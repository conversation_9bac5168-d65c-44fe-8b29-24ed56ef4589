{"version": 3, "sources": ["../../../../../lodash/_assignValue.js"], "sourcesContent": ["var baseAssignValue = require('./_baseAssignValue'),\n    eq = require('./eq');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Assigns `value` to `key` of `object` if the existing value is not equivalent\n * using [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction assignValue(object, key, value) {\n  var objValue = object[key];\n  if (!(hasOwnProperty.call(object, key) && eq(objValue, value)) ||\n      (value === undefined && !(key in object))) {\n    baseAssignValue(object, key, value);\n  }\n}\n\nmodule.exports = assignValue;\n"], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,kBAAkB;AAAtB,QACI,KAAK;AAGT,QAAI,cAAc,OAAO;AAGzB,QAAI,iBAAiB,YAAY;AAYjC,aAAS,YAAY,QAAQ,KAAK,OAAO;AACvC,UAAI,WAAW,OAAO,GAAG;AACzB,UAAI,EAAE,eAAe,KAAK,QAAQ,GAAG,KAAK,GAAG,UAAU,KAAK,MACvD,UAAU,UAAa,EAAE,OAAO,SAAU;AAC7C,wBAAgB,QAAQ,KAAK,KAAK;AAAA,MACpC;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}