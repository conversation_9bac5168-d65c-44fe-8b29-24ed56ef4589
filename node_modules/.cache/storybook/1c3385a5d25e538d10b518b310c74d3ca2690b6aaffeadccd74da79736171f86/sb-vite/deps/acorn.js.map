{"version": 3, "sources": ["../../../../../acorn/dist/acorn.mjs"], "sourcesContent": ["// This file was generated. Do not modify manually!\nvar astralIdentifierCodes = [509, 0, 227, 0, 150, 4, 294, 9, 1368, 2, 2, 1, 6, 3, 41, 2, 5, 0, 166, 1, 574, 3, 9, 9, 7, 9, 32, 4, 318, 1, 80, 3, 71, 10, 50, 3, 123, 2, 54, 14, 32, 10, 3, 1, 11, 3, 46, 10, 8, 0, 46, 9, 7, 2, 37, 13, 2, 9, 6, 1, 45, 0, 13, 2, 49, 13, 9, 3, 2, 11, 83, 11, 7, 0, 3, 0, 158, 11, 6, 9, 7, 3, 56, 1, 2, 6, 3, 1, 3, 2, 10, 0, 11, 1, 3, 6, 4, 4, 68, 8, 2, 0, 3, 0, 2, 3, 2, 4, 2, 0, 15, 1, 83, 17, 10, 9, 5, 0, 82, 19, 13, 9, 214, 6, 3, 8, 28, 1, 83, 16, 16, 9, 82, 12, 9, 9, 7, 19, 58, 14, 5, 9, 243, 14, 166, 9, 71, 5, 2, 1, 3, 3, 2, 0, 2, 1, 13, 9, 120, 6, 3, 6, 4, 0, 29, 9, 41, 6, 2, 3, 9, 0, 10, 10, 47, 15, 343, 9, 54, 7, 2, 7, 17, 9, 57, 21, 2, 13, 123, 5, 4, 0, 2, 1, 2, 6, 2, 0, 9, 9, 49, 4, 2, 1, 2, 4, 9, 9, 330, 3, 10, 1, 2, 0, 49, 6, 4, 4, 14, 10, 5350, 0, 7, 14, 11465, 27, 2343, 9, 87, 9, 39, 4, 60, 6, 26, 9, 535, 9, 470, 0, 2, 54, 8, 3, 82, 0, 12, 1, 19628, 1, 4178, 9, 519, 45, 3, 22, 543, 4, 4, 5, 9, 7, 3, 6, 31, 3, 149, 2, 1418, 49, 513, 54, 5, 49, 9, 0, 15, 0, 23, 4, 2, 14, 1361, 6, 2, 16, 3, 6, 2, 1, 2, 4, 101, 0, 161, 6, 10, 9, 357, 0, 62, 13, 499, 13, 245, 1, 2, 9, 726, 6, 110, 6, 6, 9, 4759, 9, 787719, 239];\n\n// This file was generated. Do not modify manually!\nvar astralIdentifierStartCodes = [0, 11, 2, 25, 2, 18, 2, 1, 2, 14, 3, 13, 35, 122, 70, 52, 268, 28, 4, 48, 48, 31, 14, 29, 6, 37, 11, 29, 3, 35, 5, 7, 2, 4, 43, 157, 19, 35, 5, 35, 5, 39, 9, 51, 13, 10, 2, 14, 2, 6, 2, 1, 2, 10, 2, 14, 2, 6, 2, 1, 4, 51, 13, 310, 10, 21, 11, 7, 25, 5, 2, 41, 2, 8, 70, 5, 3, 0, 2, 43, 2, 1, 4, 0, 3, 22, 11, 22, 10, 30, 66, 18, 2, 1, 11, 21, 11, 25, 71, 55, 7, 1, 65, 0, 16, 3, 2, 2, 2, 28, 43, 28, 4, 28, 36, 7, 2, 27, 28, 53, 11, 21, 11, 18, 14, 17, 111, 72, 56, 50, 14, 50, 14, 35, 39, 27, 10, 22, 251, 41, 7, 1, 17, 2, 60, 28, 11, 0, 9, 21, 43, 17, 47, 20, 28, 22, 13, 52, 58, 1, 3, 0, 14, 44, 33, 24, 27, 35, 30, 0, 3, 0, 9, 34, 4, 0, 13, 47, 15, 3, 22, 0, 2, 0, 36, 17, 2, 24, 20, 1, 64, 6, 2, 0, 2, 3, 2, 14, 2, 9, 8, 46, 39, 7, 3, 1, 3, 21, 2, 6, 2, 1, 2, 4, 4, 0, 19, 0, 13, 4, 31, 9, 2, 0, 3, 0, 2, 37, 2, 0, 26, 0, 2, 0, 45, 52, 19, 3, 21, 2, 31, 47, 21, 1, 2, 0, 185, 46, 42, 3, 37, 47, 21, 0, 60, 42, 14, 0, 72, 26, 38, 6, 186, 43, 117, 63, 32, 7, 3, 0, 3, 7, 2, 1, 2, 23, 16, 0, 2, 0, 95, 7, 3, 38, 17, 0, 2, 0, 29, 0, 11, 39, 8, 0, 22, 0, 12, 45, 20, 0, 19, 72, 200, 32, 32, 8, 2, 36, 18, 0, 50, 29, 113, 6, 2, 1, 2, 37, 22, 0, 26, 5, 2, 1, 2, 31, 15, 0, 328, 18, 16, 0, 2, 12, 2, 33, 125, 0, 80, 921, 103, 110, 18, 195, 2637, 96, 16, 1071, 18, 5, 26, 3994, 6, 582, 6842, 29, 1763, 568, 8, 30, 18, 78, 18, 29, 19, 47, 17, 3, 32, 20, 6, 18, 433, 44, 212, 63, 129, 74, 6, 0, 67, 12, 65, 1, 2, 0, 29, 6135, 9, 1237, 42, 9, 8936, 3, 2, 6, 2, 1, 2, 290, 16, 0, 30, 2, 3, 0, 15, 3, 9, 395, 2309, 106, 6, 12, 4, 8, 8, 9, 5991, 84, 2, 70, 2, 1, 3, 0, 3, 1, 3, 3, 2, 11, 2, 0, 2, 6, 2, 64, 2, 3, 3, 7, 2, 6, 2, 27, 2, 3, 2, 4, 2, 0, 4, 6, 2, 339, 3, 24, 2, 24, 2, 30, 2, 24, 2, 30, 2, 24, 2, 30, 2, 24, 2, 30, 2, 24, 2, 7, 1845, 30, 7, 5, 262, 61, 147, 44, 11, 6, 17, 0, 322, 29, 19, 43, 485, 27, 229, 29, 3, 0, 496, 6, 2, 3, 2, 1, 2, 14, 2, 196, 60, 67, 8, 0, 1205, 3, 2, 26, 2, 1, 2, 0, 3, 0, 2, 9, 2, 3, 2, 0, 2, 0, 7, 0, 5, 0, 2, 0, 2, 0, 2, 2, 2, 1, 2, 0, 3, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 1, 2, 0, 3, 3, 2, 6, 2, 3, 2, 3, 2, 0, 2, 9, 2, 16, 6, 2, 2, 4, 2, 16, 4421, 42719, 33, 4153, 7, 221, 3, 5761, 15, 7472, 16, 621, 2467, 541, 1507, 4938, 6, 4191];\n\n// This file was generated. Do not modify manually!\nvar nonASCIIidentifierChars = \"\\u200c\\u200d\\xb7\\u0300-\\u036f\\u0387\\u0483-\\u0487\\u0591-\\u05bd\\u05bf\\u05c1\\u05c2\\u05c4\\u05c5\\u05c7\\u0610-\\u061a\\u064b-\\u0669\\u0670\\u06d6-\\u06dc\\u06df-\\u06e4\\u06e7\\u06e8\\u06ea-\\u06ed\\u06f0-\\u06f9\\u0711\\u0730-\\u074a\\u07a6-\\u07b0\\u07c0-\\u07c9\\u07eb-\\u07f3\\u07fd\\u0816-\\u0819\\u081b-\\u0823\\u0825-\\u0827\\u0829-\\u082d\\u0859-\\u085b\\u0897-\\u089f\\u08ca-\\u08e1\\u08e3-\\u0903\\u093a-\\u093c\\u093e-\\u094f\\u0951-\\u0957\\u0962\\u0963\\u0966-\\u096f\\u0981-\\u0983\\u09bc\\u09be-\\u09c4\\u09c7\\u09c8\\u09cb-\\u09cd\\u09d7\\u09e2\\u09e3\\u09e6-\\u09ef\\u09fe\\u0a01-\\u0a03\\u0a3c\\u0a3e-\\u0a42\\u0a47\\u0a48\\u0a4b-\\u0a4d\\u0a51\\u0a66-\\u0a71\\u0a75\\u0a81-\\u0a83\\u0abc\\u0abe-\\u0ac5\\u0ac7-\\u0ac9\\u0acb-\\u0acd\\u0ae2\\u0ae3\\u0ae6-\\u0aef\\u0afa-\\u0aff\\u0b01-\\u0b03\\u0b3c\\u0b3e-\\u0b44\\u0b47\\u0b48\\u0b4b-\\u0b4d\\u0b55-\\u0b57\\u0b62\\u0b63\\u0b66-\\u0b6f\\u0b82\\u0bbe-\\u0bc2\\u0bc6-\\u0bc8\\u0bca-\\u0bcd\\u0bd7\\u0be6-\\u0bef\\u0c00-\\u0c04\\u0c3c\\u0c3e-\\u0c44\\u0c46-\\u0c48\\u0c4a-\\u0c4d\\u0c55\\u0c56\\u0c62\\u0c63\\u0c66-\\u0c6f\\u0c81-\\u0c83\\u0cbc\\u0cbe-\\u0cc4\\u0cc6-\\u0cc8\\u0cca-\\u0ccd\\u0cd5\\u0cd6\\u0ce2\\u0ce3\\u0ce6-\\u0cef\\u0cf3\\u0d00-\\u0d03\\u0d3b\\u0d3c\\u0d3e-\\u0d44\\u0d46-\\u0d48\\u0d4a-\\u0d4d\\u0d57\\u0d62\\u0d63\\u0d66-\\u0d6f\\u0d81-\\u0d83\\u0dca\\u0dcf-\\u0dd4\\u0dd6\\u0dd8-\\u0ddf\\u0de6-\\u0def\\u0df2\\u0df3\\u0e31\\u0e34-\\u0e3a\\u0e47-\\u0e4e\\u0e50-\\u0e59\\u0eb1\\u0eb4-\\u0ebc\\u0ec8-\\u0ece\\u0ed0-\\u0ed9\\u0f18\\u0f19\\u0f20-\\u0f29\\u0f35\\u0f37\\u0f39\\u0f3e\\u0f3f\\u0f71-\\u0f84\\u0f86\\u0f87\\u0f8d-\\u0f97\\u0f99-\\u0fbc\\u0fc6\\u102b-\\u103e\\u1040-\\u1049\\u1056-\\u1059\\u105e-\\u1060\\u1062-\\u1064\\u1067-\\u106d\\u1071-\\u1074\\u1082-\\u108d\\u108f-\\u109d\\u135d-\\u135f\\u1369-\\u1371\\u1712-\\u1715\\u1732-\\u1734\\u1752\\u1753\\u1772\\u1773\\u17b4-\\u17d3\\u17dd\\u17e0-\\u17e9\\u180b-\\u180d\\u180f-\\u1819\\u18a9\\u1920-\\u192b\\u1930-\\u193b\\u1946-\\u194f\\u19d0-\\u19da\\u1a17-\\u1a1b\\u1a55-\\u1a5e\\u1a60-\\u1a7c\\u1a7f-\\u1a89\\u1a90-\\u1a99\\u1ab0-\\u1abd\\u1abf-\\u1ace\\u1b00-\\u1b04\\u1b34-\\u1b44\\u1b50-\\u1b59\\u1b6b-\\u1b73\\u1b80-\\u1b82\\u1ba1-\\u1bad\\u1bb0-\\u1bb9\\u1be6-\\u1bf3\\u1c24-\\u1c37\\u1c40-\\u1c49\\u1c50-\\u1c59\\u1cd0-\\u1cd2\\u1cd4-\\u1ce8\\u1ced\\u1cf4\\u1cf7-\\u1cf9\\u1dc0-\\u1dff\\u200c\\u200d\\u203f\\u2040\\u2054\\u20d0-\\u20dc\\u20e1\\u20e5-\\u20f0\\u2cef-\\u2cf1\\u2d7f\\u2de0-\\u2dff\\u302a-\\u302f\\u3099\\u309a\\u30fb\\ua620-\\ua629\\ua66f\\ua674-\\ua67d\\ua69e\\ua69f\\ua6f0\\ua6f1\\ua802\\ua806\\ua80b\\ua823-\\ua827\\ua82c\\ua880\\ua881\\ua8b4-\\ua8c5\\ua8d0-\\ua8d9\\ua8e0-\\ua8f1\\ua8ff-\\ua909\\ua926-\\ua92d\\ua947-\\ua953\\ua980-\\ua983\\ua9b3-\\ua9c0\\ua9d0-\\ua9d9\\ua9e5\\ua9f0-\\ua9f9\\uaa29-\\uaa36\\uaa43\\uaa4c\\uaa4d\\uaa50-\\uaa59\\uaa7b-\\uaa7d\\uaab0\\uaab2-\\uaab4\\uaab7\\uaab8\\uaabe\\uaabf\\uaac1\\uaaeb-\\uaaef\\uaaf5\\uaaf6\\uabe3-\\uabea\\uabec\\uabed\\uabf0-\\uabf9\\ufb1e\\ufe00-\\ufe0f\\ufe20-\\ufe2f\\ufe33\\ufe34\\ufe4d-\\ufe4f\\uff10-\\uff19\\uff3f\\uff65\";\n\n// This file was generated. Do not modify manually!\nvar nonASCIIidentifierStartChars = \"\\xaa\\xb5\\xba\\xc0-\\xd6\\xd8-\\xf6\\xf8-\\u02c1\\u02c6-\\u02d1\\u02e0-\\u02e4\\u02ec\\u02ee\\u0370-\\u0374\\u0376\\u0377\\u037a-\\u037d\\u037f\\u0386\\u0388-\\u038a\\u038c\\u038e-\\u03a1\\u03a3-\\u03f5\\u03f7-\\u0481\\u048a-\\u052f\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u05d0-\\u05ea\\u05ef-\\u05f2\\u0620-\\u064a\\u066e\\u066f\\u0671-\\u06d3\\u06d5\\u06e5\\u06e6\\u06ee\\u06ef\\u06fa-\\u06fc\\u06ff\\u0710\\u0712-\\u072f\\u074d-\\u07a5\\u07b1\\u07ca-\\u07ea\\u07f4\\u07f5\\u07fa\\u0800-\\u0815\\u081a\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086a\\u0870-\\u0887\\u0889-\\u088e\\u08a0-\\u08c9\\u0904-\\u0939\\u093d\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098c\\u098f\\u0990\\u0993-\\u09a8\\u09aa-\\u09b0\\u09b2\\u09b6-\\u09b9\\u09bd\\u09ce\\u09dc\\u09dd\\u09df-\\u09e1\\u09f0\\u09f1\\u09fc\\u0a05-\\u0a0a\\u0a0f\\u0a10\\u0a13-\\u0a28\\u0a2a-\\u0a30\\u0a32\\u0a33\\u0a35\\u0a36\\u0a38\\u0a39\\u0a59-\\u0a5c\\u0a5e\\u0a72-\\u0a74\\u0a85-\\u0a8d\\u0a8f-\\u0a91\\u0a93-\\u0aa8\\u0aaa-\\u0ab0\\u0ab2\\u0ab3\\u0ab5-\\u0ab9\\u0abd\\u0ad0\\u0ae0\\u0ae1\\u0af9\\u0b05-\\u0b0c\\u0b0f\\u0b10\\u0b13-\\u0b28\\u0b2a-\\u0b30\\u0b32\\u0b33\\u0b35-\\u0b39\\u0b3d\\u0b5c\\u0b5d\\u0b5f-\\u0b61\\u0b71\\u0b83\\u0b85-\\u0b8a\\u0b8e-\\u0b90\\u0b92-\\u0b95\\u0b99\\u0b9a\\u0b9c\\u0b9e\\u0b9f\\u0ba3\\u0ba4\\u0ba8-\\u0baa\\u0bae-\\u0bb9\\u0bd0\\u0c05-\\u0c0c\\u0c0e-\\u0c10\\u0c12-\\u0c28\\u0c2a-\\u0c39\\u0c3d\\u0c58-\\u0c5a\\u0c5d\\u0c60\\u0c61\\u0c80\\u0c85-\\u0c8c\\u0c8e-\\u0c90\\u0c92-\\u0ca8\\u0caa-\\u0cb3\\u0cb5-\\u0cb9\\u0cbd\\u0cdd\\u0cde\\u0ce0\\u0ce1\\u0cf1\\u0cf2\\u0d04-\\u0d0c\\u0d0e-\\u0d10\\u0d12-\\u0d3a\\u0d3d\\u0d4e\\u0d54-\\u0d56\\u0d5f-\\u0d61\\u0d7a-\\u0d7f\\u0d85-\\u0d96\\u0d9a-\\u0db1\\u0db3-\\u0dbb\\u0dbd\\u0dc0-\\u0dc6\\u0e01-\\u0e30\\u0e32\\u0e33\\u0e40-\\u0e46\\u0e81\\u0e82\\u0e84\\u0e86-\\u0e8a\\u0e8c-\\u0ea3\\u0ea5\\u0ea7-\\u0eb0\\u0eb2\\u0eb3\\u0ebd\\u0ec0-\\u0ec4\\u0ec6\\u0edc-\\u0edf\\u0f00\\u0f40-\\u0f47\\u0f49-\\u0f6c\\u0f88-\\u0f8c\\u1000-\\u102a\\u103f\\u1050-\\u1055\\u105a-\\u105d\\u1061\\u1065\\u1066\\u106e-\\u1070\\u1075-\\u1081\\u108e\\u10a0-\\u10c5\\u10c7\\u10cd\\u10d0-\\u10fa\\u10fc-\\u1248\\u124a-\\u124d\\u1250-\\u1256\\u1258\\u125a-\\u125d\\u1260-\\u1288\\u128a-\\u128d\\u1290-\\u12b0\\u12b2-\\u12b5\\u12b8-\\u12be\\u12c0\\u12c2-\\u12c5\\u12c8-\\u12d6\\u12d8-\\u1310\\u1312-\\u1315\\u1318-\\u135a\\u1380-\\u138f\\u13a0-\\u13f5\\u13f8-\\u13fd\\u1401-\\u166c\\u166f-\\u167f\\u1681-\\u169a\\u16a0-\\u16ea\\u16ee-\\u16f8\\u1700-\\u1711\\u171f-\\u1731\\u1740-\\u1751\\u1760-\\u176c\\u176e-\\u1770\\u1780-\\u17b3\\u17d7\\u17dc\\u1820-\\u1878\\u1880-\\u18a8\\u18aa\\u18b0-\\u18f5\\u1900-\\u191e\\u1950-\\u196d\\u1970-\\u1974\\u1980-\\u19ab\\u19b0-\\u19c9\\u1a00-\\u1a16\\u1a20-\\u1a54\\u1aa7\\u1b05-\\u1b33\\u1b45-\\u1b4c\\u1b83-\\u1ba0\\u1bae\\u1baf\\u1bba-\\u1be5\\u1c00-\\u1c23\\u1c4d-\\u1c4f\\u1c5a-\\u1c7d\\u1c80-\\u1c8a\\u1c90-\\u1cba\\u1cbd-\\u1cbf\\u1ce9-\\u1cec\\u1cee-\\u1cf3\\u1cf5\\u1cf6\\u1cfa\\u1d00-\\u1dbf\\u1e00-\\u1f15\\u1f18-\\u1f1d\\u1f20-\\u1f45\\u1f48-\\u1f4d\\u1f50-\\u1f57\\u1f59\\u1f5b\\u1f5d\\u1f5f-\\u1f7d\\u1f80-\\u1fb4\\u1fb6-\\u1fbc\\u1fbe\\u1fc2-\\u1fc4\\u1fc6-\\u1fcc\\u1fd0-\\u1fd3\\u1fd6-\\u1fdb\\u1fe0-\\u1fec\\u1ff2-\\u1ff4\\u1ff6-\\u1ffc\\u2071\\u207f\\u2090-\\u209c\\u2102\\u2107\\u210a-\\u2113\\u2115\\u2118-\\u211d\\u2124\\u2126\\u2128\\u212a-\\u2139\\u213c-\\u213f\\u2145-\\u2149\\u214e\\u2160-\\u2188\\u2c00-\\u2ce4\\u2ceb-\\u2cee\\u2cf2\\u2cf3\\u2d00-\\u2d25\\u2d27\\u2d2d\\u2d30-\\u2d67\\u2d6f\\u2d80-\\u2d96\\u2da0-\\u2da6\\u2da8-\\u2dae\\u2db0-\\u2db6\\u2db8-\\u2dbe\\u2dc0-\\u2dc6\\u2dc8-\\u2dce\\u2dd0-\\u2dd6\\u2dd8-\\u2dde\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303c\\u3041-\\u3096\\u309b-\\u309f\\u30a1-\\u30fa\\u30fc-\\u30ff\\u3105-\\u312f\\u3131-\\u318e\\u31a0-\\u31bf\\u31f0-\\u31ff\\u3400-\\u4dbf\\u4e00-\\ua48c\\ua4d0-\\ua4fd\\ua500-\\ua60c\\ua610-\\ua61f\\ua62a\\ua62b\\ua640-\\ua66e\\ua67f-\\ua69d\\ua6a0-\\ua6ef\\ua717-\\ua71f\\ua722-\\ua788\\ua78b-\\ua7cd\\ua7d0\\ua7d1\\ua7d3\\ua7d5-\\ua7dc\\ua7f2-\\ua801\\ua803-\\ua805\\ua807-\\ua80a\\ua80c-\\ua822\\ua840-\\ua873\\ua882-\\ua8b3\\ua8f2-\\ua8f7\\ua8fb\\ua8fd\\ua8fe\\ua90a-\\ua925\\ua930-\\ua946\\ua960-\\ua97c\\ua984-\\ua9b2\\ua9cf\\ua9e0-\\ua9e4\\ua9e6-\\ua9ef\\ua9fa-\\ua9fe\\uaa00-\\uaa28\\uaa40-\\uaa42\\uaa44-\\uaa4b\\uaa60-\\uaa76\\uaa7a\\uaa7e-\\uaaaf\\uaab1\\uaab5\\uaab6\\uaab9-\\uaabd\\uaac0\\uaac2\\uaadb-\\uaadd\\uaae0-\\uaaea\\uaaf2-\\uaaf4\\uab01-\\uab06\\uab09-\\uab0e\\uab11-\\uab16\\uab20-\\uab26\\uab28-\\uab2e\\uab30-\\uab5a\\uab5c-\\uab69\\uab70-\\uabe2\\uac00-\\ud7a3\\ud7b0-\\ud7c6\\ud7cb-\\ud7fb\\uf900-\\ufa6d\\ufa70-\\ufad9\\ufb00-\\ufb06\\ufb13-\\ufb17\\ufb1d\\ufb1f-\\ufb28\\ufb2a-\\ufb36\\ufb38-\\ufb3c\\ufb3e\\ufb40\\ufb41\\ufb43\\ufb44\\ufb46-\\ufbb1\\ufbd3-\\ufd3d\\ufd50-\\ufd8f\\ufd92-\\ufdc7\\ufdf0-\\ufdfb\\ufe70-\\ufe74\\ufe76-\\ufefc\\uff21-\\uff3a\\uff41-\\uff5a\\uff66-\\uffbe\\uffc2-\\uffc7\\uffca-\\uffcf\\uffd2-\\uffd7\\uffda-\\uffdc\";\n\n// These are a run-length and offset encoded representation of the\n// >0xffff code points that are a valid part of identifiers. The\n// offset starts at 0x10000, and each pair of numbers represents an\n// offset to the next range, and then a size of the range.\n\n// Reserved word lists for various dialects of the language\n\nvar reservedWords = {\n  3: \"abstract boolean byte char class double enum export extends final float goto implements import int interface long native package private protected public short static super synchronized throws transient volatile\",\n  5: \"class enum extends super const export import\",\n  6: \"enum\",\n  strict: \"implements interface let package private protected public static yield\",\n  strictBind: \"eval arguments\"\n};\n\n// And the keywords\n\nvar ecma5AndLessKeywords = \"break case catch continue debugger default do else finally for function if return switch throw try var while with null true false instanceof typeof void delete new in this\";\n\nvar keywords$1 = {\n  5: ecma5AndLessKeywords,\n  \"5module\": ecma5AndLessKeywords + \" export import\",\n  6: ecma5AndLessKeywords + \" const class extends export import super\"\n};\n\nvar keywordRelationalOperator = /^in(stanceof)?$/;\n\n// ## Character categories\n\nvar nonASCIIidentifierStart = new RegExp(\"[\" + nonASCIIidentifierStartChars + \"]\");\nvar nonASCIIidentifier = new RegExp(\"[\" + nonASCIIidentifierStartChars + nonASCIIidentifierChars + \"]\");\n\n// This has a complexity linear to the value of the code. The\n// assumption is that looking up astral identifier characters is\n// rare.\nfunction isInAstralSet(code, set) {\n  var pos = 0x10000;\n  for (var i = 0; i < set.length; i += 2) {\n    pos += set[i];\n    if (pos > code) { return false }\n    pos += set[i + 1];\n    if (pos >= code) { return true }\n  }\n  return false\n}\n\n// Test whether a given character code starts an identifier.\n\nfunction isIdentifierStart(code, astral) {\n  if (code < 65) { return code === 36 }\n  if (code < 91) { return true }\n  if (code < 97) { return code === 95 }\n  if (code < 123) { return true }\n  if (code <= 0xffff) { return code >= 0xaa && nonASCIIidentifierStart.test(String.fromCharCode(code)) }\n  if (astral === false) { return false }\n  return isInAstralSet(code, astralIdentifierStartCodes)\n}\n\n// Test whether a given character is part of an identifier.\n\nfunction isIdentifierChar(code, astral) {\n  if (code < 48) { return code === 36 }\n  if (code < 58) { return true }\n  if (code < 65) { return false }\n  if (code < 91) { return true }\n  if (code < 97) { return code === 95 }\n  if (code < 123) { return true }\n  if (code <= 0xffff) { return code >= 0xaa && nonASCIIidentifier.test(String.fromCharCode(code)) }\n  if (astral === false) { return false }\n  return isInAstralSet(code, astralIdentifierStartCodes) || isInAstralSet(code, astralIdentifierCodes)\n}\n\n// ## Token types\n\n// The assignment of fine-grained, information-carrying type objects\n// allows the tokenizer to store the information it has about a\n// token in a way that is very cheap for the parser to look up.\n\n// All token type variables start with an underscore, to make them\n// easy to recognize.\n\n// The `beforeExpr` property is used to disambiguate between regular\n// expressions and divisions. It is set on all token types that can\n// be followed by an expression (thus, a slash after them would be a\n// regular expression).\n//\n// The `startsExpr` property is used to check if the token ends a\n// `yield` expression. It is set on all token types that either can\n// directly start an expression (like a quotation mark) or can\n// continue an expression (like the body of a string).\n//\n// `isLoop` marks a keyword as starting a loop, which is important\n// to know when parsing a label, in order to allow or disallow\n// continue jumps to that label.\n\nvar TokenType = function TokenType(label, conf) {\n  if ( conf === void 0 ) conf = {};\n\n  this.label = label;\n  this.keyword = conf.keyword;\n  this.beforeExpr = !!conf.beforeExpr;\n  this.startsExpr = !!conf.startsExpr;\n  this.isLoop = !!conf.isLoop;\n  this.isAssign = !!conf.isAssign;\n  this.prefix = !!conf.prefix;\n  this.postfix = !!conf.postfix;\n  this.binop = conf.binop || null;\n  this.updateContext = null;\n};\n\nfunction binop(name, prec) {\n  return new TokenType(name, {beforeExpr: true, binop: prec})\n}\nvar beforeExpr = {beforeExpr: true}, startsExpr = {startsExpr: true};\n\n// Map keyword names to token types.\n\nvar keywords = {};\n\n// Succinct definitions of keyword token types\nfunction kw(name, options) {\n  if ( options === void 0 ) options = {};\n\n  options.keyword = name;\n  return keywords[name] = new TokenType(name, options)\n}\n\nvar types$1 = {\n  num: new TokenType(\"num\", startsExpr),\n  regexp: new TokenType(\"regexp\", startsExpr),\n  string: new TokenType(\"string\", startsExpr),\n  name: new TokenType(\"name\", startsExpr),\n  privateId: new TokenType(\"privateId\", startsExpr),\n  eof: new TokenType(\"eof\"),\n\n  // Punctuation token types.\n  bracketL: new TokenType(\"[\", {beforeExpr: true, startsExpr: true}),\n  bracketR: new TokenType(\"]\"),\n  braceL: new TokenType(\"{\", {beforeExpr: true, startsExpr: true}),\n  braceR: new TokenType(\"}\"),\n  parenL: new TokenType(\"(\", {beforeExpr: true, startsExpr: true}),\n  parenR: new TokenType(\")\"),\n  comma: new TokenType(\",\", beforeExpr),\n  semi: new TokenType(\";\", beforeExpr),\n  colon: new TokenType(\":\", beforeExpr),\n  dot: new TokenType(\".\"),\n  question: new TokenType(\"?\", beforeExpr),\n  questionDot: new TokenType(\"?.\"),\n  arrow: new TokenType(\"=>\", beforeExpr),\n  template: new TokenType(\"template\"),\n  invalidTemplate: new TokenType(\"invalidTemplate\"),\n  ellipsis: new TokenType(\"...\", beforeExpr),\n  backQuote: new TokenType(\"`\", startsExpr),\n  dollarBraceL: new TokenType(\"${\", {beforeExpr: true, startsExpr: true}),\n\n  // Operators. These carry several kinds of properties to help the\n  // parser use them properly (the presence of these properties is\n  // what categorizes them as operators).\n  //\n  // `binop`, when present, specifies that this operator is a binary\n  // operator, and will refer to its precedence.\n  //\n  // `prefix` and `postfix` mark the operator as a prefix or postfix\n  // unary operator.\n  //\n  // `isAssign` marks all of `=`, `+=`, `-=` etcetera, which act as\n  // binary operators with a very low precedence, that should result\n  // in AssignmentExpression nodes.\n\n  eq: new TokenType(\"=\", {beforeExpr: true, isAssign: true}),\n  assign: new TokenType(\"_=\", {beforeExpr: true, isAssign: true}),\n  incDec: new TokenType(\"++/--\", {prefix: true, postfix: true, startsExpr: true}),\n  prefix: new TokenType(\"!/~\", {beforeExpr: true, prefix: true, startsExpr: true}),\n  logicalOR: binop(\"||\", 1),\n  logicalAND: binop(\"&&\", 2),\n  bitwiseOR: binop(\"|\", 3),\n  bitwiseXOR: binop(\"^\", 4),\n  bitwiseAND: binop(\"&\", 5),\n  equality: binop(\"==/!=/===/!==\", 6),\n  relational: binop(\"</>/<=/>=\", 7),\n  bitShift: binop(\"<</>>/>>>\", 8),\n  plusMin: new TokenType(\"+/-\", {beforeExpr: true, binop: 9, prefix: true, startsExpr: true}),\n  modulo: binop(\"%\", 10),\n  star: binop(\"*\", 10),\n  slash: binop(\"/\", 10),\n  starstar: new TokenType(\"**\", {beforeExpr: true}),\n  coalesce: binop(\"??\", 1),\n\n  // Keyword token types.\n  _break: kw(\"break\"),\n  _case: kw(\"case\", beforeExpr),\n  _catch: kw(\"catch\"),\n  _continue: kw(\"continue\"),\n  _debugger: kw(\"debugger\"),\n  _default: kw(\"default\", beforeExpr),\n  _do: kw(\"do\", {isLoop: true, beforeExpr: true}),\n  _else: kw(\"else\", beforeExpr),\n  _finally: kw(\"finally\"),\n  _for: kw(\"for\", {isLoop: true}),\n  _function: kw(\"function\", startsExpr),\n  _if: kw(\"if\"),\n  _return: kw(\"return\", beforeExpr),\n  _switch: kw(\"switch\"),\n  _throw: kw(\"throw\", beforeExpr),\n  _try: kw(\"try\"),\n  _var: kw(\"var\"),\n  _const: kw(\"const\"),\n  _while: kw(\"while\", {isLoop: true}),\n  _with: kw(\"with\"),\n  _new: kw(\"new\", {beforeExpr: true, startsExpr: true}),\n  _this: kw(\"this\", startsExpr),\n  _super: kw(\"super\", startsExpr),\n  _class: kw(\"class\", startsExpr),\n  _extends: kw(\"extends\", beforeExpr),\n  _export: kw(\"export\"),\n  _import: kw(\"import\", startsExpr),\n  _null: kw(\"null\", startsExpr),\n  _true: kw(\"true\", startsExpr),\n  _false: kw(\"false\", startsExpr),\n  _in: kw(\"in\", {beforeExpr: true, binop: 7}),\n  _instanceof: kw(\"instanceof\", {beforeExpr: true, binop: 7}),\n  _typeof: kw(\"typeof\", {beforeExpr: true, prefix: true, startsExpr: true}),\n  _void: kw(\"void\", {beforeExpr: true, prefix: true, startsExpr: true}),\n  _delete: kw(\"delete\", {beforeExpr: true, prefix: true, startsExpr: true})\n};\n\n// Matches a whole line break (where CRLF is considered a single\n// line break). Used to count lines.\n\nvar lineBreak = /\\r\\n?|\\n|\\u2028|\\u2029/;\nvar lineBreakG = new RegExp(lineBreak.source, \"g\");\n\nfunction isNewLine(code) {\n  return code === 10 || code === 13 || code === 0x2028 || code === 0x2029\n}\n\nfunction nextLineBreak(code, from, end) {\n  if ( end === void 0 ) end = code.length;\n\n  for (var i = from; i < end; i++) {\n    var next = code.charCodeAt(i);\n    if (isNewLine(next))\n      { return i < end - 1 && next === 13 && code.charCodeAt(i + 1) === 10 ? i + 2 : i + 1 }\n  }\n  return -1\n}\n\nvar nonASCIIwhitespace = /[\\u1680\\u2000-\\u200a\\u202f\\u205f\\u3000\\ufeff]/;\n\nvar skipWhiteSpace = /(?:\\s|\\/\\/.*|\\/\\*[^]*?\\*\\/)*/g;\n\nvar ref = Object.prototype;\nvar hasOwnProperty = ref.hasOwnProperty;\nvar toString = ref.toString;\n\nvar hasOwn = Object.hasOwn || (function (obj, propName) { return (\n  hasOwnProperty.call(obj, propName)\n); });\n\nvar isArray = Array.isArray || (function (obj) { return (\n  toString.call(obj) === \"[object Array]\"\n); });\n\nvar regexpCache = Object.create(null);\n\nfunction wordsRegexp(words) {\n  return regexpCache[words] || (regexpCache[words] = new RegExp(\"^(?:\" + words.replace(/ /g, \"|\") + \")$\"))\n}\n\nfunction codePointToString(code) {\n  // UTF-16 Decoding\n  if (code <= 0xFFFF) { return String.fromCharCode(code) }\n  code -= 0x10000;\n  return String.fromCharCode((code >> 10) + 0xD800, (code & 1023) + 0xDC00)\n}\n\nvar loneSurrogate = /(?:[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF])/;\n\n// These are used when `options.locations` is on, for the\n// `startLoc` and `endLoc` properties.\n\nvar Position = function Position(line, col) {\n  this.line = line;\n  this.column = col;\n};\n\nPosition.prototype.offset = function offset (n) {\n  return new Position(this.line, this.column + n)\n};\n\nvar SourceLocation = function SourceLocation(p, start, end) {\n  this.start = start;\n  this.end = end;\n  if (p.sourceFile !== null) { this.source = p.sourceFile; }\n};\n\n// The `getLineInfo` function is mostly useful when the\n// `locations` option is off (for performance reasons) and you\n// want to find the line/column position for a given character\n// offset. `input` should be the code string that the offset refers\n// into.\n\nfunction getLineInfo(input, offset) {\n  for (var line = 1, cur = 0;;) {\n    var nextBreak = nextLineBreak(input, cur, offset);\n    if (nextBreak < 0) { return new Position(line, offset - cur) }\n    ++line;\n    cur = nextBreak;\n  }\n}\n\n// A second argument must be given to configure the parser process.\n// These options are recognized (only `ecmaVersion` is required):\n\nvar defaultOptions = {\n  // `ecmaVersion` indicates the ECMAScript version to parse. Must be\n  // either 3, 5, 6 (or 2015), 7 (2016), 8 (2017), 9 (2018), 10\n  // (2019), 11 (2020), 12 (2021), 13 (2022), 14 (2023), or `\"latest\"`\n  // (the latest version the library supports). This influences\n  // support for strict mode, the set of reserved words, and support\n  // for new syntax features.\n  ecmaVersion: null,\n  // `sourceType` indicates the mode the code should be parsed in.\n  // Can be either `\"script\"` or `\"module\"`. This influences global\n  // strict mode and parsing of `import` and `export` declarations.\n  sourceType: \"script\",\n  // `onInsertedSemicolon` can be a callback that will be called when\n  // a semicolon is automatically inserted. It will be passed the\n  // position of the inserted semicolon as an offset, and if\n  // `locations` is enabled, it is given the location as a `{line,\n  // column}` object as second argument.\n  onInsertedSemicolon: null,\n  // `onTrailingComma` is similar to `onInsertedSemicolon`, but for\n  // trailing commas.\n  onTrailingComma: null,\n  // By default, reserved words are only enforced if ecmaVersion >= 5.\n  // Set `allowReserved` to a boolean value to explicitly turn this on\n  // an off. When this option has the value \"never\", reserved words\n  // and keywords can also not be used as property names.\n  allowReserved: null,\n  // When enabled, a return at the top level is not considered an\n  // error.\n  allowReturnOutsideFunction: false,\n  // When enabled, import/export statements are not constrained to\n  // appearing at the top of the program, and an import.meta expression\n  // in a script isn't considered an error.\n  allowImportExportEverywhere: false,\n  // By default, await identifiers are allowed to appear at the top-level scope only if ecmaVersion >= 2022.\n  // When enabled, await identifiers are allowed to appear at the top-level scope,\n  // but they are still not allowed in non-async functions.\n  allowAwaitOutsideFunction: null,\n  // When enabled, super identifiers are not constrained to\n  // appearing in methods and do not raise an error when they appear elsewhere.\n  allowSuperOutsideMethod: null,\n  // When enabled, hashbang directive in the beginning of file is\n  // allowed and treated as a line comment. Enabled by default when\n  // `ecmaVersion` >= 2023.\n  allowHashBang: false,\n  // By default, the parser will verify that private properties are\n  // only used in places where they are valid and have been declared.\n  // Set this to false to turn such checks off.\n  checkPrivateFields: true,\n  // When `locations` is on, `loc` properties holding objects with\n  // `start` and `end` properties in `{line, column}` form (with\n  // line being 1-based and column 0-based) will be attached to the\n  // nodes.\n  locations: false,\n  // A function can be passed as `onToken` option, which will\n  // cause Acorn to call that function with object in the same\n  // format as tokens returned from `tokenizer().getToken()`. Note\n  // that you are not allowed to call the parser from the\n  // callback—that will corrupt its internal state.\n  onToken: null,\n  // A function can be passed as `onComment` option, which will\n  // cause Acorn to call that function with `(block, text, start,\n  // end)` parameters whenever a comment is skipped. `block` is a\n  // boolean indicating whether this is a block (`/* */`) comment,\n  // `text` is the content of the comment, and `start` and `end` are\n  // character offsets that denote the start and end of the comment.\n  // When the `locations` option is on, two more parameters are\n  // passed, the full `{line, column}` locations of the start and\n  // end of the comments. Note that you are not allowed to call the\n  // parser from the callback—that will corrupt its internal state.\n  // When this option has an array as value, objects representing the\n  // comments are pushed to it.\n  onComment: null,\n  // Nodes have their start and end characters offsets recorded in\n  // `start` and `end` properties (directly on the node, rather than\n  // the `loc` object, which holds line/column data. To also add a\n  // [semi-standardized][range] `range` property holding a `[start,\n  // end]` array with the same numbers, set the `ranges` option to\n  // `true`.\n  //\n  // [range]: https://bugzilla.mozilla.org/show_bug.cgi?id=745678\n  ranges: false,\n  // It is possible to parse multiple files into a single AST by\n  // passing the tree produced by parsing the first file as\n  // `program` option in subsequent parses. This will add the\n  // toplevel forms of the parsed file to the `Program` (top) node\n  // of an existing parse tree.\n  program: null,\n  // When `locations` is on, you can pass this to record the source\n  // file in every node's `loc` object.\n  sourceFile: null,\n  // This value, if given, is stored in every node, whether\n  // `locations` is on or off.\n  directSourceFile: null,\n  // When enabled, parenthesized expressions are represented by\n  // (non-standard) ParenthesizedExpression nodes\n  preserveParens: false\n};\n\n// Interpret and default an options object\n\nvar warnedAboutEcmaVersion = false;\n\nfunction getOptions(opts) {\n  var options = {};\n\n  for (var opt in defaultOptions)\n    { options[opt] = opts && hasOwn(opts, opt) ? opts[opt] : defaultOptions[opt]; }\n\n  if (options.ecmaVersion === \"latest\") {\n    options.ecmaVersion = 1e8;\n  } else if (options.ecmaVersion == null) {\n    if (!warnedAboutEcmaVersion && typeof console === \"object\" && console.warn) {\n      warnedAboutEcmaVersion = true;\n      console.warn(\"Since Acorn 8.0.0, options.ecmaVersion is required.\\nDefaulting to 2020, but this will stop working in the future.\");\n    }\n    options.ecmaVersion = 11;\n  } else if (options.ecmaVersion >= 2015) {\n    options.ecmaVersion -= 2009;\n  }\n\n  if (options.allowReserved == null)\n    { options.allowReserved = options.ecmaVersion < 5; }\n\n  if (!opts || opts.allowHashBang == null)\n    { options.allowHashBang = options.ecmaVersion >= 14; }\n\n  if (isArray(options.onToken)) {\n    var tokens = options.onToken;\n    options.onToken = function (token) { return tokens.push(token); };\n  }\n  if (isArray(options.onComment))\n    { options.onComment = pushComment(options, options.onComment); }\n\n  return options\n}\n\nfunction pushComment(options, array) {\n  return function(block, text, start, end, startLoc, endLoc) {\n    var comment = {\n      type: block ? \"Block\" : \"Line\",\n      value: text,\n      start: start,\n      end: end\n    };\n    if (options.locations)\n      { comment.loc = new SourceLocation(this, startLoc, endLoc); }\n    if (options.ranges)\n      { comment.range = [start, end]; }\n    array.push(comment);\n  }\n}\n\n// Each scope gets a bitset that may contain these flags\nvar\n    SCOPE_TOP = 1,\n    SCOPE_FUNCTION = 2,\n    SCOPE_ASYNC = 4,\n    SCOPE_GENERATOR = 8,\n    SCOPE_ARROW = 16,\n    SCOPE_SIMPLE_CATCH = 32,\n    SCOPE_SUPER = 64,\n    SCOPE_DIRECT_SUPER = 128,\n    SCOPE_CLASS_STATIC_BLOCK = 256,\n    SCOPE_CLASS_FIELD_INIT = 512,\n    SCOPE_VAR = SCOPE_TOP | SCOPE_FUNCTION | SCOPE_CLASS_STATIC_BLOCK;\n\nfunction functionFlags(async, generator) {\n  return SCOPE_FUNCTION | (async ? SCOPE_ASYNC : 0) | (generator ? SCOPE_GENERATOR : 0)\n}\n\n// Used in checkLVal* and declareName to determine the type of a binding\nvar\n    BIND_NONE = 0, // Not a binding\n    BIND_VAR = 1, // Var-style binding\n    BIND_LEXICAL = 2, // Let- or const-style binding\n    BIND_FUNCTION = 3, // Function declaration\n    BIND_SIMPLE_CATCH = 4, // Simple (identifier pattern) catch binding\n    BIND_OUTSIDE = 5; // Special case for function names as bound inside the function\n\nvar Parser = function Parser(options, input, startPos) {\n  this.options = options = getOptions(options);\n  this.sourceFile = options.sourceFile;\n  this.keywords = wordsRegexp(keywords$1[options.ecmaVersion >= 6 ? 6 : options.sourceType === \"module\" ? \"5module\" : 5]);\n  var reserved = \"\";\n  if (options.allowReserved !== true) {\n    reserved = reservedWords[options.ecmaVersion >= 6 ? 6 : options.ecmaVersion === 5 ? 5 : 3];\n    if (options.sourceType === \"module\") { reserved += \" await\"; }\n  }\n  this.reservedWords = wordsRegexp(reserved);\n  var reservedStrict = (reserved ? reserved + \" \" : \"\") + reservedWords.strict;\n  this.reservedWordsStrict = wordsRegexp(reservedStrict);\n  this.reservedWordsStrictBind = wordsRegexp(reservedStrict + \" \" + reservedWords.strictBind);\n  this.input = String(input);\n\n  // Used to signal to callers of `readWord1` whether the word\n  // contained any escape sequences. This is needed because words with\n  // escape sequences must not be interpreted as keywords.\n  this.containsEsc = false;\n\n  // Set up token state\n\n  // The current position of the tokenizer in the input.\n  if (startPos) {\n    this.pos = startPos;\n    this.lineStart = this.input.lastIndexOf(\"\\n\", startPos - 1) + 1;\n    this.curLine = this.input.slice(0, this.lineStart).split(lineBreak).length;\n  } else {\n    this.pos = this.lineStart = 0;\n    this.curLine = 1;\n  }\n\n  // Properties of the current token:\n  // Its type\n  this.type = types$1.eof;\n  // For tokens that include more information than their type, the value\n  this.value = null;\n  // Its start and end offset\n  this.start = this.end = this.pos;\n  // And, if locations are used, the {line, column} object\n  // corresponding to those offsets\n  this.startLoc = this.endLoc = this.curPosition();\n\n  // Position information for the previous token\n  this.lastTokEndLoc = this.lastTokStartLoc = null;\n  this.lastTokStart = this.lastTokEnd = this.pos;\n\n  // The context stack is used to superficially track syntactic\n  // context to predict whether a regular expression is allowed in a\n  // given position.\n  this.context = this.initialContext();\n  this.exprAllowed = true;\n\n  // Figure out if it's a module code.\n  this.inModule = options.sourceType === \"module\";\n  this.strict = this.inModule || this.strictDirective(this.pos);\n\n  // Used to signify the start of a potential arrow function\n  this.potentialArrowAt = -1;\n  this.potentialArrowInForAwait = false;\n\n  // Positions to delayed-check that yield/await does not exist in default parameters.\n  this.yieldPos = this.awaitPos = this.awaitIdentPos = 0;\n  // Labels in scope.\n  this.labels = [];\n  // Thus-far undefined exports.\n  this.undefinedExports = Object.create(null);\n\n  // If enabled, skip leading hashbang line.\n  if (this.pos === 0 && options.allowHashBang && this.input.slice(0, 2) === \"#!\")\n    { this.skipLineComment(2); }\n\n  // Scope tracking for duplicate variable names (see scope.js)\n  this.scopeStack = [];\n  this.enterScope(SCOPE_TOP);\n\n  // For RegExp validation\n  this.regexpState = null;\n\n  // The stack of private names.\n  // Each element has two properties: 'declared' and 'used'.\n  // When it exited from the outermost class definition, all used private names must be declared.\n  this.privateNameStack = [];\n};\n\nvar prototypeAccessors = { inFunction: { configurable: true },inGenerator: { configurable: true },inAsync: { configurable: true },canAwait: { configurable: true },allowSuper: { configurable: true },allowDirectSuper: { configurable: true },treatFunctionsAsVar: { configurable: true },allowNewDotTarget: { configurable: true },inClassStaticBlock: { configurable: true } };\n\nParser.prototype.parse = function parse () {\n  var node = this.options.program || this.startNode();\n  this.nextToken();\n  return this.parseTopLevel(node)\n};\n\nprototypeAccessors.inFunction.get = function () { return (this.currentVarScope().flags & SCOPE_FUNCTION) > 0 };\n\nprototypeAccessors.inGenerator.get = function () { return (this.currentVarScope().flags & SCOPE_GENERATOR) > 0 };\n\nprototypeAccessors.inAsync.get = function () { return (this.currentVarScope().flags & SCOPE_ASYNC) > 0 };\n\nprototypeAccessors.canAwait.get = function () {\n  for (var i = this.scopeStack.length - 1; i >= 0; i--) {\n    var ref = this.scopeStack[i];\n      var flags = ref.flags;\n    if (flags & (SCOPE_CLASS_STATIC_BLOCK | SCOPE_CLASS_FIELD_INIT)) { return false }\n    if (flags & SCOPE_FUNCTION) { return (flags & SCOPE_ASYNC) > 0 }\n  }\n  return (this.inModule && this.options.ecmaVersion >= 13) || this.options.allowAwaitOutsideFunction\n};\n\nprototypeAccessors.allowSuper.get = function () {\n  var ref = this.currentThisScope();\n    var flags = ref.flags;\n  return (flags & SCOPE_SUPER) > 0 || this.options.allowSuperOutsideMethod\n};\n\nprototypeAccessors.allowDirectSuper.get = function () { return (this.currentThisScope().flags & SCOPE_DIRECT_SUPER) > 0 };\n\nprototypeAccessors.treatFunctionsAsVar.get = function () { return this.treatFunctionsAsVarInScope(this.currentScope()) };\n\nprototypeAccessors.allowNewDotTarget.get = function () {\n  for (var i = this.scopeStack.length - 1; i >= 0; i--) {\n    var ref = this.scopeStack[i];\n      var flags = ref.flags;\n    if (flags & (SCOPE_CLASS_STATIC_BLOCK | SCOPE_CLASS_FIELD_INIT) ||\n        ((flags & SCOPE_FUNCTION) && !(flags & SCOPE_ARROW))) { return true }\n  }\n  return false\n};\n\nprototypeAccessors.inClassStaticBlock.get = function () {\n  return (this.currentVarScope().flags & SCOPE_CLASS_STATIC_BLOCK) > 0\n};\n\nParser.extend = function extend () {\n    var plugins = [], len = arguments.length;\n    while ( len-- ) plugins[ len ] = arguments[ len ];\n\n  var cls = this;\n  for (var i = 0; i < plugins.length; i++) { cls = plugins[i](cls); }\n  return cls\n};\n\nParser.parse = function parse (input, options) {\n  return new this(options, input).parse()\n};\n\nParser.parseExpressionAt = function parseExpressionAt (input, pos, options) {\n  var parser = new this(options, input, pos);\n  parser.nextToken();\n  return parser.parseExpression()\n};\n\nParser.tokenizer = function tokenizer (input, options) {\n  return new this(options, input)\n};\n\nObject.defineProperties( Parser.prototype, prototypeAccessors );\n\nvar pp$9 = Parser.prototype;\n\n// ## Parser utilities\n\nvar literal = /^(?:'((?:\\\\[^]|[^'\\\\])*?)'|\"((?:\\\\[^]|[^\"\\\\])*?)\")/;\npp$9.strictDirective = function(start) {\n  if (this.options.ecmaVersion < 5) { return false }\n  for (;;) {\n    // Try to find string literal.\n    skipWhiteSpace.lastIndex = start;\n    start += skipWhiteSpace.exec(this.input)[0].length;\n    var match = literal.exec(this.input.slice(start));\n    if (!match) { return false }\n    if ((match[1] || match[2]) === \"use strict\") {\n      skipWhiteSpace.lastIndex = start + match[0].length;\n      var spaceAfter = skipWhiteSpace.exec(this.input), end = spaceAfter.index + spaceAfter[0].length;\n      var next = this.input.charAt(end);\n      return next === \";\" || next === \"}\" ||\n        (lineBreak.test(spaceAfter[0]) &&\n         !(/[(`.[+\\-/*%<>=,?^&]/.test(next) || next === \"!\" && this.input.charAt(end + 1) === \"=\"))\n    }\n    start += match[0].length;\n\n    // Skip semicolon, if any.\n    skipWhiteSpace.lastIndex = start;\n    start += skipWhiteSpace.exec(this.input)[0].length;\n    if (this.input[start] === \";\")\n      { start++; }\n  }\n};\n\n// Predicate that tests whether the next token is of the given\n// type, and if yes, consumes it as a side effect.\n\npp$9.eat = function(type) {\n  if (this.type === type) {\n    this.next();\n    return true\n  } else {\n    return false\n  }\n};\n\n// Tests whether parsed token is a contextual keyword.\n\npp$9.isContextual = function(name) {\n  return this.type === types$1.name && this.value === name && !this.containsEsc\n};\n\n// Consumes contextual keyword if possible.\n\npp$9.eatContextual = function(name) {\n  if (!this.isContextual(name)) { return false }\n  this.next();\n  return true\n};\n\n// Asserts that following token is given contextual keyword.\n\npp$9.expectContextual = function(name) {\n  if (!this.eatContextual(name)) { this.unexpected(); }\n};\n\n// Test whether a semicolon can be inserted at the current position.\n\npp$9.canInsertSemicolon = function() {\n  return this.type === types$1.eof ||\n    this.type === types$1.braceR ||\n    lineBreak.test(this.input.slice(this.lastTokEnd, this.start))\n};\n\npp$9.insertSemicolon = function() {\n  if (this.canInsertSemicolon()) {\n    if (this.options.onInsertedSemicolon)\n      { this.options.onInsertedSemicolon(this.lastTokEnd, this.lastTokEndLoc); }\n    return true\n  }\n};\n\n// Consume a semicolon, or, failing that, see if we are allowed to\n// pretend that there is a semicolon at this position.\n\npp$9.semicolon = function() {\n  if (!this.eat(types$1.semi) && !this.insertSemicolon()) { this.unexpected(); }\n};\n\npp$9.afterTrailingComma = function(tokType, notNext) {\n  if (this.type === tokType) {\n    if (this.options.onTrailingComma)\n      { this.options.onTrailingComma(this.lastTokStart, this.lastTokStartLoc); }\n    if (!notNext)\n      { this.next(); }\n    return true\n  }\n};\n\n// Expect a token of a given type. If found, consume it, otherwise,\n// raise an unexpected token error.\n\npp$9.expect = function(type) {\n  this.eat(type) || this.unexpected();\n};\n\n// Raise an unexpected token error.\n\npp$9.unexpected = function(pos) {\n  this.raise(pos != null ? pos : this.start, \"Unexpected token\");\n};\n\nvar DestructuringErrors = function DestructuringErrors() {\n  this.shorthandAssign =\n  this.trailingComma =\n  this.parenthesizedAssign =\n  this.parenthesizedBind =\n  this.doubleProto =\n    -1;\n};\n\npp$9.checkPatternErrors = function(refDestructuringErrors, isAssign) {\n  if (!refDestructuringErrors) { return }\n  if (refDestructuringErrors.trailingComma > -1)\n    { this.raiseRecoverable(refDestructuringErrors.trailingComma, \"Comma is not permitted after the rest element\"); }\n  var parens = isAssign ? refDestructuringErrors.parenthesizedAssign : refDestructuringErrors.parenthesizedBind;\n  if (parens > -1) { this.raiseRecoverable(parens, isAssign ? \"Assigning to rvalue\" : \"Parenthesized pattern\"); }\n};\n\npp$9.checkExpressionErrors = function(refDestructuringErrors, andThrow) {\n  if (!refDestructuringErrors) { return false }\n  var shorthandAssign = refDestructuringErrors.shorthandAssign;\n  var doubleProto = refDestructuringErrors.doubleProto;\n  if (!andThrow) { return shorthandAssign >= 0 || doubleProto >= 0 }\n  if (shorthandAssign >= 0)\n    { this.raise(shorthandAssign, \"Shorthand property assignments are valid only in destructuring patterns\"); }\n  if (doubleProto >= 0)\n    { this.raiseRecoverable(doubleProto, \"Redefinition of __proto__ property\"); }\n};\n\npp$9.checkYieldAwaitInDefaultParams = function() {\n  if (this.yieldPos && (!this.awaitPos || this.yieldPos < this.awaitPos))\n    { this.raise(this.yieldPos, \"Yield expression cannot be a default value\"); }\n  if (this.awaitPos)\n    { this.raise(this.awaitPos, \"Await expression cannot be a default value\"); }\n};\n\npp$9.isSimpleAssignTarget = function(expr) {\n  if (expr.type === \"ParenthesizedExpression\")\n    { return this.isSimpleAssignTarget(expr.expression) }\n  return expr.type === \"Identifier\" || expr.type === \"MemberExpression\"\n};\n\nvar pp$8 = Parser.prototype;\n\n// ### Statement parsing\n\n// Parse a program. Initializes the parser, reads any number of\n// statements, and wraps them in a Program node.  Optionally takes a\n// `program` argument.  If present, the statements will be appended\n// to its body instead of creating a new node.\n\npp$8.parseTopLevel = function(node) {\n  var exports = Object.create(null);\n  if (!node.body) { node.body = []; }\n  while (this.type !== types$1.eof) {\n    var stmt = this.parseStatement(null, true, exports);\n    node.body.push(stmt);\n  }\n  if (this.inModule)\n    { for (var i = 0, list = Object.keys(this.undefinedExports); i < list.length; i += 1)\n      {\n        var name = list[i];\n\n        this.raiseRecoverable(this.undefinedExports[name].start, (\"Export '\" + name + \"' is not defined\"));\n      } }\n  this.adaptDirectivePrologue(node.body);\n  this.next();\n  node.sourceType = this.options.sourceType;\n  return this.finishNode(node, \"Program\")\n};\n\nvar loopLabel = {kind: \"loop\"}, switchLabel = {kind: \"switch\"};\n\npp$8.isLet = function(context) {\n  if (this.options.ecmaVersion < 6 || !this.isContextual(\"let\")) { return false }\n  skipWhiteSpace.lastIndex = this.pos;\n  var skip = skipWhiteSpace.exec(this.input);\n  var next = this.pos + skip[0].length, nextCh = this.input.charCodeAt(next);\n  // For ambiguous cases, determine if a LexicalDeclaration (or only a\n  // Statement) is allowed here. If context is not empty then only a Statement\n  // is allowed. However, `let [` is an explicit negative lookahead for\n  // ExpressionStatement, so special-case it first.\n  if (nextCh === 91 || nextCh === 92) { return true } // '[', '\\'\n  if (context) { return false }\n\n  if (nextCh === 123 || nextCh > 0xd7ff && nextCh < 0xdc00) { return true } // '{', astral\n  if (isIdentifierStart(nextCh, true)) {\n    var pos = next + 1;\n    while (isIdentifierChar(nextCh = this.input.charCodeAt(pos), true)) { ++pos; }\n    if (nextCh === 92 || nextCh > 0xd7ff && nextCh < 0xdc00) { return true }\n    var ident = this.input.slice(next, pos);\n    if (!keywordRelationalOperator.test(ident)) { return true }\n  }\n  return false\n};\n\n// check 'async [no LineTerminator here] function'\n// - 'async /*foo*/ function' is OK.\n// - 'async /*\\n*/ function' is invalid.\npp$8.isAsyncFunction = function() {\n  if (this.options.ecmaVersion < 8 || !this.isContextual(\"async\"))\n    { return false }\n\n  skipWhiteSpace.lastIndex = this.pos;\n  var skip = skipWhiteSpace.exec(this.input);\n  var next = this.pos + skip[0].length, after;\n  return !lineBreak.test(this.input.slice(this.pos, next)) &&\n    this.input.slice(next, next + 8) === \"function\" &&\n    (next + 8 === this.input.length ||\n     !(isIdentifierChar(after = this.input.charCodeAt(next + 8)) || after > 0xd7ff && after < 0xdc00))\n};\n\npp$8.isUsingKeyword = function(isAwaitUsing, isFor) {\n  if (this.options.ecmaVersion < 17 || !this.isContextual(isAwaitUsing ? \"await\" : \"using\"))\n    { return false }\n\n  skipWhiteSpace.lastIndex = this.pos;\n  var skip = skipWhiteSpace.exec(this.input);\n  var next = this.pos + skip[0].length;\n\n  if (lineBreak.test(this.input.slice(this.pos, next))) { return false }\n\n  if (isAwaitUsing) {\n    var awaitEndPos = next + 5 /* await */, after;\n    if (this.input.slice(next, awaitEndPos) !== \"using\" ||\n      awaitEndPos === this.input.length ||\n      isIdentifierChar(after = this.input.charCodeAt(awaitEndPos)) ||\n      (after > 0xd7ff && after < 0xdc00)\n    ) { return false }\n\n    skipWhiteSpace.lastIndex = awaitEndPos;\n    var skipAfterUsing = skipWhiteSpace.exec(this.input);\n    if (skipAfterUsing && lineBreak.test(this.input.slice(awaitEndPos, awaitEndPos + skipAfterUsing[0].length))) { return false }\n  }\n\n  if (isFor) {\n    var ofEndPos = next + 2 /* of */, after$1;\n    if (this.input.slice(next, ofEndPos) === \"of\") {\n      if (ofEndPos === this.input.length ||\n        (!isIdentifierChar(after$1 = this.input.charCodeAt(ofEndPos)) && !(after$1 > 0xd7ff && after$1 < 0xdc00))) { return false }\n    }\n  }\n\n  var ch = this.input.charCodeAt(next);\n  return isIdentifierStart(ch, true) || ch === 92 // '\\'\n};\n\npp$8.isAwaitUsing = function(isFor) {\n  return this.isUsingKeyword(true, isFor)\n};\n\npp$8.isUsing = function(isFor) {\n  return this.isUsingKeyword(false, isFor)\n};\n\n// Parse a single statement.\n//\n// If expecting a statement and finding a slash operator, parse a\n// regular expression literal. This is to handle cases like\n// `if (foo) /blah/.exec(foo)`, where looking at the previous token\n// does not help.\n\npp$8.parseStatement = function(context, topLevel, exports) {\n  var starttype = this.type, node = this.startNode(), kind;\n\n  if (this.isLet(context)) {\n    starttype = types$1._var;\n    kind = \"let\";\n  }\n\n  // Most types of statements are recognized by the keyword they\n  // start with. Many are trivial to parse, some require a bit of\n  // complexity.\n\n  switch (starttype) {\n  case types$1._break: case types$1._continue: return this.parseBreakContinueStatement(node, starttype.keyword)\n  case types$1._debugger: return this.parseDebuggerStatement(node)\n  case types$1._do: return this.parseDoStatement(node)\n  case types$1._for: return this.parseForStatement(node)\n  case types$1._function:\n    // Function as sole body of either an if statement or a labeled statement\n    // works, but not when it is part of a labeled statement that is the sole\n    // body of an if statement.\n    if ((context && (this.strict || context !== \"if\" && context !== \"label\")) && this.options.ecmaVersion >= 6) { this.unexpected(); }\n    return this.parseFunctionStatement(node, false, !context)\n  case types$1._class:\n    if (context) { this.unexpected(); }\n    return this.parseClass(node, true)\n  case types$1._if: return this.parseIfStatement(node)\n  case types$1._return: return this.parseReturnStatement(node)\n  case types$1._switch: return this.parseSwitchStatement(node)\n  case types$1._throw: return this.parseThrowStatement(node)\n  case types$1._try: return this.parseTryStatement(node)\n  case types$1._const: case types$1._var:\n    kind = kind || this.value;\n    if (context && kind !== \"var\") { this.unexpected(); }\n    return this.parseVarStatement(node, kind)\n  case types$1._while: return this.parseWhileStatement(node)\n  case types$1._with: return this.parseWithStatement(node)\n  case types$1.braceL: return this.parseBlock(true, node)\n  case types$1.semi: return this.parseEmptyStatement(node)\n  case types$1._export:\n  case types$1._import:\n    if (this.options.ecmaVersion > 10 && starttype === types$1._import) {\n      skipWhiteSpace.lastIndex = this.pos;\n      var skip = skipWhiteSpace.exec(this.input);\n      var next = this.pos + skip[0].length, nextCh = this.input.charCodeAt(next);\n      if (nextCh === 40 || nextCh === 46) // '(' or '.'\n        { return this.parseExpressionStatement(node, this.parseExpression()) }\n    }\n\n    if (!this.options.allowImportExportEverywhere) {\n      if (!topLevel)\n        { this.raise(this.start, \"'import' and 'export' may only appear at the top level\"); }\n      if (!this.inModule)\n        { this.raise(this.start, \"'import' and 'export' may appear only with 'sourceType: module'\"); }\n    }\n    return starttype === types$1._import ? this.parseImport(node) : this.parseExport(node, exports)\n\n    // If the statement does not start with a statement keyword or a\n    // brace, it's an ExpressionStatement or LabeledStatement. We\n    // simply start parsing an expression, and afterwards, if the\n    // next token is a colon and the expression was a simple\n    // Identifier node, we switch to interpreting it as a label.\n  default:\n    if (this.isAsyncFunction()) {\n      if (context) { this.unexpected(); }\n      this.next();\n      return this.parseFunctionStatement(node, true, !context)\n    }\n\n    var usingKind = this.isAwaitUsing(false) ? \"await using\" : this.isUsing(false) ? \"using\" : null;\n    if (usingKind) {\n      if (topLevel && this.options.sourceType === \"script\") {\n        this.raise(this.start, \"Using declaration cannot appear in the top level when source type is `script`\");\n      }\n      if (usingKind === \"await using\") {\n        if (!this.canAwait) {\n          this.raise(this.start, \"Await using cannot appear outside of async function\");\n        }\n        this.next();\n      }\n      this.next();\n      this.parseVar(node, false, usingKind);\n      this.semicolon();\n      return this.finishNode(node, \"VariableDeclaration\")\n    }\n\n    var maybeName = this.value, expr = this.parseExpression();\n    if (starttype === types$1.name && expr.type === \"Identifier\" && this.eat(types$1.colon))\n      { return this.parseLabeledStatement(node, maybeName, expr, context) }\n    else { return this.parseExpressionStatement(node, expr) }\n  }\n};\n\npp$8.parseBreakContinueStatement = function(node, keyword) {\n  var isBreak = keyword === \"break\";\n  this.next();\n  if (this.eat(types$1.semi) || this.insertSemicolon()) { node.label = null; }\n  else if (this.type !== types$1.name) { this.unexpected(); }\n  else {\n    node.label = this.parseIdent();\n    this.semicolon();\n  }\n\n  // Verify that there is an actual destination to break or\n  // continue to.\n  var i = 0;\n  for (; i < this.labels.length; ++i) {\n    var lab = this.labels[i];\n    if (node.label == null || lab.name === node.label.name) {\n      if (lab.kind != null && (isBreak || lab.kind === \"loop\")) { break }\n      if (node.label && isBreak) { break }\n    }\n  }\n  if (i === this.labels.length) { this.raise(node.start, \"Unsyntactic \" + keyword); }\n  return this.finishNode(node, isBreak ? \"BreakStatement\" : \"ContinueStatement\")\n};\n\npp$8.parseDebuggerStatement = function(node) {\n  this.next();\n  this.semicolon();\n  return this.finishNode(node, \"DebuggerStatement\")\n};\n\npp$8.parseDoStatement = function(node) {\n  this.next();\n  this.labels.push(loopLabel);\n  node.body = this.parseStatement(\"do\");\n  this.labels.pop();\n  this.expect(types$1._while);\n  node.test = this.parseParenExpression();\n  if (this.options.ecmaVersion >= 6)\n    { this.eat(types$1.semi); }\n  else\n    { this.semicolon(); }\n  return this.finishNode(node, \"DoWhileStatement\")\n};\n\n// Disambiguating between a `for` and a `for`/`in` or `for`/`of`\n// loop is non-trivial. Basically, we have to parse the init `var`\n// statement or expression, disallowing the `in` operator (see\n// the second parameter to `parseExpression`), and then check\n// whether the next token is `in` or `of`. When there is no init\n// part (semicolon immediately after the opening parenthesis), it\n// is a regular `for` loop.\n\npp$8.parseForStatement = function(node) {\n  this.next();\n  var awaitAt = (this.options.ecmaVersion >= 9 && this.canAwait && this.eatContextual(\"await\")) ? this.lastTokStart : -1;\n  this.labels.push(loopLabel);\n  this.enterScope(0);\n  this.expect(types$1.parenL);\n  if (this.type === types$1.semi) {\n    if (awaitAt > -1) { this.unexpected(awaitAt); }\n    return this.parseFor(node, null)\n  }\n  var isLet = this.isLet();\n  if (this.type === types$1._var || this.type === types$1._const || isLet) {\n    var init$1 = this.startNode(), kind = isLet ? \"let\" : this.value;\n    this.next();\n    this.parseVar(init$1, true, kind);\n    this.finishNode(init$1, \"VariableDeclaration\");\n    return this.parseForAfterInit(node, init$1, awaitAt)\n  }\n  var startsWithLet = this.isContextual(\"let\"), isForOf = false;\n\n  var usingKind = this.isUsing(true) ? \"using\" : this.isAwaitUsing(true) ? \"await using\" : null;\n  if (usingKind) {\n    var init$2 = this.startNode();\n    this.next();\n    if (usingKind === \"await using\") { this.next(); }\n    this.parseVar(init$2, true, usingKind);\n    this.finishNode(init$2, \"VariableDeclaration\");\n    return this.parseForAfterInit(node, init$2, awaitAt)\n  }\n  var containsEsc = this.containsEsc;\n  var refDestructuringErrors = new DestructuringErrors;\n  var initPos = this.start;\n  var init = awaitAt > -1\n    ? this.parseExprSubscripts(refDestructuringErrors, \"await\")\n    : this.parseExpression(true, refDestructuringErrors);\n  if (this.type === types$1._in || (isForOf = this.options.ecmaVersion >= 6 && this.isContextual(\"of\"))) {\n    if (awaitAt > -1) { // implies `ecmaVersion >= 9` (see declaration of awaitAt)\n      if (this.type === types$1._in) { this.unexpected(awaitAt); }\n      node.await = true;\n    } else if (isForOf && this.options.ecmaVersion >= 8) {\n      if (init.start === initPos && !containsEsc && init.type === \"Identifier\" && init.name === \"async\") { this.unexpected(); }\n      else if (this.options.ecmaVersion >= 9) { node.await = false; }\n    }\n    if (startsWithLet && isForOf) { this.raise(init.start, \"The left-hand side of a for-of loop may not start with 'let'.\"); }\n    this.toAssignable(init, false, refDestructuringErrors);\n    this.checkLValPattern(init);\n    return this.parseForIn(node, init)\n  } else {\n    this.checkExpressionErrors(refDestructuringErrors, true);\n  }\n  if (awaitAt > -1) { this.unexpected(awaitAt); }\n  return this.parseFor(node, init)\n};\n\n// Helper method to parse for loop after variable initialization\npp$8.parseForAfterInit = function(node, init, awaitAt) {\n  if ((this.type === types$1._in || (this.options.ecmaVersion >= 6 && this.isContextual(\"of\"))) && init.declarations.length === 1) {\n    if (this.options.ecmaVersion >= 9) {\n      if (this.type === types$1._in) {\n        if (awaitAt > -1) { this.unexpected(awaitAt); }\n      } else { node.await = awaitAt > -1; }\n    }\n    return this.parseForIn(node, init)\n  }\n  if (awaitAt > -1) { this.unexpected(awaitAt); }\n  return this.parseFor(node, init)\n};\n\npp$8.parseFunctionStatement = function(node, isAsync, declarationPosition) {\n  this.next();\n  return this.parseFunction(node, FUNC_STATEMENT | (declarationPosition ? 0 : FUNC_HANGING_STATEMENT), false, isAsync)\n};\n\npp$8.parseIfStatement = function(node) {\n  this.next();\n  node.test = this.parseParenExpression();\n  // allow function declarations in branches, but only in non-strict mode\n  node.consequent = this.parseStatement(\"if\");\n  node.alternate = this.eat(types$1._else) ? this.parseStatement(\"if\") : null;\n  return this.finishNode(node, \"IfStatement\")\n};\n\npp$8.parseReturnStatement = function(node) {\n  if (!this.inFunction && !this.options.allowReturnOutsideFunction)\n    { this.raise(this.start, \"'return' outside of function\"); }\n  this.next();\n\n  // In `return` (and `break`/`continue`), the keywords with\n  // optional arguments, we eagerly look for a semicolon or the\n  // possibility to insert one.\n\n  if (this.eat(types$1.semi) || this.insertSemicolon()) { node.argument = null; }\n  else { node.argument = this.parseExpression(); this.semicolon(); }\n  return this.finishNode(node, \"ReturnStatement\")\n};\n\npp$8.parseSwitchStatement = function(node) {\n  this.next();\n  node.discriminant = this.parseParenExpression();\n  node.cases = [];\n  this.expect(types$1.braceL);\n  this.labels.push(switchLabel);\n  this.enterScope(0);\n\n  // Statements under must be grouped (by label) in SwitchCase\n  // nodes. `cur` is used to keep the node that we are currently\n  // adding statements to.\n\n  var cur;\n  for (var sawDefault = false; this.type !== types$1.braceR;) {\n    if (this.type === types$1._case || this.type === types$1._default) {\n      var isCase = this.type === types$1._case;\n      if (cur) { this.finishNode(cur, \"SwitchCase\"); }\n      node.cases.push(cur = this.startNode());\n      cur.consequent = [];\n      this.next();\n      if (isCase) {\n        cur.test = this.parseExpression();\n      } else {\n        if (sawDefault) { this.raiseRecoverable(this.lastTokStart, \"Multiple default clauses\"); }\n        sawDefault = true;\n        cur.test = null;\n      }\n      this.expect(types$1.colon);\n    } else {\n      if (!cur) { this.unexpected(); }\n      cur.consequent.push(this.parseStatement(null));\n    }\n  }\n  this.exitScope();\n  if (cur) { this.finishNode(cur, \"SwitchCase\"); }\n  this.next(); // Closing brace\n  this.labels.pop();\n  return this.finishNode(node, \"SwitchStatement\")\n};\n\npp$8.parseThrowStatement = function(node) {\n  this.next();\n  if (lineBreak.test(this.input.slice(this.lastTokEnd, this.start)))\n    { this.raise(this.lastTokEnd, \"Illegal newline after throw\"); }\n  node.argument = this.parseExpression();\n  this.semicolon();\n  return this.finishNode(node, \"ThrowStatement\")\n};\n\n// Reused empty array added for node fields that are always empty.\n\nvar empty$1 = [];\n\npp$8.parseCatchClauseParam = function() {\n  var param = this.parseBindingAtom();\n  var simple = param.type === \"Identifier\";\n  this.enterScope(simple ? SCOPE_SIMPLE_CATCH : 0);\n  this.checkLValPattern(param, simple ? BIND_SIMPLE_CATCH : BIND_LEXICAL);\n  this.expect(types$1.parenR);\n\n  return param\n};\n\npp$8.parseTryStatement = function(node) {\n  this.next();\n  node.block = this.parseBlock();\n  node.handler = null;\n  if (this.type === types$1._catch) {\n    var clause = this.startNode();\n    this.next();\n    if (this.eat(types$1.parenL)) {\n      clause.param = this.parseCatchClauseParam();\n    } else {\n      if (this.options.ecmaVersion < 10) { this.unexpected(); }\n      clause.param = null;\n      this.enterScope(0);\n    }\n    clause.body = this.parseBlock(false);\n    this.exitScope();\n    node.handler = this.finishNode(clause, \"CatchClause\");\n  }\n  node.finalizer = this.eat(types$1._finally) ? this.parseBlock() : null;\n  if (!node.handler && !node.finalizer)\n    { this.raise(node.start, \"Missing catch or finally clause\"); }\n  return this.finishNode(node, \"TryStatement\")\n};\n\npp$8.parseVarStatement = function(node, kind, allowMissingInitializer) {\n  this.next();\n  this.parseVar(node, false, kind, allowMissingInitializer);\n  this.semicolon();\n  return this.finishNode(node, \"VariableDeclaration\")\n};\n\npp$8.parseWhileStatement = function(node) {\n  this.next();\n  node.test = this.parseParenExpression();\n  this.labels.push(loopLabel);\n  node.body = this.parseStatement(\"while\");\n  this.labels.pop();\n  return this.finishNode(node, \"WhileStatement\")\n};\n\npp$8.parseWithStatement = function(node) {\n  if (this.strict) { this.raise(this.start, \"'with' in strict mode\"); }\n  this.next();\n  node.object = this.parseParenExpression();\n  node.body = this.parseStatement(\"with\");\n  return this.finishNode(node, \"WithStatement\")\n};\n\npp$8.parseEmptyStatement = function(node) {\n  this.next();\n  return this.finishNode(node, \"EmptyStatement\")\n};\n\npp$8.parseLabeledStatement = function(node, maybeName, expr, context) {\n  for (var i$1 = 0, list = this.labels; i$1 < list.length; i$1 += 1)\n    {\n    var label = list[i$1];\n\n    if (label.name === maybeName)\n      { this.raise(expr.start, \"Label '\" + maybeName + \"' is already declared\");\n  } }\n  var kind = this.type.isLoop ? \"loop\" : this.type === types$1._switch ? \"switch\" : null;\n  for (var i = this.labels.length - 1; i >= 0; i--) {\n    var label$1 = this.labels[i];\n    if (label$1.statementStart === node.start) {\n      // Update information about previous labels on this node\n      label$1.statementStart = this.start;\n      label$1.kind = kind;\n    } else { break }\n  }\n  this.labels.push({name: maybeName, kind: kind, statementStart: this.start});\n  node.body = this.parseStatement(context ? context.indexOf(\"label\") === -1 ? context + \"label\" : context : \"label\");\n  this.labels.pop();\n  node.label = expr;\n  return this.finishNode(node, \"LabeledStatement\")\n};\n\npp$8.parseExpressionStatement = function(node, expr) {\n  node.expression = expr;\n  this.semicolon();\n  return this.finishNode(node, \"ExpressionStatement\")\n};\n\n// Parse a semicolon-enclosed block of statements, handling `\"use\n// strict\"` declarations when `allowStrict` is true (used for\n// function bodies).\n\npp$8.parseBlock = function(createNewLexicalScope, node, exitStrict) {\n  if ( createNewLexicalScope === void 0 ) createNewLexicalScope = true;\n  if ( node === void 0 ) node = this.startNode();\n\n  node.body = [];\n  this.expect(types$1.braceL);\n  if (createNewLexicalScope) { this.enterScope(0); }\n  while (this.type !== types$1.braceR) {\n    var stmt = this.parseStatement(null);\n    node.body.push(stmt);\n  }\n  if (exitStrict) { this.strict = false; }\n  this.next();\n  if (createNewLexicalScope) { this.exitScope(); }\n  return this.finishNode(node, \"BlockStatement\")\n};\n\n// Parse a regular `for` loop. The disambiguation code in\n// `parseStatement` will already have parsed the init statement or\n// expression.\n\npp$8.parseFor = function(node, init) {\n  node.init = init;\n  this.expect(types$1.semi);\n  node.test = this.type === types$1.semi ? null : this.parseExpression();\n  this.expect(types$1.semi);\n  node.update = this.type === types$1.parenR ? null : this.parseExpression();\n  this.expect(types$1.parenR);\n  node.body = this.parseStatement(\"for\");\n  this.exitScope();\n  this.labels.pop();\n  return this.finishNode(node, \"ForStatement\")\n};\n\n// Parse a `for`/`in` and `for`/`of` loop, which are almost\n// same from parser's perspective.\n\npp$8.parseForIn = function(node, init) {\n  var isForIn = this.type === types$1._in;\n  this.next();\n\n  if (\n    init.type === \"VariableDeclaration\" &&\n    init.declarations[0].init != null &&\n    (\n      !isForIn ||\n      this.options.ecmaVersion < 8 ||\n      this.strict ||\n      init.kind !== \"var\" ||\n      init.declarations[0].id.type !== \"Identifier\"\n    )\n  ) {\n    this.raise(\n      init.start,\n      ((isForIn ? \"for-in\" : \"for-of\") + \" loop variable declaration may not have an initializer\")\n    );\n  }\n  node.left = init;\n  node.right = isForIn ? this.parseExpression() : this.parseMaybeAssign();\n  this.expect(types$1.parenR);\n  node.body = this.parseStatement(\"for\");\n  this.exitScope();\n  this.labels.pop();\n  return this.finishNode(node, isForIn ? \"ForInStatement\" : \"ForOfStatement\")\n};\n\n// Parse a list of variable declarations.\n\npp$8.parseVar = function(node, isFor, kind, allowMissingInitializer) {\n  node.declarations = [];\n  node.kind = kind;\n  for (;;) {\n    var decl = this.startNode();\n    this.parseVarId(decl, kind);\n    if (this.eat(types$1.eq)) {\n      decl.init = this.parseMaybeAssign(isFor);\n    } else if (!allowMissingInitializer && kind === \"const\" && !(this.type === types$1._in || (this.options.ecmaVersion >= 6 && this.isContextual(\"of\")))) {\n      this.unexpected();\n    } else if (!allowMissingInitializer && (kind === \"using\" || kind === \"await using\") && this.options.ecmaVersion >= 17 && this.type !== types$1._in && !this.isContextual(\"of\")) {\n      this.raise(this.lastTokEnd, (\"Missing initializer in \" + kind + \" declaration\"));\n    } else if (!allowMissingInitializer && decl.id.type !== \"Identifier\" && !(isFor && (this.type === types$1._in || this.isContextual(\"of\")))) {\n      this.raise(this.lastTokEnd, \"Complex binding patterns require an initialization value\");\n    } else {\n      decl.init = null;\n    }\n    node.declarations.push(this.finishNode(decl, \"VariableDeclarator\"));\n    if (!this.eat(types$1.comma)) { break }\n  }\n  return node\n};\n\npp$8.parseVarId = function(decl, kind) {\n  decl.id = kind === \"using\" || kind === \"await using\"\n    ? this.parseIdent()\n    : this.parseBindingAtom();\n\n  this.checkLValPattern(decl.id, kind === \"var\" ? BIND_VAR : BIND_LEXICAL, false);\n};\n\nvar FUNC_STATEMENT = 1, FUNC_HANGING_STATEMENT = 2, FUNC_NULLABLE_ID = 4;\n\n// Parse a function declaration or literal (depending on the\n// `statement & FUNC_STATEMENT`).\n\n// Remove `allowExpressionBody` for 7.0.0, as it is only called with false\npp$8.parseFunction = function(node, statement, allowExpressionBody, isAsync, forInit) {\n  this.initFunction(node);\n  if (this.options.ecmaVersion >= 9 || this.options.ecmaVersion >= 6 && !isAsync) {\n    if (this.type === types$1.star && (statement & FUNC_HANGING_STATEMENT))\n      { this.unexpected(); }\n    node.generator = this.eat(types$1.star);\n  }\n  if (this.options.ecmaVersion >= 8)\n    { node.async = !!isAsync; }\n\n  if (statement & FUNC_STATEMENT) {\n    node.id = (statement & FUNC_NULLABLE_ID) && this.type !== types$1.name ? null : this.parseIdent();\n    if (node.id && !(statement & FUNC_HANGING_STATEMENT))\n      // If it is a regular function declaration in sloppy mode, then it is\n      // subject to Annex B semantics (BIND_FUNCTION). Otherwise, the binding\n      // mode depends on properties of the current scope (see\n      // treatFunctionsAsVar).\n      { this.checkLValSimple(node.id, (this.strict || node.generator || node.async) ? this.treatFunctionsAsVar ? BIND_VAR : BIND_LEXICAL : BIND_FUNCTION); }\n  }\n\n  var oldYieldPos = this.yieldPos, oldAwaitPos = this.awaitPos, oldAwaitIdentPos = this.awaitIdentPos;\n  this.yieldPos = 0;\n  this.awaitPos = 0;\n  this.awaitIdentPos = 0;\n  this.enterScope(functionFlags(node.async, node.generator));\n\n  if (!(statement & FUNC_STATEMENT))\n    { node.id = this.type === types$1.name ? this.parseIdent() : null; }\n\n  this.parseFunctionParams(node);\n  this.parseFunctionBody(node, allowExpressionBody, false, forInit);\n\n  this.yieldPos = oldYieldPos;\n  this.awaitPos = oldAwaitPos;\n  this.awaitIdentPos = oldAwaitIdentPos;\n  return this.finishNode(node, (statement & FUNC_STATEMENT) ? \"FunctionDeclaration\" : \"FunctionExpression\")\n};\n\npp$8.parseFunctionParams = function(node) {\n  this.expect(types$1.parenL);\n  node.params = this.parseBindingList(types$1.parenR, false, this.options.ecmaVersion >= 8);\n  this.checkYieldAwaitInDefaultParams();\n};\n\n// Parse a class declaration or literal (depending on the\n// `isStatement` parameter).\n\npp$8.parseClass = function(node, isStatement) {\n  this.next();\n\n  // ecma-262 14.6 Class Definitions\n  // A class definition is always strict mode code.\n  var oldStrict = this.strict;\n  this.strict = true;\n\n  this.parseClassId(node, isStatement);\n  this.parseClassSuper(node);\n  var privateNameMap = this.enterClassBody();\n  var classBody = this.startNode();\n  var hadConstructor = false;\n  classBody.body = [];\n  this.expect(types$1.braceL);\n  while (this.type !== types$1.braceR) {\n    var element = this.parseClassElement(node.superClass !== null);\n    if (element) {\n      classBody.body.push(element);\n      if (element.type === \"MethodDefinition\" && element.kind === \"constructor\") {\n        if (hadConstructor) { this.raiseRecoverable(element.start, \"Duplicate constructor in the same class\"); }\n        hadConstructor = true;\n      } else if (element.key && element.key.type === \"PrivateIdentifier\" && isPrivateNameConflicted(privateNameMap, element)) {\n        this.raiseRecoverable(element.key.start, (\"Identifier '#\" + (element.key.name) + \"' has already been declared\"));\n      }\n    }\n  }\n  this.strict = oldStrict;\n  this.next();\n  node.body = this.finishNode(classBody, \"ClassBody\");\n  this.exitClassBody();\n  return this.finishNode(node, isStatement ? \"ClassDeclaration\" : \"ClassExpression\")\n};\n\npp$8.parseClassElement = function(constructorAllowsSuper) {\n  if (this.eat(types$1.semi)) { return null }\n\n  var ecmaVersion = this.options.ecmaVersion;\n  var node = this.startNode();\n  var keyName = \"\";\n  var isGenerator = false;\n  var isAsync = false;\n  var kind = \"method\";\n  var isStatic = false;\n\n  if (this.eatContextual(\"static\")) {\n    // Parse static init block\n    if (ecmaVersion >= 13 && this.eat(types$1.braceL)) {\n      this.parseClassStaticBlock(node);\n      return node\n    }\n    if (this.isClassElementNameStart() || this.type === types$1.star) {\n      isStatic = true;\n    } else {\n      keyName = \"static\";\n    }\n  }\n  node.static = isStatic;\n  if (!keyName && ecmaVersion >= 8 && this.eatContextual(\"async\")) {\n    if ((this.isClassElementNameStart() || this.type === types$1.star) && !this.canInsertSemicolon()) {\n      isAsync = true;\n    } else {\n      keyName = \"async\";\n    }\n  }\n  if (!keyName && (ecmaVersion >= 9 || !isAsync) && this.eat(types$1.star)) {\n    isGenerator = true;\n  }\n  if (!keyName && !isAsync && !isGenerator) {\n    var lastValue = this.value;\n    if (this.eatContextual(\"get\") || this.eatContextual(\"set\")) {\n      if (this.isClassElementNameStart()) {\n        kind = lastValue;\n      } else {\n        keyName = lastValue;\n      }\n    }\n  }\n\n  // Parse element name\n  if (keyName) {\n    // 'async', 'get', 'set', or 'static' were not a keyword contextually.\n    // The last token is any of those. Make it the element name.\n    node.computed = false;\n    node.key = this.startNodeAt(this.lastTokStart, this.lastTokStartLoc);\n    node.key.name = keyName;\n    this.finishNode(node.key, \"Identifier\");\n  } else {\n    this.parseClassElementName(node);\n  }\n\n  // Parse element value\n  if (ecmaVersion < 13 || this.type === types$1.parenL || kind !== \"method\" || isGenerator || isAsync) {\n    var isConstructor = !node.static && checkKeyName(node, \"constructor\");\n    var allowsDirectSuper = isConstructor && constructorAllowsSuper;\n    // Couldn't move this check into the 'parseClassMethod' method for backward compatibility.\n    if (isConstructor && kind !== \"method\") { this.raise(node.key.start, \"Constructor can't have get/set modifier\"); }\n    node.kind = isConstructor ? \"constructor\" : kind;\n    this.parseClassMethod(node, isGenerator, isAsync, allowsDirectSuper);\n  } else {\n    this.parseClassField(node);\n  }\n\n  return node\n};\n\npp$8.isClassElementNameStart = function() {\n  return (\n    this.type === types$1.name ||\n    this.type === types$1.privateId ||\n    this.type === types$1.num ||\n    this.type === types$1.string ||\n    this.type === types$1.bracketL ||\n    this.type.keyword\n  )\n};\n\npp$8.parseClassElementName = function(element) {\n  if (this.type === types$1.privateId) {\n    if (this.value === \"constructor\") {\n      this.raise(this.start, \"Classes can't have an element named '#constructor'\");\n    }\n    element.computed = false;\n    element.key = this.parsePrivateIdent();\n  } else {\n    this.parsePropertyName(element);\n  }\n};\n\npp$8.parseClassMethod = function(method, isGenerator, isAsync, allowsDirectSuper) {\n  // Check key and flags\n  var key = method.key;\n  if (method.kind === \"constructor\") {\n    if (isGenerator) { this.raise(key.start, \"Constructor can't be a generator\"); }\n    if (isAsync) { this.raise(key.start, \"Constructor can't be an async method\"); }\n  } else if (method.static && checkKeyName(method, \"prototype\")) {\n    this.raise(key.start, \"Classes may not have a static property named prototype\");\n  }\n\n  // Parse value\n  var value = method.value = this.parseMethod(isGenerator, isAsync, allowsDirectSuper);\n\n  // Check value\n  if (method.kind === \"get\" && value.params.length !== 0)\n    { this.raiseRecoverable(value.start, \"getter should have no params\"); }\n  if (method.kind === \"set\" && value.params.length !== 1)\n    { this.raiseRecoverable(value.start, \"setter should have exactly one param\"); }\n  if (method.kind === \"set\" && value.params[0].type === \"RestElement\")\n    { this.raiseRecoverable(value.params[0].start, \"Setter cannot use rest params\"); }\n\n  return this.finishNode(method, \"MethodDefinition\")\n};\n\npp$8.parseClassField = function(field) {\n  if (checkKeyName(field, \"constructor\")) {\n    this.raise(field.key.start, \"Classes can't have a field named 'constructor'\");\n  } else if (field.static && checkKeyName(field, \"prototype\")) {\n    this.raise(field.key.start, \"Classes can't have a static field named 'prototype'\");\n  }\n\n  if (this.eat(types$1.eq)) {\n    // To raise SyntaxError if 'arguments' exists in the initializer.\n    this.enterScope(SCOPE_CLASS_FIELD_INIT | SCOPE_SUPER);\n    field.value = this.parseMaybeAssign();\n    this.exitScope();\n  } else {\n    field.value = null;\n  }\n  this.semicolon();\n\n  return this.finishNode(field, \"PropertyDefinition\")\n};\n\npp$8.parseClassStaticBlock = function(node) {\n  node.body = [];\n\n  var oldLabels = this.labels;\n  this.labels = [];\n  this.enterScope(SCOPE_CLASS_STATIC_BLOCK | SCOPE_SUPER);\n  while (this.type !== types$1.braceR) {\n    var stmt = this.parseStatement(null);\n    node.body.push(stmt);\n  }\n  this.next();\n  this.exitScope();\n  this.labels = oldLabels;\n\n  return this.finishNode(node, \"StaticBlock\")\n};\n\npp$8.parseClassId = function(node, isStatement) {\n  if (this.type === types$1.name) {\n    node.id = this.parseIdent();\n    if (isStatement)\n      { this.checkLValSimple(node.id, BIND_LEXICAL, false); }\n  } else {\n    if (isStatement === true)\n      { this.unexpected(); }\n    node.id = null;\n  }\n};\n\npp$8.parseClassSuper = function(node) {\n  node.superClass = this.eat(types$1._extends) ? this.parseExprSubscripts(null, false) : null;\n};\n\npp$8.enterClassBody = function() {\n  var element = {declared: Object.create(null), used: []};\n  this.privateNameStack.push(element);\n  return element.declared\n};\n\npp$8.exitClassBody = function() {\n  var ref = this.privateNameStack.pop();\n  var declared = ref.declared;\n  var used = ref.used;\n  if (!this.options.checkPrivateFields) { return }\n  var len = this.privateNameStack.length;\n  var parent = len === 0 ? null : this.privateNameStack[len - 1];\n  for (var i = 0; i < used.length; ++i) {\n    var id = used[i];\n    if (!hasOwn(declared, id.name)) {\n      if (parent) {\n        parent.used.push(id);\n      } else {\n        this.raiseRecoverable(id.start, (\"Private field '#\" + (id.name) + \"' must be declared in an enclosing class\"));\n      }\n    }\n  }\n};\n\nfunction isPrivateNameConflicted(privateNameMap, element) {\n  var name = element.key.name;\n  var curr = privateNameMap[name];\n\n  var next = \"true\";\n  if (element.type === \"MethodDefinition\" && (element.kind === \"get\" || element.kind === \"set\")) {\n    next = (element.static ? \"s\" : \"i\") + element.kind;\n  }\n\n  // `class { get #a(){}; static set #a(_){} }` is also conflict.\n  if (\n    curr === \"iget\" && next === \"iset\" ||\n    curr === \"iset\" && next === \"iget\" ||\n    curr === \"sget\" && next === \"sset\" ||\n    curr === \"sset\" && next === \"sget\"\n  ) {\n    privateNameMap[name] = \"true\";\n    return false\n  } else if (!curr) {\n    privateNameMap[name] = next;\n    return false\n  } else {\n    return true\n  }\n}\n\nfunction checkKeyName(node, name) {\n  var computed = node.computed;\n  var key = node.key;\n  return !computed && (\n    key.type === \"Identifier\" && key.name === name ||\n    key.type === \"Literal\" && key.value === name\n  )\n}\n\n// Parses module export declaration.\n\npp$8.parseExportAllDeclaration = function(node, exports) {\n  if (this.options.ecmaVersion >= 11) {\n    if (this.eatContextual(\"as\")) {\n      node.exported = this.parseModuleExportName();\n      this.checkExport(exports, node.exported, this.lastTokStart);\n    } else {\n      node.exported = null;\n    }\n  }\n  this.expectContextual(\"from\");\n  if (this.type !== types$1.string) { this.unexpected(); }\n  node.source = this.parseExprAtom();\n  if (this.options.ecmaVersion >= 16)\n    { node.attributes = this.parseWithClause(); }\n  this.semicolon();\n  return this.finishNode(node, \"ExportAllDeclaration\")\n};\n\npp$8.parseExport = function(node, exports) {\n  this.next();\n  // export * from '...'\n  if (this.eat(types$1.star)) {\n    return this.parseExportAllDeclaration(node, exports)\n  }\n  if (this.eat(types$1._default)) { // export default ...\n    this.checkExport(exports, \"default\", this.lastTokStart);\n    node.declaration = this.parseExportDefaultDeclaration();\n    return this.finishNode(node, \"ExportDefaultDeclaration\")\n  }\n  // export var|const|let|function|class ...\n  if (this.shouldParseExportStatement()) {\n    node.declaration = this.parseExportDeclaration(node);\n    if (node.declaration.type === \"VariableDeclaration\")\n      { this.checkVariableExport(exports, node.declaration.declarations); }\n    else\n      { this.checkExport(exports, node.declaration.id, node.declaration.id.start); }\n    node.specifiers = [];\n    node.source = null;\n    if (this.options.ecmaVersion >= 16)\n      { node.attributes = []; }\n  } else { // export { x, y as z } [from '...']\n    node.declaration = null;\n    node.specifiers = this.parseExportSpecifiers(exports);\n    if (this.eatContextual(\"from\")) {\n      if (this.type !== types$1.string) { this.unexpected(); }\n      node.source = this.parseExprAtom();\n      if (this.options.ecmaVersion >= 16)\n        { node.attributes = this.parseWithClause(); }\n    } else {\n      for (var i = 0, list = node.specifiers; i < list.length; i += 1) {\n        // check for keywords used as local names\n        var spec = list[i];\n\n        this.checkUnreserved(spec.local);\n        // check if export is defined\n        this.checkLocalExport(spec.local);\n\n        if (spec.local.type === \"Literal\") {\n          this.raise(spec.local.start, \"A string literal cannot be used as an exported binding without `from`.\");\n        }\n      }\n\n      node.source = null;\n      if (this.options.ecmaVersion >= 16)\n        { node.attributes = []; }\n    }\n    this.semicolon();\n  }\n  return this.finishNode(node, \"ExportNamedDeclaration\")\n};\n\npp$8.parseExportDeclaration = function(node) {\n  return this.parseStatement(null)\n};\n\npp$8.parseExportDefaultDeclaration = function() {\n  var isAsync;\n  if (this.type === types$1._function || (isAsync = this.isAsyncFunction())) {\n    var fNode = this.startNode();\n    this.next();\n    if (isAsync) { this.next(); }\n    return this.parseFunction(fNode, FUNC_STATEMENT | FUNC_NULLABLE_ID, false, isAsync)\n  } else if (this.type === types$1._class) {\n    var cNode = this.startNode();\n    return this.parseClass(cNode, \"nullableID\")\n  } else {\n    var declaration = this.parseMaybeAssign();\n    this.semicolon();\n    return declaration\n  }\n};\n\npp$8.checkExport = function(exports, name, pos) {\n  if (!exports) { return }\n  if (typeof name !== \"string\")\n    { name = name.type === \"Identifier\" ? name.name : name.value; }\n  if (hasOwn(exports, name))\n    { this.raiseRecoverable(pos, \"Duplicate export '\" + name + \"'\"); }\n  exports[name] = true;\n};\n\npp$8.checkPatternExport = function(exports, pat) {\n  var type = pat.type;\n  if (type === \"Identifier\")\n    { this.checkExport(exports, pat, pat.start); }\n  else if (type === \"ObjectPattern\")\n    { for (var i = 0, list = pat.properties; i < list.length; i += 1)\n      {\n        var prop = list[i];\n\n        this.checkPatternExport(exports, prop);\n      } }\n  else if (type === \"ArrayPattern\")\n    { for (var i$1 = 0, list$1 = pat.elements; i$1 < list$1.length; i$1 += 1) {\n      var elt = list$1[i$1];\n\n        if (elt) { this.checkPatternExport(exports, elt); }\n    } }\n  else if (type === \"Property\")\n    { this.checkPatternExport(exports, pat.value); }\n  else if (type === \"AssignmentPattern\")\n    { this.checkPatternExport(exports, pat.left); }\n  else if (type === \"RestElement\")\n    { this.checkPatternExport(exports, pat.argument); }\n};\n\npp$8.checkVariableExport = function(exports, decls) {\n  if (!exports) { return }\n  for (var i = 0, list = decls; i < list.length; i += 1)\n    {\n    var decl = list[i];\n\n    this.checkPatternExport(exports, decl.id);\n  }\n};\n\npp$8.shouldParseExportStatement = function() {\n  return this.type.keyword === \"var\" ||\n    this.type.keyword === \"const\" ||\n    this.type.keyword === \"class\" ||\n    this.type.keyword === \"function\" ||\n    this.isLet() ||\n    this.isAsyncFunction()\n};\n\n// Parses a comma-separated list of module exports.\n\npp$8.parseExportSpecifier = function(exports) {\n  var node = this.startNode();\n  node.local = this.parseModuleExportName();\n\n  node.exported = this.eatContextual(\"as\") ? this.parseModuleExportName() : node.local;\n  this.checkExport(\n    exports,\n    node.exported,\n    node.exported.start\n  );\n\n  return this.finishNode(node, \"ExportSpecifier\")\n};\n\npp$8.parseExportSpecifiers = function(exports) {\n  var nodes = [], first = true;\n  // export { x, y as z } [from '...']\n  this.expect(types$1.braceL);\n  while (!this.eat(types$1.braceR)) {\n    if (!first) {\n      this.expect(types$1.comma);\n      if (this.afterTrailingComma(types$1.braceR)) { break }\n    } else { first = false; }\n\n    nodes.push(this.parseExportSpecifier(exports));\n  }\n  return nodes\n};\n\n// Parses import declaration.\n\npp$8.parseImport = function(node) {\n  this.next();\n\n  // import '...'\n  if (this.type === types$1.string) {\n    node.specifiers = empty$1;\n    node.source = this.parseExprAtom();\n  } else {\n    node.specifiers = this.parseImportSpecifiers();\n    this.expectContextual(\"from\");\n    node.source = this.type === types$1.string ? this.parseExprAtom() : this.unexpected();\n  }\n  if (this.options.ecmaVersion >= 16)\n    { node.attributes = this.parseWithClause(); }\n  this.semicolon();\n  return this.finishNode(node, \"ImportDeclaration\")\n};\n\n// Parses a comma-separated list of module imports.\n\npp$8.parseImportSpecifier = function() {\n  var node = this.startNode();\n  node.imported = this.parseModuleExportName();\n\n  if (this.eatContextual(\"as\")) {\n    node.local = this.parseIdent();\n  } else {\n    this.checkUnreserved(node.imported);\n    node.local = node.imported;\n  }\n  this.checkLValSimple(node.local, BIND_LEXICAL);\n\n  return this.finishNode(node, \"ImportSpecifier\")\n};\n\npp$8.parseImportDefaultSpecifier = function() {\n  // import defaultObj, { x, y as z } from '...'\n  var node = this.startNode();\n  node.local = this.parseIdent();\n  this.checkLValSimple(node.local, BIND_LEXICAL);\n  return this.finishNode(node, \"ImportDefaultSpecifier\")\n};\n\npp$8.parseImportNamespaceSpecifier = function() {\n  var node = this.startNode();\n  this.next();\n  this.expectContextual(\"as\");\n  node.local = this.parseIdent();\n  this.checkLValSimple(node.local, BIND_LEXICAL);\n  return this.finishNode(node, \"ImportNamespaceSpecifier\")\n};\n\npp$8.parseImportSpecifiers = function() {\n  var nodes = [], first = true;\n  if (this.type === types$1.name) {\n    nodes.push(this.parseImportDefaultSpecifier());\n    if (!this.eat(types$1.comma)) { return nodes }\n  }\n  if (this.type === types$1.star) {\n    nodes.push(this.parseImportNamespaceSpecifier());\n    return nodes\n  }\n  this.expect(types$1.braceL);\n  while (!this.eat(types$1.braceR)) {\n    if (!first) {\n      this.expect(types$1.comma);\n      if (this.afterTrailingComma(types$1.braceR)) { break }\n    } else { first = false; }\n\n    nodes.push(this.parseImportSpecifier());\n  }\n  return nodes\n};\n\npp$8.parseWithClause = function() {\n  var nodes = [];\n  if (!this.eat(types$1._with)) {\n    return nodes\n  }\n  this.expect(types$1.braceL);\n  var attributeKeys = {};\n  var first = true;\n  while (!this.eat(types$1.braceR)) {\n    if (!first) {\n      this.expect(types$1.comma);\n      if (this.afterTrailingComma(types$1.braceR)) { break }\n    } else { first = false; }\n\n    var attr = this.parseImportAttribute();\n    var keyName = attr.key.type === \"Identifier\" ? attr.key.name : attr.key.value;\n    if (hasOwn(attributeKeys, keyName))\n      { this.raiseRecoverable(attr.key.start, \"Duplicate attribute key '\" + keyName + \"'\"); }\n    attributeKeys[keyName] = true;\n    nodes.push(attr);\n  }\n  return nodes\n};\n\npp$8.parseImportAttribute = function() {\n  var node = this.startNode();\n  node.key = this.type === types$1.string ? this.parseExprAtom() : this.parseIdent(this.options.allowReserved !== \"never\");\n  this.expect(types$1.colon);\n  if (this.type !== types$1.string) {\n    this.unexpected();\n  }\n  node.value = this.parseExprAtom();\n  return this.finishNode(node, \"ImportAttribute\")\n};\n\npp$8.parseModuleExportName = function() {\n  if (this.options.ecmaVersion >= 13 && this.type === types$1.string) {\n    var stringLiteral = this.parseLiteral(this.value);\n    if (loneSurrogate.test(stringLiteral.value)) {\n      this.raise(stringLiteral.start, \"An export name cannot include a lone surrogate.\");\n    }\n    return stringLiteral\n  }\n  return this.parseIdent(true)\n};\n\n// Set `ExpressionStatement#directive` property for directive prologues.\npp$8.adaptDirectivePrologue = function(statements) {\n  for (var i = 0; i < statements.length && this.isDirectiveCandidate(statements[i]); ++i) {\n    statements[i].directive = statements[i].expression.raw.slice(1, -1);\n  }\n};\npp$8.isDirectiveCandidate = function(statement) {\n  return (\n    this.options.ecmaVersion >= 5 &&\n    statement.type === \"ExpressionStatement\" &&\n    statement.expression.type === \"Literal\" &&\n    typeof statement.expression.value === \"string\" &&\n    // Reject parenthesized strings.\n    (this.input[statement.start] === \"\\\"\" || this.input[statement.start] === \"'\")\n  )\n};\n\nvar pp$7 = Parser.prototype;\n\n// Convert existing expression atom to assignable pattern\n// if possible.\n\npp$7.toAssignable = function(node, isBinding, refDestructuringErrors) {\n  if (this.options.ecmaVersion >= 6 && node) {\n    switch (node.type) {\n    case \"Identifier\":\n      if (this.inAsync && node.name === \"await\")\n        { this.raise(node.start, \"Cannot use 'await' as identifier inside an async function\"); }\n      break\n\n    case \"ObjectPattern\":\n    case \"ArrayPattern\":\n    case \"AssignmentPattern\":\n    case \"RestElement\":\n      break\n\n    case \"ObjectExpression\":\n      node.type = \"ObjectPattern\";\n      if (refDestructuringErrors) { this.checkPatternErrors(refDestructuringErrors, true); }\n      for (var i = 0, list = node.properties; i < list.length; i += 1) {\n        var prop = list[i];\n\n      this.toAssignable(prop, isBinding);\n        // Early error:\n        //   AssignmentRestProperty[Yield, Await] :\n        //     `...` DestructuringAssignmentTarget[Yield, Await]\n        //\n        //   It is a Syntax Error if |DestructuringAssignmentTarget| is an |ArrayLiteral| or an |ObjectLiteral|.\n        if (\n          prop.type === \"RestElement\" &&\n          (prop.argument.type === \"ArrayPattern\" || prop.argument.type === \"ObjectPattern\")\n        ) {\n          this.raise(prop.argument.start, \"Unexpected token\");\n        }\n      }\n      break\n\n    case \"Property\":\n      // AssignmentProperty has type === \"Property\"\n      if (node.kind !== \"init\") { this.raise(node.key.start, \"Object pattern can't contain getter or setter\"); }\n      this.toAssignable(node.value, isBinding);\n      break\n\n    case \"ArrayExpression\":\n      node.type = \"ArrayPattern\";\n      if (refDestructuringErrors) { this.checkPatternErrors(refDestructuringErrors, true); }\n      this.toAssignableList(node.elements, isBinding);\n      break\n\n    case \"SpreadElement\":\n      node.type = \"RestElement\";\n      this.toAssignable(node.argument, isBinding);\n      if (node.argument.type === \"AssignmentPattern\")\n        { this.raise(node.argument.start, \"Rest elements cannot have a default value\"); }\n      break\n\n    case \"AssignmentExpression\":\n      if (node.operator !== \"=\") { this.raise(node.left.end, \"Only '=' operator can be used for specifying default value.\"); }\n      node.type = \"AssignmentPattern\";\n      delete node.operator;\n      this.toAssignable(node.left, isBinding);\n      break\n\n    case \"ParenthesizedExpression\":\n      this.toAssignable(node.expression, isBinding, refDestructuringErrors);\n      break\n\n    case \"ChainExpression\":\n      this.raiseRecoverable(node.start, \"Optional chaining cannot appear in left-hand side\");\n      break\n\n    case \"MemberExpression\":\n      if (!isBinding) { break }\n\n    default:\n      this.raise(node.start, \"Assigning to rvalue\");\n    }\n  } else if (refDestructuringErrors) { this.checkPatternErrors(refDestructuringErrors, true); }\n  return node\n};\n\n// Convert list of expression atoms to binding list.\n\npp$7.toAssignableList = function(exprList, isBinding) {\n  var end = exprList.length;\n  for (var i = 0; i < end; i++) {\n    var elt = exprList[i];\n    if (elt) { this.toAssignable(elt, isBinding); }\n  }\n  if (end) {\n    var last = exprList[end - 1];\n    if (this.options.ecmaVersion === 6 && isBinding && last && last.type === \"RestElement\" && last.argument.type !== \"Identifier\")\n      { this.unexpected(last.argument.start); }\n  }\n  return exprList\n};\n\n// Parses spread element.\n\npp$7.parseSpread = function(refDestructuringErrors) {\n  var node = this.startNode();\n  this.next();\n  node.argument = this.parseMaybeAssign(false, refDestructuringErrors);\n  return this.finishNode(node, \"SpreadElement\")\n};\n\npp$7.parseRestBinding = function() {\n  var node = this.startNode();\n  this.next();\n\n  // RestElement inside of a function parameter must be an identifier\n  if (this.options.ecmaVersion === 6 && this.type !== types$1.name)\n    { this.unexpected(); }\n\n  node.argument = this.parseBindingAtom();\n\n  return this.finishNode(node, \"RestElement\")\n};\n\n// Parses lvalue (assignable) atom.\n\npp$7.parseBindingAtom = function() {\n  if (this.options.ecmaVersion >= 6) {\n    switch (this.type) {\n    case types$1.bracketL:\n      var node = this.startNode();\n      this.next();\n      node.elements = this.parseBindingList(types$1.bracketR, true, true);\n      return this.finishNode(node, \"ArrayPattern\")\n\n    case types$1.braceL:\n      return this.parseObj(true)\n    }\n  }\n  return this.parseIdent()\n};\n\npp$7.parseBindingList = function(close, allowEmpty, allowTrailingComma, allowModifiers) {\n  var elts = [], first = true;\n  while (!this.eat(close)) {\n    if (first) { first = false; }\n    else { this.expect(types$1.comma); }\n    if (allowEmpty && this.type === types$1.comma) {\n      elts.push(null);\n    } else if (allowTrailingComma && this.afterTrailingComma(close)) {\n      break\n    } else if (this.type === types$1.ellipsis) {\n      var rest = this.parseRestBinding();\n      this.parseBindingListItem(rest);\n      elts.push(rest);\n      if (this.type === types$1.comma) { this.raiseRecoverable(this.start, \"Comma is not permitted after the rest element\"); }\n      this.expect(close);\n      break\n    } else {\n      elts.push(this.parseAssignableListItem(allowModifiers));\n    }\n  }\n  return elts\n};\n\npp$7.parseAssignableListItem = function(allowModifiers) {\n  var elem = this.parseMaybeDefault(this.start, this.startLoc);\n  this.parseBindingListItem(elem);\n  return elem\n};\n\npp$7.parseBindingListItem = function(param) {\n  return param\n};\n\n// Parses assignment pattern around given atom if possible.\n\npp$7.parseMaybeDefault = function(startPos, startLoc, left) {\n  left = left || this.parseBindingAtom();\n  if (this.options.ecmaVersion < 6 || !this.eat(types$1.eq)) { return left }\n  var node = this.startNodeAt(startPos, startLoc);\n  node.left = left;\n  node.right = this.parseMaybeAssign();\n  return this.finishNode(node, \"AssignmentPattern\")\n};\n\n// The following three functions all verify that a node is an lvalue —\n// something that can be bound, or assigned to. In order to do so, they perform\n// a variety of checks:\n//\n// - Check that none of the bound/assigned-to identifiers are reserved words.\n// - Record name declarations for bindings in the appropriate scope.\n// - Check duplicate argument names, if checkClashes is set.\n//\n// If a complex binding pattern is encountered (e.g., object and array\n// destructuring), the entire pattern is recursively checked.\n//\n// There are three versions of checkLVal*() appropriate for different\n// circumstances:\n//\n// - checkLValSimple() shall be used if the syntactic construct supports\n//   nothing other than identifiers and member expressions. Parenthesized\n//   expressions are also correctly handled. This is generally appropriate for\n//   constructs for which the spec says\n//\n//   > It is a Syntax Error if AssignmentTargetType of [the production] is not\n//   > simple.\n//\n//   It is also appropriate for checking if an identifier is valid and not\n//   defined elsewhere, like import declarations or function/class identifiers.\n//\n//   Examples where this is used include:\n//     a += …;\n//     import a from '…';\n//   where a is the node to be checked.\n//\n// - checkLValPattern() shall be used if the syntactic construct supports\n//   anything checkLValSimple() supports, as well as object and array\n//   destructuring patterns. This is generally appropriate for constructs for\n//   which the spec says\n//\n//   > It is a Syntax Error if [the production] is neither an ObjectLiteral nor\n//   > an ArrayLiteral and AssignmentTargetType of [the production] is not\n//   > simple.\n//\n//   Examples where this is used include:\n//     (a = …);\n//     const a = …;\n//     try { … } catch (a) { … }\n//   where a is the node to be checked.\n//\n// - checkLValInnerPattern() shall be used if the syntactic construct supports\n//   anything checkLValPattern() supports, as well as default assignment\n//   patterns, rest elements, and other constructs that may appear within an\n//   object or array destructuring pattern.\n//\n//   As a special case, function parameters also use checkLValInnerPattern(),\n//   as they also support defaults and rest constructs.\n//\n// These functions deliberately support both assignment and binding constructs,\n// as the logic for both is exceedingly similar. If the node is the target of\n// an assignment, then bindingType should be set to BIND_NONE. Otherwise, it\n// should be set to the appropriate BIND_* constant, like BIND_VAR or\n// BIND_LEXICAL.\n//\n// If the function is called with a non-BIND_NONE bindingType, then\n// additionally a checkClashes object may be specified to allow checking for\n// duplicate argument names. checkClashes is ignored if the provided construct\n// is an assignment (i.e., bindingType is BIND_NONE).\n\npp$7.checkLValSimple = function(expr, bindingType, checkClashes) {\n  if ( bindingType === void 0 ) bindingType = BIND_NONE;\n\n  var isBind = bindingType !== BIND_NONE;\n\n  switch (expr.type) {\n  case \"Identifier\":\n    if (this.strict && this.reservedWordsStrictBind.test(expr.name))\n      { this.raiseRecoverable(expr.start, (isBind ? \"Binding \" : \"Assigning to \") + expr.name + \" in strict mode\"); }\n    if (isBind) {\n      if (bindingType === BIND_LEXICAL && expr.name === \"let\")\n        { this.raiseRecoverable(expr.start, \"let is disallowed as a lexically bound name\"); }\n      if (checkClashes) {\n        if (hasOwn(checkClashes, expr.name))\n          { this.raiseRecoverable(expr.start, \"Argument name clash\"); }\n        checkClashes[expr.name] = true;\n      }\n      if (bindingType !== BIND_OUTSIDE) { this.declareName(expr.name, bindingType, expr.start); }\n    }\n    break\n\n  case \"ChainExpression\":\n    this.raiseRecoverable(expr.start, \"Optional chaining cannot appear in left-hand side\");\n    break\n\n  case \"MemberExpression\":\n    if (isBind) { this.raiseRecoverable(expr.start, \"Binding member expression\"); }\n    break\n\n  case \"ParenthesizedExpression\":\n    if (isBind) { this.raiseRecoverable(expr.start, \"Binding parenthesized expression\"); }\n    return this.checkLValSimple(expr.expression, bindingType, checkClashes)\n\n  default:\n    this.raise(expr.start, (isBind ? \"Binding\" : \"Assigning to\") + \" rvalue\");\n  }\n};\n\npp$7.checkLValPattern = function(expr, bindingType, checkClashes) {\n  if ( bindingType === void 0 ) bindingType = BIND_NONE;\n\n  switch (expr.type) {\n  case \"ObjectPattern\":\n    for (var i = 0, list = expr.properties; i < list.length; i += 1) {\n      var prop = list[i];\n\n    this.checkLValInnerPattern(prop, bindingType, checkClashes);\n    }\n    break\n\n  case \"ArrayPattern\":\n    for (var i$1 = 0, list$1 = expr.elements; i$1 < list$1.length; i$1 += 1) {\n      var elem = list$1[i$1];\n\n    if (elem) { this.checkLValInnerPattern(elem, bindingType, checkClashes); }\n    }\n    break\n\n  default:\n    this.checkLValSimple(expr, bindingType, checkClashes);\n  }\n};\n\npp$7.checkLValInnerPattern = function(expr, bindingType, checkClashes) {\n  if ( bindingType === void 0 ) bindingType = BIND_NONE;\n\n  switch (expr.type) {\n  case \"Property\":\n    // AssignmentProperty has type === \"Property\"\n    this.checkLValInnerPattern(expr.value, bindingType, checkClashes);\n    break\n\n  case \"AssignmentPattern\":\n    this.checkLValPattern(expr.left, bindingType, checkClashes);\n    break\n\n  case \"RestElement\":\n    this.checkLValPattern(expr.argument, bindingType, checkClashes);\n    break\n\n  default:\n    this.checkLValPattern(expr, bindingType, checkClashes);\n  }\n};\n\n// The algorithm used to determine whether a regexp can appear at a\n// given point in the program is loosely based on sweet.js' approach.\n// See https://github.com/mozilla/sweet.js/wiki/design\n\n\nvar TokContext = function TokContext(token, isExpr, preserveSpace, override, generator) {\n  this.token = token;\n  this.isExpr = !!isExpr;\n  this.preserveSpace = !!preserveSpace;\n  this.override = override;\n  this.generator = !!generator;\n};\n\nvar types = {\n  b_stat: new TokContext(\"{\", false),\n  b_expr: new TokContext(\"{\", true),\n  b_tmpl: new TokContext(\"${\", false),\n  p_stat: new TokContext(\"(\", false),\n  p_expr: new TokContext(\"(\", true),\n  q_tmpl: new TokContext(\"`\", true, true, function (p) { return p.tryReadTemplateToken(); }),\n  f_stat: new TokContext(\"function\", false),\n  f_expr: new TokContext(\"function\", true),\n  f_expr_gen: new TokContext(\"function\", true, false, null, true),\n  f_gen: new TokContext(\"function\", false, false, null, true)\n};\n\nvar pp$6 = Parser.prototype;\n\npp$6.initialContext = function() {\n  return [types.b_stat]\n};\n\npp$6.curContext = function() {\n  return this.context[this.context.length - 1]\n};\n\npp$6.braceIsBlock = function(prevType) {\n  var parent = this.curContext();\n  if (parent === types.f_expr || parent === types.f_stat)\n    { return true }\n  if (prevType === types$1.colon && (parent === types.b_stat || parent === types.b_expr))\n    { return !parent.isExpr }\n\n  // The check for `tt.name && exprAllowed` detects whether we are\n  // after a `yield` or `of` construct. See the `updateContext` for\n  // `tt.name`.\n  if (prevType === types$1._return || prevType === types$1.name && this.exprAllowed)\n    { return lineBreak.test(this.input.slice(this.lastTokEnd, this.start)) }\n  if (prevType === types$1._else || prevType === types$1.semi || prevType === types$1.eof || prevType === types$1.parenR || prevType === types$1.arrow)\n    { return true }\n  if (prevType === types$1.braceL)\n    { return parent === types.b_stat }\n  if (prevType === types$1._var || prevType === types$1._const || prevType === types$1.name)\n    { return false }\n  return !this.exprAllowed\n};\n\npp$6.inGeneratorContext = function() {\n  for (var i = this.context.length - 1; i >= 1; i--) {\n    var context = this.context[i];\n    if (context.token === \"function\")\n      { return context.generator }\n  }\n  return false\n};\n\npp$6.updateContext = function(prevType) {\n  var update, type = this.type;\n  if (type.keyword && prevType === types$1.dot)\n    { this.exprAllowed = false; }\n  else if (update = type.updateContext)\n    { update.call(this, prevType); }\n  else\n    { this.exprAllowed = type.beforeExpr; }\n};\n\n// Used to handle edge cases when token context could not be inferred correctly during tokenization phase\n\npp$6.overrideContext = function(tokenCtx) {\n  if (this.curContext() !== tokenCtx) {\n    this.context[this.context.length - 1] = tokenCtx;\n  }\n};\n\n// Token-specific context update code\n\ntypes$1.parenR.updateContext = types$1.braceR.updateContext = function() {\n  if (this.context.length === 1) {\n    this.exprAllowed = true;\n    return\n  }\n  var out = this.context.pop();\n  if (out === types.b_stat && this.curContext().token === \"function\") {\n    out = this.context.pop();\n  }\n  this.exprAllowed = !out.isExpr;\n};\n\ntypes$1.braceL.updateContext = function(prevType) {\n  this.context.push(this.braceIsBlock(prevType) ? types.b_stat : types.b_expr);\n  this.exprAllowed = true;\n};\n\ntypes$1.dollarBraceL.updateContext = function() {\n  this.context.push(types.b_tmpl);\n  this.exprAllowed = true;\n};\n\ntypes$1.parenL.updateContext = function(prevType) {\n  var statementParens = prevType === types$1._if || prevType === types$1._for || prevType === types$1._with || prevType === types$1._while;\n  this.context.push(statementParens ? types.p_stat : types.p_expr);\n  this.exprAllowed = true;\n};\n\ntypes$1.incDec.updateContext = function() {\n  // tokExprAllowed stays unchanged\n};\n\ntypes$1._function.updateContext = types$1._class.updateContext = function(prevType) {\n  if (prevType.beforeExpr && prevType !== types$1._else &&\n      !(prevType === types$1.semi && this.curContext() !== types.p_stat) &&\n      !(prevType === types$1._return && lineBreak.test(this.input.slice(this.lastTokEnd, this.start))) &&\n      !((prevType === types$1.colon || prevType === types$1.braceL) && this.curContext() === types.b_stat))\n    { this.context.push(types.f_expr); }\n  else\n    { this.context.push(types.f_stat); }\n  this.exprAllowed = false;\n};\n\ntypes$1.colon.updateContext = function() {\n  if (this.curContext().token === \"function\") { this.context.pop(); }\n  this.exprAllowed = true;\n};\n\ntypes$1.backQuote.updateContext = function() {\n  if (this.curContext() === types.q_tmpl)\n    { this.context.pop(); }\n  else\n    { this.context.push(types.q_tmpl); }\n  this.exprAllowed = false;\n};\n\ntypes$1.star.updateContext = function(prevType) {\n  if (prevType === types$1._function) {\n    var index = this.context.length - 1;\n    if (this.context[index] === types.f_expr)\n      { this.context[index] = types.f_expr_gen; }\n    else\n      { this.context[index] = types.f_gen; }\n  }\n  this.exprAllowed = true;\n};\n\ntypes$1.name.updateContext = function(prevType) {\n  var allowed = false;\n  if (this.options.ecmaVersion >= 6 && prevType !== types$1.dot) {\n    if (this.value === \"of\" && !this.exprAllowed ||\n        this.value === \"yield\" && this.inGeneratorContext())\n      { allowed = true; }\n  }\n  this.exprAllowed = allowed;\n};\n\n// A recursive descent parser operates by defining functions for all\n// syntactic elements, and recursively calling those, each function\n// advancing the input stream and returning an AST node. Precedence\n// of constructs (for example, the fact that `!x[1]` means `!(x[1])`\n// instead of `(!x)[1]` is handled by the fact that the parser\n// function that parses unary prefix operators is called first, and\n// in turn calls the function that parses `[]` subscripts — that\n// way, it'll receive the node for `x[1]` already parsed, and wraps\n// *that* in the unary operator node.\n//\n// Acorn uses an [operator precedence parser][opp] to handle binary\n// operator precedence, because it is much more compact than using\n// the technique outlined above, which uses different, nesting\n// functions to specify precedence, for all of the ten binary\n// precedence levels that JavaScript defines.\n//\n// [opp]: http://en.wikipedia.org/wiki/Operator-precedence_parser\n\n\nvar pp$5 = Parser.prototype;\n\n// Check if property name clashes with already added.\n// Object/class getters and setters are not allowed to clash —\n// either with each other or with an init property — and in\n// strict mode, init properties are also not allowed to be repeated.\n\npp$5.checkPropClash = function(prop, propHash, refDestructuringErrors) {\n  if (this.options.ecmaVersion >= 9 && prop.type === \"SpreadElement\")\n    { return }\n  if (this.options.ecmaVersion >= 6 && (prop.computed || prop.method || prop.shorthand))\n    { return }\n  var key = prop.key;\n  var name;\n  switch (key.type) {\n  case \"Identifier\": name = key.name; break\n  case \"Literal\": name = String(key.value); break\n  default: return\n  }\n  var kind = prop.kind;\n  if (this.options.ecmaVersion >= 6) {\n    if (name === \"__proto__\" && kind === \"init\") {\n      if (propHash.proto) {\n        if (refDestructuringErrors) {\n          if (refDestructuringErrors.doubleProto < 0) {\n            refDestructuringErrors.doubleProto = key.start;\n          }\n        } else {\n          this.raiseRecoverable(key.start, \"Redefinition of __proto__ property\");\n        }\n      }\n      propHash.proto = true;\n    }\n    return\n  }\n  name = \"$\" + name;\n  var other = propHash[name];\n  if (other) {\n    var redefinition;\n    if (kind === \"init\") {\n      redefinition = this.strict && other.init || other.get || other.set;\n    } else {\n      redefinition = other.init || other[kind];\n    }\n    if (redefinition)\n      { this.raiseRecoverable(key.start, \"Redefinition of property\"); }\n  } else {\n    other = propHash[name] = {\n      init: false,\n      get: false,\n      set: false\n    };\n  }\n  other[kind] = true;\n};\n\n// ### Expression parsing\n\n// These nest, from the most general expression type at the top to\n// 'atomic', nondivisible expression types at the bottom. Most of\n// the functions will simply let the function(s) below them parse,\n// and, *if* the syntactic construct they handle is present, wrap\n// the AST node that the inner parser gave them in another node.\n\n// Parse a full expression. The optional arguments are used to\n// forbid the `in` operator (in for loops initalization expressions)\n// and provide reference for storing '=' operator inside shorthand\n// property assignment in contexts where both object expression\n// and object pattern might appear (so it's possible to raise\n// delayed syntax error at correct position).\n\npp$5.parseExpression = function(forInit, refDestructuringErrors) {\n  var startPos = this.start, startLoc = this.startLoc;\n  var expr = this.parseMaybeAssign(forInit, refDestructuringErrors);\n  if (this.type === types$1.comma) {\n    var node = this.startNodeAt(startPos, startLoc);\n    node.expressions = [expr];\n    while (this.eat(types$1.comma)) { node.expressions.push(this.parseMaybeAssign(forInit, refDestructuringErrors)); }\n    return this.finishNode(node, \"SequenceExpression\")\n  }\n  return expr\n};\n\n// Parse an assignment expression. This includes applications of\n// operators like `+=`.\n\npp$5.parseMaybeAssign = function(forInit, refDestructuringErrors, afterLeftParse) {\n  if (this.isContextual(\"yield\")) {\n    if (this.inGenerator) { return this.parseYield(forInit) }\n    // The tokenizer will assume an expression is allowed after\n    // `yield`, but this isn't that kind of yield\n    else { this.exprAllowed = false; }\n  }\n\n  var ownDestructuringErrors = false, oldParenAssign = -1, oldTrailingComma = -1, oldDoubleProto = -1;\n  if (refDestructuringErrors) {\n    oldParenAssign = refDestructuringErrors.parenthesizedAssign;\n    oldTrailingComma = refDestructuringErrors.trailingComma;\n    oldDoubleProto = refDestructuringErrors.doubleProto;\n    refDestructuringErrors.parenthesizedAssign = refDestructuringErrors.trailingComma = -1;\n  } else {\n    refDestructuringErrors = new DestructuringErrors;\n    ownDestructuringErrors = true;\n  }\n\n  var startPos = this.start, startLoc = this.startLoc;\n  if (this.type === types$1.parenL || this.type === types$1.name) {\n    this.potentialArrowAt = this.start;\n    this.potentialArrowInForAwait = forInit === \"await\";\n  }\n  var left = this.parseMaybeConditional(forInit, refDestructuringErrors);\n  if (afterLeftParse) { left = afterLeftParse.call(this, left, startPos, startLoc); }\n  if (this.type.isAssign) {\n    var node = this.startNodeAt(startPos, startLoc);\n    node.operator = this.value;\n    if (this.type === types$1.eq)\n      { left = this.toAssignable(left, false, refDestructuringErrors); }\n    if (!ownDestructuringErrors) {\n      refDestructuringErrors.parenthesizedAssign = refDestructuringErrors.trailingComma = refDestructuringErrors.doubleProto = -1;\n    }\n    if (refDestructuringErrors.shorthandAssign >= left.start)\n      { refDestructuringErrors.shorthandAssign = -1; } // reset because shorthand default was used correctly\n    if (this.type === types$1.eq)\n      { this.checkLValPattern(left); }\n    else\n      { this.checkLValSimple(left); }\n    node.left = left;\n    this.next();\n    node.right = this.parseMaybeAssign(forInit);\n    if (oldDoubleProto > -1) { refDestructuringErrors.doubleProto = oldDoubleProto; }\n    return this.finishNode(node, \"AssignmentExpression\")\n  } else {\n    if (ownDestructuringErrors) { this.checkExpressionErrors(refDestructuringErrors, true); }\n  }\n  if (oldParenAssign > -1) { refDestructuringErrors.parenthesizedAssign = oldParenAssign; }\n  if (oldTrailingComma > -1) { refDestructuringErrors.trailingComma = oldTrailingComma; }\n  return left\n};\n\n// Parse a ternary conditional (`?:`) operator.\n\npp$5.parseMaybeConditional = function(forInit, refDestructuringErrors) {\n  var startPos = this.start, startLoc = this.startLoc;\n  var expr = this.parseExprOps(forInit, refDestructuringErrors);\n  if (this.checkExpressionErrors(refDestructuringErrors)) { return expr }\n  if (this.eat(types$1.question)) {\n    var node = this.startNodeAt(startPos, startLoc);\n    node.test = expr;\n    node.consequent = this.parseMaybeAssign();\n    this.expect(types$1.colon);\n    node.alternate = this.parseMaybeAssign(forInit);\n    return this.finishNode(node, \"ConditionalExpression\")\n  }\n  return expr\n};\n\n// Start the precedence parser.\n\npp$5.parseExprOps = function(forInit, refDestructuringErrors) {\n  var startPos = this.start, startLoc = this.startLoc;\n  var expr = this.parseMaybeUnary(refDestructuringErrors, false, false, forInit);\n  if (this.checkExpressionErrors(refDestructuringErrors)) { return expr }\n  return expr.start === startPos && expr.type === \"ArrowFunctionExpression\" ? expr : this.parseExprOp(expr, startPos, startLoc, -1, forInit)\n};\n\n// Parse binary operators with the operator precedence parsing\n// algorithm. `left` is the left-hand side of the operator.\n// `minPrec` provides context that allows the function to stop and\n// defer further parser to one of its callers when it encounters an\n// operator that has a lower precedence than the set it is parsing.\n\npp$5.parseExprOp = function(left, leftStartPos, leftStartLoc, minPrec, forInit) {\n  var prec = this.type.binop;\n  if (prec != null && (!forInit || this.type !== types$1._in)) {\n    if (prec > minPrec) {\n      var logical = this.type === types$1.logicalOR || this.type === types$1.logicalAND;\n      var coalesce = this.type === types$1.coalesce;\n      if (coalesce) {\n        // Handle the precedence of `tt.coalesce` as equal to the range of logical expressions.\n        // In other words, `node.right` shouldn't contain logical expressions in order to check the mixed error.\n        prec = types$1.logicalAND.binop;\n      }\n      var op = this.value;\n      this.next();\n      var startPos = this.start, startLoc = this.startLoc;\n      var right = this.parseExprOp(this.parseMaybeUnary(null, false, false, forInit), startPos, startLoc, prec, forInit);\n      var node = this.buildBinary(leftStartPos, leftStartLoc, left, right, op, logical || coalesce);\n      if ((logical && this.type === types$1.coalesce) || (coalesce && (this.type === types$1.logicalOR || this.type === types$1.logicalAND))) {\n        this.raiseRecoverable(this.start, \"Logical expressions and coalesce expressions cannot be mixed. Wrap either by parentheses\");\n      }\n      return this.parseExprOp(node, leftStartPos, leftStartLoc, minPrec, forInit)\n    }\n  }\n  return left\n};\n\npp$5.buildBinary = function(startPos, startLoc, left, right, op, logical) {\n  if (right.type === \"PrivateIdentifier\") { this.raise(right.start, \"Private identifier can only be left side of binary expression\"); }\n  var node = this.startNodeAt(startPos, startLoc);\n  node.left = left;\n  node.operator = op;\n  node.right = right;\n  return this.finishNode(node, logical ? \"LogicalExpression\" : \"BinaryExpression\")\n};\n\n// Parse unary operators, both prefix and postfix.\n\npp$5.parseMaybeUnary = function(refDestructuringErrors, sawUnary, incDec, forInit) {\n  var startPos = this.start, startLoc = this.startLoc, expr;\n  if (this.isContextual(\"await\") && this.canAwait) {\n    expr = this.parseAwait(forInit);\n    sawUnary = true;\n  } else if (this.type.prefix) {\n    var node = this.startNode(), update = this.type === types$1.incDec;\n    node.operator = this.value;\n    node.prefix = true;\n    this.next();\n    node.argument = this.parseMaybeUnary(null, true, update, forInit);\n    this.checkExpressionErrors(refDestructuringErrors, true);\n    if (update) { this.checkLValSimple(node.argument); }\n    else if (this.strict && node.operator === \"delete\" && isLocalVariableAccess(node.argument))\n      { this.raiseRecoverable(node.start, \"Deleting local variable in strict mode\"); }\n    else if (node.operator === \"delete\" && isPrivateFieldAccess(node.argument))\n      { this.raiseRecoverable(node.start, \"Private fields can not be deleted\"); }\n    else { sawUnary = true; }\n    expr = this.finishNode(node, update ? \"UpdateExpression\" : \"UnaryExpression\");\n  } else if (!sawUnary && this.type === types$1.privateId) {\n    if ((forInit || this.privateNameStack.length === 0) && this.options.checkPrivateFields) { this.unexpected(); }\n    expr = this.parsePrivateIdent();\n    // only could be private fields in 'in', such as #x in obj\n    if (this.type !== types$1._in) { this.unexpected(); }\n  } else {\n    expr = this.parseExprSubscripts(refDestructuringErrors, forInit);\n    if (this.checkExpressionErrors(refDestructuringErrors)) { return expr }\n    while (this.type.postfix && !this.canInsertSemicolon()) {\n      var node$1 = this.startNodeAt(startPos, startLoc);\n      node$1.operator = this.value;\n      node$1.prefix = false;\n      node$1.argument = expr;\n      this.checkLValSimple(expr);\n      this.next();\n      expr = this.finishNode(node$1, \"UpdateExpression\");\n    }\n  }\n\n  if (!incDec && this.eat(types$1.starstar)) {\n    if (sawUnary)\n      { this.unexpected(this.lastTokStart); }\n    else\n      { return this.buildBinary(startPos, startLoc, expr, this.parseMaybeUnary(null, false, false, forInit), \"**\", false) }\n  } else {\n    return expr\n  }\n};\n\nfunction isLocalVariableAccess(node) {\n  return (\n    node.type === \"Identifier\" ||\n    node.type === \"ParenthesizedExpression\" && isLocalVariableAccess(node.expression)\n  )\n}\n\nfunction isPrivateFieldAccess(node) {\n  return (\n    node.type === \"MemberExpression\" && node.property.type === \"PrivateIdentifier\" ||\n    node.type === \"ChainExpression\" && isPrivateFieldAccess(node.expression) ||\n    node.type === \"ParenthesizedExpression\" && isPrivateFieldAccess(node.expression)\n  )\n}\n\n// Parse call, dot, and `[]`-subscript expressions.\n\npp$5.parseExprSubscripts = function(refDestructuringErrors, forInit) {\n  var startPos = this.start, startLoc = this.startLoc;\n  var expr = this.parseExprAtom(refDestructuringErrors, forInit);\n  if (expr.type === \"ArrowFunctionExpression\" && this.input.slice(this.lastTokStart, this.lastTokEnd) !== \")\")\n    { return expr }\n  var result = this.parseSubscripts(expr, startPos, startLoc, false, forInit);\n  if (refDestructuringErrors && result.type === \"MemberExpression\") {\n    if (refDestructuringErrors.parenthesizedAssign >= result.start) { refDestructuringErrors.parenthesizedAssign = -1; }\n    if (refDestructuringErrors.parenthesizedBind >= result.start) { refDestructuringErrors.parenthesizedBind = -1; }\n    if (refDestructuringErrors.trailingComma >= result.start) { refDestructuringErrors.trailingComma = -1; }\n  }\n  return result\n};\n\npp$5.parseSubscripts = function(base, startPos, startLoc, noCalls, forInit) {\n  var maybeAsyncArrow = this.options.ecmaVersion >= 8 && base.type === \"Identifier\" && base.name === \"async\" &&\n      this.lastTokEnd === base.end && !this.canInsertSemicolon() && base.end - base.start === 5 &&\n      this.potentialArrowAt === base.start;\n  var optionalChained = false;\n\n  while (true) {\n    var element = this.parseSubscript(base, startPos, startLoc, noCalls, maybeAsyncArrow, optionalChained, forInit);\n\n    if (element.optional) { optionalChained = true; }\n    if (element === base || element.type === \"ArrowFunctionExpression\") {\n      if (optionalChained) {\n        var chainNode = this.startNodeAt(startPos, startLoc);\n        chainNode.expression = element;\n        element = this.finishNode(chainNode, \"ChainExpression\");\n      }\n      return element\n    }\n\n    base = element;\n  }\n};\n\npp$5.shouldParseAsyncArrow = function() {\n  return !this.canInsertSemicolon() && this.eat(types$1.arrow)\n};\n\npp$5.parseSubscriptAsyncArrow = function(startPos, startLoc, exprList, forInit) {\n  return this.parseArrowExpression(this.startNodeAt(startPos, startLoc), exprList, true, forInit)\n};\n\npp$5.parseSubscript = function(base, startPos, startLoc, noCalls, maybeAsyncArrow, optionalChained, forInit) {\n  var optionalSupported = this.options.ecmaVersion >= 11;\n  var optional = optionalSupported && this.eat(types$1.questionDot);\n  if (noCalls && optional) { this.raise(this.lastTokStart, \"Optional chaining cannot appear in the callee of new expressions\"); }\n\n  var computed = this.eat(types$1.bracketL);\n  if (computed || (optional && this.type !== types$1.parenL && this.type !== types$1.backQuote) || this.eat(types$1.dot)) {\n    var node = this.startNodeAt(startPos, startLoc);\n    node.object = base;\n    if (computed) {\n      node.property = this.parseExpression();\n      this.expect(types$1.bracketR);\n    } else if (this.type === types$1.privateId && base.type !== \"Super\") {\n      node.property = this.parsePrivateIdent();\n    } else {\n      node.property = this.parseIdent(this.options.allowReserved !== \"never\");\n    }\n    node.computed = !!computed;\n    if (optionalSupported) {\n      node.optional = optional;\n    }\n    base = this.finishNode(node, \"MemberExpression\");\n  } else if (!noCalls && this.eat(types$1.parenL)) {\n    var refDestructuringErrors = new DestructuringErrors, oldYieldPos = this.yieldPos, oldAwaitPos = this.awaitPos, oldAwaitIdentPos = this.awaitIdentPos;\n    this.yieldPos = 0;\n    this.awaitPos = 0;\n    this.awaitIdentPos = 0;\n    var exprList = this.parseExprList(types$1.parenR, this.options.ecmaVersion >= 8, false, refDestructuringErrors);\n    if (maybeAsyncArrow && !optional && this.shouldParseAsyncArrow()) {\n      this.checkPatternErrors(refDestructuringErrors, false);\n      this.checkYieldAwaitInDefaultParams();\n      if (this.awaitIdentPos > 0)\n        { this.raise(this.awaitIdentPos, \"Cannot use 'await' as identifier inside an async function\"); }\n      this.yieldPos = oldYieldPos;\n      this.awaitPos = oldAwaitPos;\n      this.awaitIdentPos = oldAwaitIdentPos;\n      return this.parseSubscriptAsyncArrow(startPos, startLoc, exprList, forInit)\n    }\n    this.checkExpressionErrors(refDestructuringErrors, true);\n    this.yieldPos = oldYieldPos || this.yieldPos;\n    this.awaitPos = oldAwaitPos || this.awaitPos;\n    this.awaitIdentPos = oldAwaitIdentPos || this.awaitIdentPos;\n    var node$1 = this.startNodeAt(startPos, startLoc);\n    node$1.callee = base;\n    node$1.arguments = exprList;\n    if (optionalSupported) {\n      node$1.optional = optional;\n    }\n    base = this.finishNode(node$1, \"CallExpression\");\n  } else if (this.type === types$1.backQuote) {\n    if (optional || optionalChained) {\n      this.raise(this.start, \"Optional chaining cannot appear in the tag of tagged template expressions\");\n    }\n    var node$2 = this.startNodeAt(startPos, startLoc);\n    node$2.tag = base;\n    node$2.quasi = this.parseTemplate({isTagged: true});\n    base = this.finishNode(node$2, \"TaggedTemplateExpression\");\n  }\n  return base\n};\n\n// Parse an atomic expression — either a single token that is an\n// expression, an expression started by a keyword like `function` or\n// `new`, or an expression wrapped in punctuation like `()`, `[]`,\n// or `{}`.\n\npp$5.parseExprAtom = function(refDestructuringErrors, forInit, forNew) {\n  // If a division operator appears in an expression position, the\n  // tokenizer got confused, and we force it to read a regexp instead.\n  if (this.type === types$1.slash) { this.readRegexp(); }\n\n  var node, canBeArrow = this.potentialArrowAt === this.start;\n  switch (this.type) {\n  case types$1._super:\n    if (!this.allowSuper)\n      { this.raise(this.start, \"'super' keyword outside a method\"); }\n    node = this.startNode();\n    this.next();\n    if (this.type === types$1.parenL && !this.allowDirectSuper)\n      { this.raise(node.start, \"super() call outside constructor of a subclass\"); }\n    // The `super` keyword can appear at below:\n    // SuperProperty:\n    //     super [ Expression ]\n    //     super . IdentifierName\n    // SuperCall:\n    //     super ( Arguments )\n    if (this.type !== types$1.dot && this.type !== types$1.bracketL && this.type !== types$1.parenL)\n      { this.unexpected(); }\n    return this.finishNode(node, \"Super\")\n\n  case types$1._this:\n    node = this.startNode();\n    this.next();\n    return this.finishNode(node, \"ThisExpression\")\n\n  case types$1.name:\n    var startPos = this.start, startLoc = this.startLoc, containsEsc = this.containsEsc;\n    var id = this.parseIdent(false);\n    if (this.options.ecmaVersion >= 8 && !containsEsc && id.name === \"async\" && !this.canInsertSemicolon() && this.eat(types$1._function)) {\n      this.overrideContext(types.f_expr);\n      return this.parseFunction(this.startNodeAt(startPos, startLoc), 0, false, true, forInit)\n    }\n    if (canBeArrow && !this.canInsertSemicolon()) {\n      if (this.eat(types$1.arrow))\n        { return this.parseArrowExpression(this.startNodeAt(startPos, startLoc), [id], false, forInit) }\n      if (this.options.ecmaVersion >= 8 && id.name === \"async\" && this.type === types$1.name && !containsEsc &&\n          (!this.potentialArrowInForAwait || this.value !== \"of\" || this.containsEsc)) {\n        id = this.parseIdent(false);\n        if (this.canInsertSemicolon() || !this.eat(types$1.arrow))\n          { this.unexpected(); }\n        return this.parseArrowExpression(this.startNodeAt(startPos, startLoc), [id], true, forInit)\n      }\n    }\n    return id\n\n  case types$1.regexp:\n    var value = this.value;\n    node = this.parseLiteral(value.value);\n    node.regex = {pattern: value.pattern, flags: value.flags};\n    return node\n\n  case types$1.num: case types$1.string:\n    return this.parseLiteral(this.value)\n\n  case types$1._null: case types$1._true: case types$1._false:\n    node = this.startNode();\n    node.value = this.type === types$1._null ? null : this.type === types$1._true;\n    node.raw = this.type.keyword;\n    this.next();\n    return this.finishNode(node, \"Literal\")\n\n  case types$1.parenL:\n    var start = this.start, expr = this.parseParenAndDistinguishExpression(canBeArrow, forInit);\n    if (refDestructuringErrors) {\n      if (refDestructuringErrors.parenthesizedAssign < 0 && !this.isSimpleAssignTarget(expr))\n        { refDestructuringErrors.parenthesizedAssign = start; }\n      if (refDestructuringErrors.parenthesizedBind < 0)\n        { refDestructuringErrors.parenthesizedBind = start; }\n    }\n    return expr\n\n  case types$1.bracketL:\n    node = this.startNode();\n    this.next();\n    node.elements = this.parseExprList(types$1.bracketR, true, true, refDestructuringErrors);\n    return this.finishNode(node, \"ArrayExpression\")\n\n  case types$1.braceL:\n    this.overrideContext(types.b_expr);\n    return this.parseObj(false, refDestructuringErrors)\n\n  case types$1._function:\n    node = this.startNode();\n    this.next();\n    return this.parseFunction(node, 0)\n\n  case types$1._class:\n    return this.parseClass(this.startNode(), false)\n\n  case types$1._new:\n    return this.parseNew()\n\n  case types$1.backQuote:\n    return this.parseTemplate()\n\n  case types$1._import:\n    if (this.options.ecmaVersion >= 11) {\n      return this.parseExprImport(forNew)\n    } else {\n      return this.unexpected()\n    }\n\n  default:\n    return this.parseExprAtomDefault()\n  }\n};\n\npp$5.parseExprAtomDefault = function() {\n  this.unexpected();\n};\n\npp$5.parseExprImport = function(forNew) {\n  var node = this.startNode();\n\n  // Consume `import` as an identifier for `import.meta`.\n  // Because `this.parseIdent(true)` doesn't check escape sequences, it needs the check of `this.containsEsc`.\n  if (this.containsEsc) { this.raiseRecoverable(this.start, \"Escape sequence in keyword import\"); }\n  this.next();\n\n  if (this.type === types$1.parenL && !forNew) {\n    return this.parseDynamicImport(node)\n  } else if (this.type === types$1.dot) {\n    var meta = this.startNodeAt(node.start, node.loc && node.loc.start);\n    meta.name = \"import\";\n    node.meta = this.finishNode(meta, \"Identifier\");\n    return this.parseImportMeta(node)\n  } else {\n    this.unexpected();\n  }\n};\n\npp$5.parseDynamicImport = function(node) {\n  this.next(); // skip `(`\n\n  // Parse node.source.\n  node.source = this.parseMaybeAssign();\n\n  if (this.options.ecmaVersion >= 16) {\n    if (!this.eat(types$1.parenR)) {\n      this.expect(types$1.comma);\n      if (!this.afterTrailingComma(types$1.parenR)) {\n        node.options = this.parseMaybeAssign();\n        if (!this.eat(types$1.parenR)) {\n          this.expect(types$1.comma);\n          if (!this.afterTrailingComma(types$1.parenR)) {\n            this.unexpected();\n          }\n        }\n      } else {\n        node.options = null;\n      }\n    } else {\n      node.options = null;\n    }\n  } else {\n    // Verify ending.\n    if (!this.eat(types$1.parenR)) {\n      var errorPos = this.start;\n      if (this.eat(types$1.comma) && this.eat(types$1.parenR)) {\n        this.raiseRecoverable(errorPos, \"Trailing comma is not allowed in import()\");\n      } else {\n        this.unexpected(errorPos);\n      }\n    }\n  }\n\n  return this.finishNode(node, \"ImportExpression\")\n};\n\npp$5.parseImportMeta = function(node) {\n  this.next(); // skip `.`\n\n  var containsEsc = this.containsEsc;\n  node.property = this.parseIdent(true);\n\n  if (node.property.name !== \"meta\")\n    { this.raiseRecoverable(node.property.start, \"The only valid meta property for import is 'import.meta'\"); }\n  if (containsEsc)\n    { this.raiseRecoverable(node.start, \"'import.meta' must not contain escaped characters\"); }\n  if (this.options.sourceType !== \"module\" && !this.options.allowImportExportEverywhere)\n    { this.raiseRecoverable(node.start, \"Cannot use 'import.meta' outside a module\"); }\n\n  return this.finishNode(node, \"MetaProperty\")\n};\n\npp$5.parseLiteral = function(value) {\n  var node = this.startNode();\n  node.value = value;\n  node.raw = this.input.slice(this.start, this.end);\n  if (node.raw.charCodeAt(node.raw.length - 1) === 110)\n    { node.bigint = node.value != null ? node.value.toString() : node.raw.slice(0, -1).replace(/_/g, \"\"); }\n  this.next();\n  return this.finishNode(node, \"Literal\")\n};\n\npp$5.parseParenExpression = function() {\n  this.expect(types$1.parenL);\n  var val = this.parseExpression();\n  this.expect(types$1.parenR);\n  return val\n};\n\npp$5.shouldParseArrow = function(exprList) {\n  return !this.canInsertSemicolon()\n};\n\npp$5.parseParenAndDistinguishExpression = function(canBeArrow, forInit) {\n  var startPos = this.start, startLoc = this.startLoc, val, allowTrailingComma = this.options.ecmaVersion >= 8;\n  if (this.options.ecmaVersion >= 6) {\n    this.next();\n\n    var innerStartPos = this.start, innerStartLoc = this.startLoc;\n    var exprList = [], first = true, lastIsComma = false;\n    var refDestructuringErrors = new DestructuringErrors, oldYieldPos = this.yieldPos, oldAwaitPos = this.awaitPos, spreadStart;\n    this.yieldPos = 0;\n    this.awaitPos = 0;\n    // Do not save awaitIdentPos to allow checking awaits nested in parameters\n    while (this.type !== types$1.parenR) {\n      first ? first = false : this.expect(types$1.comma);\n      if (allowTrailingComma && this.afterTrailingComma(types$1.parenR, true)) {\n        lastIsComma = true;\n        break\n      } else if (this.type === types$1.ellipsis) {\n        spreadStart = this.start;\n        exprList.push(this.parseParenItem(this.parseRestBinding()));\n        if (this.type === types$1.comma) {\n          this.raiseRecoverable(\n            this.start,\n            \"Comma is not permitted after the rest element\"\n          );\n        }\n        break\n      } else {\n        exprList.push(this.parseMaybeAssign(false, refDestructuringErrors, this.parseParenItem));\n      }\n    }\n    var innerEndPos = this.lastTokEnd, innerEndLoc = this.lastTokEndLoc;\n    this.expect(types$1.parenR);\n\n    if (canBeArrow && this.shouldParseArrow(exprList) && this.eat(types$1.arrow)) {\n      this.checkPatternErrors(refDestructuringErrors, false);\n      this.checkYieldAwaitInDefaultParams();\n      this.yieldPos = oldYieldPos;\n      this.awaitPos = oldAwaitPos;\n      return this.parseParenArrowList(startPos, startLoc, exprList, forInit)\n    }\n\n    if (!exprList.length || lastIsComma) { this.unexpected(this.lastTokStart); }\n    if (spreadStart) { this.unexpected(spreadStart); }\n    this.checkExpressionErrors(refDestructuringErrors, true);\n    this.yieldPos = oldYieldPos || this.yieldPos;\n    this.awaitPos = oldAwaitPos || this.awaitPos;\n\n    if (exprList.length > 1) {\n      val = this.startNodeAt(innerStartPos, innerStartLoc);\n      val.expressions = exprList;\n      this.finishNodeAt(val, \"SequenceExpression\", innerEndPos, innerEndLoc);\n    } else {\n      val = exprList[0];\n    }\n  } else {\n    val = this.parseParenExpression();\n  }\n\n  if (this.options.preserveParens) {\n    var par = this.startNodeAt(startPos, startLoc);\n    par.expression = val;\n    return this.finishNode(par, \"ParenthesizedExpression\")\n  } else {\n    return val\n  }\n};\n\npp$5.parseParenItem = function(item) {\n  return item\n};\n\npp$5.parseParenArrowList = function(startPos, startLoc, exprList, forInit) {\n  return this.parseArrowExpression(this.startNodeAt(startPos, startLoc), exprList, false, forInit)\n};\n\n// New's precedence is slightly tricky. It must allow its argument to\n// be a `[]` or dot subscript expression, but not a call — at least,\n// not without wrapping it in parentheses. Thus, it uses the noCalls\n// argument to parseSubscripts to prevent it from consuming the\n// argument list.\n\nvar empty = [];\n\npp$5.parseNew = function() {\n  if (this.containsEsc) { this.raiseRecoverable(this.start, \"Escape sequence in keyword new\"); }\n  var node = this.startNode();\n  this.next();\n  if (this.options.ecmaVersion >= 6 && this.type === types$1.dot) {\n    var meta = this.startNodeAt(node.start, node.loc && node.loc.start);\n    meta.name = \"new\";\n    node.meta = this.finishNode(meta, \"Identifier\");\n    this.next();\n    var containsEsc = this.containsEsc;\n    node.property = this.parseIdent(true);\n    if (node.property.name !== \"target\")\n      { this.raiseRecoverable(node.property.start, \"The only valid meta property for new is 'new.target'\"); }\n    if (containsEsc)\n      { this.raiseRecoverable(node.start, \"'new.target' must not contain escaped characters\"); }\n    if (!this.allowNewDotTarget)\n      { this.raiseRecoverable(node.start, \"'new.target' can only be used in functions and class static block\"); }\n    return this.finishNode(node, \"MetaProperty\")\n  }\n  var startPos = this.start, startLoc = this.startLoc;\n  node.callee = this.parseSubscripts(this.parseExprAtom(null, false, true), startPos, startLoc, true, false);\n  if (this.eat(types$1.parenL)) { node.arguments = this.parseExprList(types$1.parenR, this.options.ecmaVersion >= 8, false); }\n  else { node.arguments = empty; }\n  return this.finishNode(node, \"NewExpression\")\n};\n\n// Parse template expression.\n\npp$5.parseTemplateElement = function(ref) {\n  var isTagged = ref.isTagged;\n\n  var elem = this.startNode();\n  if (this.type === types$1.invalidTemplate) {\n    if (!isTagged) {\n      this.raiseRecoverable(this.start, \"Bad escape sequence in untagged template literal\");\n    }\n    elem.value = {\n      raw: this.value.replace(/\\r\\n?/g, \"\\n\"),\n      cooked: null\n    };\n  } else {\n    elem.value = {\n      raw: this.input.slice(this.start, this.end).replace(/\\r\\n?/g, \"\\n\"),\n      cooked: this.value\n    };\n  }\n  this.next();\n  elem.tail = this.type === types$1.backQuote;\n  return this.finishNode(elem, \"TemplateElement\")\n};\n\npp$5.parseTemplate = function(ref) {\n  if ( ref === void 0 ) ref = {};\n  var isTagged = ref.isTagged; if ( isTagged === void 0 ) isTagged = false;\n\n  var node = this.startNode();\n  this.next();\n  node.expressions = [];\n  var curElt = this.parseTemplateElement({isTagged: isTagged});\n  node.quasis = [curElt];\n  while (!curElt.tail) {\n    if (this.type === types$1.eof) { this.raise(this.pos, \"Unterminated template literal\"); }\n    this.expect(types$1.dollarBraceL);\n    node.expressions.push(this.parseExpression());\n    this.expect(types$1.braceR);\n    node.quasis.push(curElt = this.parseTemplateElement({isTagged: isTagged}));\n  }\n  this.next();\n  return this.finishNode(node, \"TemplateLiteral\")\n};\n\npp$5.isAsyncProp = function(prop) {\n  return !prop.computed && prop.key.type === \"Identifier\" && prop.key.name === \"async\" &&\n    (this.type === types$1.name || this.type === types$1.num || this.type === types$1.string || this.type === types$1.bracketL || this.type.keyword || (this.options.ecmaVersion >= 9 && this.type === types$1.star)) &&\n    !lineBreak.test(this.input.slice(this.lastTokEnd, this.start))\n};\n\n// Parse an object literal or binding pattern.\n\npp$5.parseObj = function(isPattern, refDestructuringErrors) {\n  var node = this.startNode(), first = true, propHash = {};\n  node.properties = [];\n  this.next();\n  while (!this.eat(types$1.braceR)) {\n    if (!first) {\n      this.expect(types$1.comma);\n      if (this.options.ecmaVersion >= 5 && this.afterTrailingComma(types$1.braceR)) { break }\n    } else { first = false; }\n\n    var prop = this.parseProperty(isPattern, refDestructuringErrors);\n    if (!isPattern) { this.checkPropClash(prop, propHash, refDestructuringErrors); }\n    node.properties.push(prop);\n  }\n  return this.finishNode(node, isPattern ? \"ObjectPattern\" : \"ObjectExpression\")\n};\n\npp$5.parseProperty = function(isPattern, refDestructuringErrors) {\n  var prop = this.startNode(), isGenerator, isAsync, startPos, startLoc;\n  if (this.options.ecmaVersion >= 9 && this.eat(types$1.ellipsis)) {\n    if (isPattern) {\n      prop.argument = this.parseIdent(false);\n      if (this.type === types$1.comma) {\n        this.raiseRecoverable(this.start, \"Comma is not permitted after the rest element\");\n      }\n      return this.finishNode(prop, \"RestElement\")\n    }\n    // Parse argument.\n    prop.argument = this.parseMaybeAssign(false, refDestructuringErrors);\n    // To disallow trailing comma via `this.toAssignable()`.\n    if (this.type === types$1.comma && refDestructuringErrors && refDestructuringErrors.trailingComma < 0) {\n      refDestructuringErrors.trailingComma = this.start;\n    }\n    // Finish\n    return this.finishNode(prop, \"SpreadElement\")\n  }\n  if (this.options.ecmaVersion >= 6) {\n    prop.method = false;\n    prop.shorthand = false;\n    if (isPattern || refDestructuringErrors) {\n      startPos = this.start;\n      startLoc = this.startLoc;\n    }\n    if (!isPattern)\n      { isGenerator = this.eat(types$1.star); }\n  }\n  var containsEsc = this.containsEsc;\n  this.parsePropertyName(prop);\n  if (!isPattern && !containsEsc && this.options.ecmaVersion >= 8 && !isGenerator && this.isAsyncProp(prop)) {\n    isAsync = true;\n    isGenerator = this.options.ecmaVersion >= 9 && this.eat(types$1.star);\n    this.parsePropertyName(prop);\n  } else {\n    isAsync = false;\n  }\n  this.parsePropertyValue(prop, isPattern, isGenerator, isAsync, startPos, startLoc, refDestructuringErrors, containsEsc);\n  return this.finishNode(prop, \"Property\")\n};\n\npp$5.parseGetterSetter = function(prop) {\n  var kind = prop.key.name;\n  this.parsePropertyName(prop);\n  prop.value = this.parseMethod(false);\n  prop.kind = kind;\n  var paramCount = prop.kind === \"get\" ? 0 : 1;\n  if (prop.value.params.length !== paramCount) {\n    var start = prop.value.start;\n    if (prop.kind === \"get\")\n      { this.raiseRecoverable(start, \"getter should have no params\"); }\n    else\n      { this.raiseRecoverable(start, \"setter should have exactly one param\"); }\n  } else {\n    if (prop.kind === \"set\" && prop.value.params[0].type === \"RestElement\")\n      { this.raiseRecoverable(prop.value.params[0].start, \"Setter cannot use rest params\"); }\n  }\n};\n\npp$5.parsePropertyValue = function(prop, isPattern, isGenerator, isAsync, startPos, startLoc, refDestructuringErrors, containsEsc) {\n  if ((isGenerator || isAsync) && this.type === types$1.colon)\n    { this.unexpected(); }\n\n  if (this.eat(types$1.colon)) {\n    prop.value = isPattern ? this.parseMaybeDefault(this.start, this.startLoc) : this.parseMaybeAssign(false, refDestructuringErrors);\n    prop.kind = \"init\";\n  } else if (this.options.ecmaVersion >= 6 && this.type === types$1.parenL) {\n    if (isPattern) { this.unexpected(); }\n    prop.method = true;\n    prop.value = this.parseMethod(isGenerator, isAsync);\n    prop.kind = \"init\";\n  } else if (!isPattern && !containsEsc &&\n             this.options.ecmaVersion >= 5 && !prop.computed && prop.key.type === \"Identifier\" &&\n             (prop.key.name === \"get\" || prop.key.name === \"set\") &&\n             (this.type !== types$1.comma && this.type !== types$1.braceR && this.type !== types$1.eq)) {\n    if (isGenerator || isAsync) { this.unexpected(); }\n    this.parseGetterSetter(prop);\n  } else if (this.options.ecmaVersion >= 6 && !prop.computed && prop.key.type === \"Identifier\") {\n    if (isGenerator || isAsync) { this.unexpected(); }\n    this.checkUnreserved(prop.key);\n    if (prop.key.name === \"await\" && !this.awaitIdentPos)\n      { this.awaitIdentPos = startPos; }\n    if (isPattern) {\n      prop.value = this.parseMaybeDefault(startPos, startLoc, this.copyNode(prop.key));\n    } else if (this.type === types$1.eq && refDestructuringErrors) {\n      if (refDestructuringErrors.shorthandAssign < 0)\n        { refDestructuringErrors.shorthandAssign = this.start; }\n      prop.value = this.parseMaybeDefault(startPos, startLoc, this.copyNode(prop.key));\n    } else {\n      prop.value = this.copyNode(prop.key);\n    }\n    prop.kind = \"init\";\n    prop.shorthand = true;\n  } else { this.unexpected(); }\n};\n\npp$5.parsePropertyName = function(prop) {\n  if (this.options.ecmaVersion >= 6) {\n    if (this.eat(types$1.bracketL)) {\n      prop.computed = true;\n      prop.key = this.parseMaybeAssign();\n      this.expect(types$1.bracketR);\n      return prop.key\n    } else {\n      prop.computed = false;\n    }\n  }\n  return prop.key = this.type === types$1.num || this.type === types$1.string ? this.parseExprAtom() : this.parseIdent(this.options.allowReserved !== \"never\")\n};\n\n// Initialize empty function node.\n\npp$5.initFunction = function(node) {\n  node.id = null;\n  if (this.options.ecmaVersion >= 6) { node.generator = node.expression = false; }\n  if (this.options.ecmaVersion >= 8) { node.async = false; }\n};\n\n// Parse object or class method.\n\npp$5.parseMethod = function(isGenerator, isAsync, allowDirectSuper) {\n  var node = this.startNode(), oldYieldPos = this.yieldPos, oldAwaitPos = this.awaitPos, oldAwaitIdentPos = this.awaitIdentPos;\n\n  this.initFunction(node);\n  if (this.options.ecmaVersion >= 6)\n    { node.generator = isGenerator; }\n  if (this.options.ecmaVersion >= 8)\n    { node.async = !!isAsync; }\n\n  this.yieldPos = 0;\n  this.awaitPos = 0;\n  this.awaitIdentPos = 0;\n  this.enterScope(functionFlags(isAsync, node.generator) | SCOPE_SUPER | (allowDirectSuper ? SCOPE_DIRECT_SUPER : 0));\n\n  this.expect(types$1.parenL);\n  node.params = this.parseBindingList(types$1.parenR, false, this.options.ecmaVersion >= 8);\n  this.checkYieldAwaitInDefaultParams();\n  this.parseFunctionBody(node, false, true, false);\n\n  this.yieldPos = oldYieldPos;\n  this.awaitPos = oldAwaitPos;\n  this.awaitIdentPos = oldAwaitIdentPos;\n  return this.finishNode(node, \"FunctionExpression\")\n};\n\n// Parse arrow function expression with given parameters.\n\npp$5.parseArrowExpression = function(node, params, isAsync, forInit) {\n  var oldYieldPos = this.yieldPos, oldAwaitPos = this.awaitPos, oldAwaitIdentPos = this.awaitIdentPos;\n\n  this.enterScope(functionFlags(isAsync, false) | SCOPE_ARROW);\n  this.initFunction(node);\n  if (this.options.ecmaVersion >= 8) { node.async = !!isAsync; }\n\n  this.yieldPos = 0;\n  this.awaitPos = 0;\n  this.awaitIdentPos = 0;\n\n  node.params = this.toAssignableList(params, true);\n  this.parseFunctionBody(node, true, false, forInit);\n\n  this.yieldPos = oldYieldPos;\n  this.awaitPos = oldAwaitPos;\n  this.awaitIdentPos = oldAwaitIdentPos;\n  return this.finishNode(node, \"ArrowFunctionExpression\")\n};\n\n// Parse function body and check parameters.\n\npp$5.parseFunctionBody = function(node, isArrowFunction, isMethod, forInit) {\n  var isExpression = isArrowFunction && this.type !== types$1.braceL;\n  var oldStrict = this.strict, useStrict = false;\n\n  if (isExpression) {\n    node.body = this.parseMaybeAssign(forInit);\n    node.expression = true;\n    this.checkParams(node, false);\n  } else {\n    var nonSimple = this.options.ecmaVersion >= 7 && !this.isSimpleParamList(node.params);\n    if (!oldStrict || nonSimple) {\n      useStrict = this.strictDirective(this.end);\n      // If this is a strict mode function, verify that argument names\n      // are not repeated, and it does not try to bind the words `eval`\n      // or `arguments`.\n      if (useStrict && nonSimple)\n        { this.raiseRecoverable(node.start, \"Illegal 'use strict' directive in function with non-simple parameter list\"); }\n    }\n    // Start a new scope with regard to labels and the `inFunction`\n    // flag (restore them to their old value afterwards).\n    var oldLabels = this.labels;\n    this.labels = [];\n    if (useStrict) { this.strict = true; }\n\n    // Add the params to varDeclaredNames to ensure that an error is thrown\n    // if a let/const declaration in the function clashes with one of the params.\n    this.checkParams(node, !oldStrict && !useStrict && !isArrowFunction && !isMethod && this.isSimpleParamList(node.params));\n    // Ensure the function name isn't a forbidden identifier in strict mode, e.g. 'eval'\n    if (this.strict && node.id) { this.checkLValSimple(node.id, BIND_OUTSIDE); }\n    node.body = this.parseBlock(false, undefined, useStrict && !oldStrict);\n    node.expression = false;\n    this.adaptDirectivePrologue(node.body.body);\n    this.labels = oldLabels;\n  }\n  this.exitScope();\n};\n\npp$5.isSimpleParamList = function(params) {\n  for (var i = 0, list = params; i < list.length; i += 1)\n    {\n    var param = list[i];\n\n    if (param.type !== \"Identifier\") { return false\n  } }\n  return true\n};\n\n// Checks function params for various disallowed patterns such as using \"eval\"\n// or \"arguments\" and duplicate parameters.\n\npp$5.checkParams = function(node, allowDuplicates) {\n  var nameHash = Object.create(null);\n  for (var i = 0, list = node.params; i < list.length; i += 1)\n    {\n    var param = list[i];\n\n    this.checkLValInnerPattern(param, BIND_VAR, allowDuplicates ? null : nameHash);\n  }\n};\n\n// Parses a comma-separated list of expressions, and returns them as\n// an array. `close` is the token type that ends the list, and\n// `allowEmpty` can be turned on to allow subsequent commas with\n// nothing in between them to be parsed as `null` (which is needed\n// for array literals).\n\npp$5.parseExprList = function(close, allowTrailingComma, allowEmpty, refDestructuringErrors) {\n  var elts = [], first = true;\n  while (!this.eat(close)) {\n    if (!first) {\n      this.expect(types$1.comma);\n      if (allowTrailingComma && this.afterTrailingComma(close)) { break }\n    } else { first = false; }\n\n    var elt = (void 0);\n    if (allowEmpty && this.type === types$1.comma)\n      { elt = null; }\n    else if (this.type === types$1.ellipsis) {\n      elt = this.parseSpread(refDestructuringErrors);\n      if (refDestructuringErrors && this.type === types$1.comma && refDestructuringErrors.trailingComma < 0)\n        { refDestructuringErrors.trailingComma = this.start; }\n    } else {\n      elt = this.parseMaybeAssign(false, refDestructuringErrors);\n    }\n    elts.push(elt);\n  }\n  return elts\n};\n\npp$5.checkUnreserved = function(ref) {\n  var start = ref.start;\n  var end = ref.end;\n  var name = ref.name;\n\n  if (this.inGenerator && name === \"yield\")\n    { this.raiseRecoverable(start, \"Cannot use 'yield' as identifier inside a generator\"); }\n  if (this.inAsync && name === \"await\")\n    { this.raiseRecoverable(start, \"Cannot use 'await' as identifier inside an async function\"); }\n  if (!(this.currentThisScope().flags & SCOPE_VAR) && name === \"arguments\")\n    { this.raiseRecoverable(start, \"Cannot use 'arguments' in class field initializer\"); }\n  if (this.inClassStaticBlock && (name === \"arguments\" || name === \"await\"))\n    { this.raise(start, (\"Cannot use \" + name + \" in class static initialization block\")); }\n  if (this.keywords.test(name))\n    { this.raise(start, (\"Unexpected keyword '\" + name + \"'\")); }\n  if (this.options.ecmaVersion < 6 &&\n    this.input.slice(start, end).indexOf(\"\\\\\") !== -1) { return }\n  var re = this.strict ? this.reservedWordsStrict : this.reservedWords;\n  if (re.test(name)) {\n    if (!this.inAsync && name === \"await\")\n      { this.raiseRecoverable(start, \"Cannot use keyword 'await' outside an async function\"); }\n    this.raiseRecoverable(start, (\"The keyword '\" + name + \"' is reserved\"));\n  }\n};\n\n// Parse the next token as an identifier. If `liberal` is true (used\n// when parsing properties), it will also convert keywords into\n// identifiers.\n\npp$5.parseIdent = function(liberal) {\n  var node = this.parseIdentNode();\n  this.next(!!liberal);\n  this.finishNode(node, \"Identifier\");\n  if (!liberal) {\n    this.checkUnreserved(node);\n    if (node.name === \"await\" && !this.awaitIdentPos)\n      { this.awaitIdentPos = node.start; }\n  }\n  return node\n};\n\npp$5.parseIdentNode = function() {\n  var node = this.startNode();\n  if (this.type === types$1.name) {\n    node.name = this.value;\n  } else if (this.type.keyword) {\n    node.name = this.type.keyword;\n\n    // To fix https://github.com/acornjs/acorn/issues/575\n    // `class` and `function` keywords push new context into this.context.\n    // But there is no chance to pop the context if the keyword is consumed as an identifier such as a property name.\n    // If the previous token is a dot, this does not apply because the context-managing code already ignored the keyword\n    if ((node.name === \"class\" || node.name === \"function\") &&\n      (this.lastTokEnd !== this.lastTokStart + 1 || this.input.charCodeAt(this.lastTokStart) !== 46)) {\n      this.context.pop();\n    }\n    this.type = types$1.name;\n  } else {\n    this.unexpected();\n  }\n  return node\n};\n\npp$5.parsePrivateIdent = function() {\n  var node = this.startNode();\n  if (this.type === types$1.privateId) {\n    node.name = this.value;\n  } else {\n    this.unexpected();\n  }\n  this.next();\n  this.finishNode(node, \"PrivateIdentifier\");\n\n  // For validating existence\n  if (this.options.checkPrivateFields) {\n    if (this.privateNameStack.length === 0) {\n      this.raise(node.start, (\"Private field '#\" + (node.name) + \"' must be declared in an enclosing class\"));\n    } else {\n      this.privateNameStack[this.privateNameStack.length - 1].used.push(node);\n    }\n  }\n\n  return node\n};\n\n// Parses yield expression inside generator.\n\npp$5.parseYield = function(forInit) {\n  if (!this.yieldPos) { this.yieldPos = this.start; }\n\n  var node = this.startNode();\n  this.next();\n  if (this.type === types$1.semi || this.canInsertSemicolon() || (this.type !== types$1.star && !this.type.startsExpr)) {\n    node.delegate = false;\n    node.argument = null;\n  } else {\n    node.delegate = this.eat(types$1.star);\n    node.argument = this.parseMaybeAssign(forInit);\n  }\n  return this.finishNode(node, \"YieldExpression\")\n};\n\npp$5.parseAwait = function(forInit) {\n  if (!this.awaitPos) { this.awaitPos = this.start; }\n\n  var node = this.startNode();\n  this.next();\n  node.argument = this.parseMaybeUnary(null, true, false, forInit);\n  return this.finishNode(node, \"AwaitExpression\")\n};\n\nvar pp$4 = Parser.prototype;\n\n// This function is used to raise exceptions on parse errors. It\n// takes an offset integer (into the current `input`) to indicate\n// the location of the error, attaches the position to the end\n// of the error message, and then raises a `SyntaxError` with that\n// message.\n\npp$4.raise = function(pos, message) {\n  var loc = getLineInfo(this.input, pos);\n  message += \" (\" + loc.line + \":\" + loc.column + \")\";\n  if (this.sourceFile) {\n    message += \" in \" + this.sourceFile;\n  }\n  var err = new SyntaxError(message);\n  err.pos = pos; err.loc = loc; err.raisedAt = this.pos;\n  throw err\n};\n\npp$4.raiseRecoverable = pp$4.raise;\n\npp$4.curPosition = function() {\n  if (this.options.locations) {\n    return new Position(this.curLine, this.pos - this.lineStart)\n  }\n};\n\nvar pp$3 = Parser.prototype;\n\nvar Scope = function Scope(flags) {\n  this.flags = flags;\n  // A list of var-declared names in the current lexical scope\n  this.var = [];\n  // A list of lexically-declared names in the current lexical scope\n  this.lexical = [];\n  // A list of lexically-declared FunctionDeclaration names in the current lexical scope\n  this.functions = [];\n};\n\n// The functions in this module keep track of declared variables in the current scope in order to detect duplicate variable names.\n\npp$3.enterScope = function(flags) {\n  this.scopeStack.push(new Scope(flags));\n};\n\npp$3.exitScope = function() {\n  this.scopeStack.pop();\n};\n\n// The spec says:\n// > At the top level of a function, or script, function declarations are\n// > treated like var declarations rather than like lexical declarations.\npp$3.treatFunctionsAsVarInScope = function(scope) {\n  return (scope.flags & SCOPE_FUNCTION) || !this.inModule && (scope.flags & SCOPE_TOP)\n};\n\npp$3.declareName = function(name, bindingType, pos) {\n  var redeclared = false;\n  if (bindingType === BIND_LEXICAL) {\n    var scope = this.currentScope();\n    redeclared = scope.lexical.indexOf(name) > -1 || scope.functions.indexOf(name) > -1 || scope.var.indexOf(name) > -1;\n    scope.lexical.push(name);\n    if (this.inModule && (scope.flags & SCOPE_TOP))\n      { delete this.undefinedExports[name]; }\n  } else if (bindingType === BIND_SIMPLE_CATCH) {\n    var scope$1 = this.currentScope();\n    scope$1.lexical.push(name);\n  } else if (bindingType === BIND_FUNCTION) {\n    var scope$2 = this.currentScope();\n    if (this.treatFunctionsAsVar)\n      { redeclared = scope$2.lexical.indexOf(name) > -1; }\n    else\n      { redeclared = scope$2.lexical.indexOf(name) > -1 || scope$2.var.indexOf(name) > -1; }\n    scope$2.functions.push(name);\n  } else {\n    for (var i = this.scopeStack.length - 1; i >= 0; --i) {\n      var scope$3 = this.scopeStack[i];\n      if (scope$3.lexical.indexOf(name) > -1 && !((scope$3.flags & SCOPE_SIMPLE_CATCH) && scope$3.lexical[0] === name) ||\n          !this.treatFunctionsAsVarInScope(scope$3) && scope$3.functions.indexOf(name) > -1) {\n        redeclared = true;\n        break\n      }\n      scope$3.var.push(name);\n      if (this.inModule && (scope$3.flags & SCOPE_TOP))\n        { delete this.undefinedExports[name]; }\n      if (scope$3.flags & SCOPE_VAR) { break }\n    }\n  }\n  if (redeclared) { this.raiseRecoverable(pos, (\"Identifier '\" + name + \"' has already been declared\")); }\n};\n\npp$3.checkLocalExport = function(id) {\n  // scope.functions must be empty as Module code is always strict.\n  if (this.scopeStack[0].lexical.indexOf(id.name) === -1 &&\n      this.scopeStack[0].var.indexOf(id.name) === -1) {\n    this.undefinedExports[id.name] = id;\n  }\n};\n\npp$3.currentScope = function() {\n  return this.scopeStack[this.scopeStack.length - 1]\n};\n\npp$3.currentVarScope = function() {\n  for (var i = this.scopeStack.length - 1;; i--) {\n    var scope = this.scopeStack[i];\n    if (scope.flags & (SCOPE_VAR | SCOPE_CLASS_FIELD_INIT | SCOPE_CLASS_STATIC_BLOCK)) { return scope }\n  }\n};\n\n// Could be useful for `this`, `new.target`, `super()`, `super.property`, and `super[property]`.\npp$3.currentThisScope = function() {\n  for (var i = this.scopeStack.length - 1;; i--) {\n    var scope = this.scopeStack[i];\n    if (scope.flags & (SCOPE_VAR | SCOPE_CLASS_FIELD_INIT | SCOPE_CLASS_STATIC_BLOCK) &&\n        !(scope.flags & SCOPE_ARROW)) { return scope }\n  }\n};\n\nvar Node = function Node(parser, pos, loc) {\n  this.type = \"\";\n  this.start = pos;\n  this.end = 0;\n  if (parser.options.locations)\n    { this.loc = new SourceLocation(parser, loc); }\n  if (parser.options.directSourceFile)\n    { this.sourceFile = parser.options.directSourceFile; }\n  if (parser.options.ranges)\n    { this.range = [pos, 0]; }\n};\n\n// Start an AST node, attaching a start offset.\n\nvar pp$2 = Parser.prototype;\n\npp$2.startNode = function() {\n  return new Node(this, this.start, this.startLoc)\n};\n\npp$2.startNodeAt = function(pos, loc) {\n  return new Node(this, pos, loc)\n};\n\n// Finish an AST node, adding `type` and `end` properties.\n\nfunction finishNodeAt(node, type, pos, loc) {\n  node.type = type;\n  node.end = pos;\n  if (this.options.locations)\n    { node.loc.end = loc; }\n  if (this.options.ranges)\n    { node.range[1] = pos; }\n  return node\n}\n\npp$2.finishNode = function(node, type) {\n  return finishNodeAt.call(this, node, type, this.lastTokEnd, this.lastTokEndLoc)\n};\n\n// Finish node at given position\n\npp$2.finishNodeAt = function(node, type, pos, loc) {\n  return finishNodeAt.call(this, node, type, pos, loc)\n};\n\npp$2.copyNode = function(node) {\n  var newNode = new Node(this, node.start, this.startLoc);\n  for (var prop in node) { newNode[prop] = node[prop]; }\n  return newNode\n};\n\n// This file was generated by \"bin/generate-unicode-script-values.js\". Do not modify manually!\nvar scriptValuesAddedInUnicode = \"Gara Garay Gukh Gurung_Khema Hrkt Katakana_Or_Hiragana Kawi Kirat_Rai Krai Nag_Mundari Nagm Ol_Onal Onao Sunu Sunuwar Todhri Todr Tulu_Tigalari Tutg Unknown Zzzz\";\n\n// This file contains Unicode properties extracted from the ECMAScript specification.\n// The lists are extracted like so:\n// $$('#table-binary-unicode-properties > figure > table > tbody > tr > td:nth-child(1) code').map(el => el.innerText)\n\n// #table-binary-unicode-properties\nvar ecma9BinaryProperties = \"ASCII ASCII_Hex_Digit AHex Alphabetic Alpha Any Assigned Bidi_Control Bidi_C Bidi_Mirrored Bidi_M Case_Ignorable CI Cased Changes_When_Casefolded CWCF Changes_When_Casemapped CWCM Changes_When_Lowercased CWL Changes_When_NFKC_Casefolded CWKCF Changes_When_Titlecased CWT Changes_When_Uppercased CWU Dash Default_Ignorable_Code_Point DI Deprecated Dep Diacritic Dia Emoji Emoji_Component Emoji_Modifier Emoji_Modifier_Base Emoji_Presentation Extender Ext Grapheme_Base Gr_Base Grapheme_Extend Gr_Ext Hex_Digit Hex IDS_Binary_Operator IDSB IDS_Trinary_Operator IDST ID_Continue IDC ID_Start IDS Ideographic Ideo Join_Control Join_C Logical_Order_Exception LOE Lowercase Lower Math Noncharacter_Code_Point NChar Pattern_Syntax Pat_Syn Pattern_White_Space Pat_WS Quotation_Mark QMark Radical Regional_Indicator RI Sentence_Terminal STerm Soft_Dotted SD Terminal_Punctuation Term Unified_Ideograph UIdeo Uppercase Upper Variation_Selector VS White_Space space XID_Continue XIDC XID_Start XIDS\";\nvar ecma10BinaryProperties = ecma9BinaryProperties + \" Extended_Pictographic\";\nvar ecma11BinaryProperties = ecma10BinaryProperties;\nvar ecma12BinaryProperties = ecma11BinaryProperties + \" EBase EComp EMod EPres ExtPict\";\nvar ecma13BinaryProperties = ecma12BinaryProperties;\nvar ecma14BinaryProperties = ecma13BinaryProperties;\n\nvar unicodeBinaryProperties = {\n  9: ecma9BinaryProperties,\n  10: ecma10BinaryProperties,\n  11: ecma11BinaryProperties,\n  12: ecma12BinaryProperties,\n  13: ecma13BinaryProperties,\n  14: ecma14BinaryProperties\n};\n\n// #table-binary-unicode-properties-of-strings\nvar ecma14BinaryPropertiesOfStrings = \"Basic_Emoji Emoji_Keycap_Sequence RGI_Emoji_Modifier_Sequence RGI_Emoji_Flag_Sequence RGI_Emoji_Tag_Sequence RGI_Emoji_ZWJ_Sequence RGI_Emoji\";\n\nvar unicodeBinaryPropertiesOfStrings = {\n  9: \"\",\n  10: \"\",\n  11: \"\",\n  12: \"\",\n  13: \"\",\n  14: ecma14BinaryPropertiesOfStrings\n};\n\n// #table-unicode-general-category-values\nvar unicodeGeneralCategoryValues = \"Cased_Letter LC Close_Punctuation Pe Connector_Punctuation Pc Control Cc cntrl Currency_Symbol Sc Dash_Punctuation Pd Decimal_Number Nd digit Enclosing_Mark Me Final_Punctuation Pf Format Cf Initial_Punctuation Pi Letter L Letter_Number Nl Line_Separator Zl Lowercase_Letter Ll Mark M Combining_Mark Math_Symbol Sm Modifier_Letter Lm Modifier_Symbol Sk Nonspacing_Mark Mn Number N Open_Punctuation Ps Other C Other_Letter Lo Other_Number No Other_Punctuation Po Other_Symbol So Paragraph_Separator Zp Private_Use Co Punctuation P punct Separator Z Space_Separator Zs Spacing_Mark Mc Surrogate Cs Symbol S Titlecase_Letter Lt Unassigned Cn Uppercase_Letter Lu\";\n\n// #table-unicode-script-values\nvar ecma9ScriptValues = \"Adlam Adlm Ahom Anatolian_Hieroglyphs Hluw Arabic Arab Armenian Armn Avestan Avst Balinese Bali Bamum Bamu Bassa_Vah Bass Batak Batk Bengali Beng Bhaiksuki Bhks Bopomofo Bopo Brahmi Brah Braille Brai Buginese Bugi Buhid Buhd Canadian_Aboriginal Cans Carian Cari Caucasian_Albanian Aghb Chakma Cakm Cham Cham Cherokee Cher Common Zyyy Coptic Copt Qaac Cuneiform Xsux Cypriot Cprt Cyrillic Cyrl Deseret Dsrt Devanagari Deva Duployan Dupl Egyptian_Hieroglyphs Egyp Elbasan Elba Ethiopic Ethi Georgian Geor Glagolitic Glag Gothic Goth Grantha Gran Greek Grek Gujarati Gujr Gurmukhi Guru Han Hani Hangul Hang Hanunoo Hano Hatran Hatr Hebrew Hebr Hiragana Hira Imperial_Aramaic Armi Inherited Zinh Qaai Inscriptional_Pahlavi Phli Inscriptional_Parthian Prti Javanese Java Kaithi Kthi Kannada Knda Katakana Kana Kayah_Li Kali Kharoshthi Khar Khmer Khmr Khojki Khoj Khudawadi Sind Lao Laoo Latin Latn Lepcha Lepc Limbu Limb Linear_A Lina Linear_B Linb Lisu Lisu Lycian Lyci Lydian Lydi Mahajani Mahj Malayalam Mlym Mandaic Mand Manichaean Mani Marchen Marc Masaram_Gondi Gonm Meetei_Mayek Mtei Mende_Kikakui Mend Meroitic_Cursive Merc Meroitic_Hieroglyphs Mero Miao Plrd Modi Mongolian Mong Mro Mroo Multani Mult Myanmar Mymr Nabataean Nbat New_Tai_Lue Talu Newa Newa Nko Nkoo Nushu Nshu Ogham Ogam Ol_Chiki Olck Old_Hungarian Hung Old_Italic Ital Old_North_Arabian Narb Old_Permic Perm Old_Persian Xpeo Old_South_Arabian Sarb Old_Turkic Orkh Oriya Orya Osage Osge Osmanya Osma Pahawh_Hmong Hmng Palmyrene Palm Pau_Cin_Hau Pauc Phags_Pa Phag Phoenician Phnx Psalter_Pahlavi Phlp Rejang Rjng Runic Runr Samaritan Samr Saurashtra Saur Sharada Shrd Shavian Shaw Siddham Sidd SignWriting Sgnw Sinhala Sinh Sora_Sompeng Sora Soyombo Soyo Sundanese Sund Syloti_Nagri Sylo Syriac Syrc Tagalog Tglg Tagbanwa Tagb Tai_Le Tale Tai_Tham Lana Tai_Viet Tavt Takri Takr Tamil Taml Tangut Tang Telugu Telu Thaana Thaa Thai Thai Tibetan Tibt Tifinagh Tfng Tirhuta Tirh Ugaritic Ugar Vai Vaii Warang_Citi Wara Yi Yiii Zanabazar_Square Zanb\";\nvar ecma10ScriptValues = ecma9ScriptValues + \" Dogra Dogr Gunjala_Gondi Gong Hanifi_Rohingya Rohg Makasar Maka Medefaidrin Medf Old_Sogdian Sogo Sogdian Sogd\";\nvar ecma11ScriptValues = ecma10ScriptValues + \" Elymaic Elym Nandinagari Nand Nyiakeng_Puachue_Hmong Hmnp Wancho Wcho\";\nvar ecma12ScriptValues = ecma11ScriptValues + \" Chorasmian Chrs Diak Dives_Akuru Khitan_Small_Script Kits Yezi Yezidi\";\nvar ecma13ScriptValues = ecma12ScriptValues + \" Cypro_Minoan Cpmn Old_Uyghur Ougr Tangsa Tnsa Toto Vithkuqi Vith\";\nvar ecma14ScriptValues = ecma13ScriptValues + \" \" + scriptValuesAddedInUnicode;\n\nvar unicodeScriptValues = {\n  9: ecma9ScriptValues,\n  10: ecma10ScriptValues,\n  11: ecma11ScriptValues,\n  12: ecma12ScriptValues,\n  13: ecma13ScriptValues,\n  14: ecma14ScriptValues\n};\n\nvar data = {};\nfunction buildUnicodeData(ecmaVersion) {\n  var d = data[ecmaVersion] = {\n    binary: wordsRegexp(unicodeBinaryProperties[ecmaVersion] + \" \" + unicodeGeneralCategoryValues),\n    binaryOfStrings: wordsRegexp(unicodeBinaryPropertiesOfStrings[ecmaVersion]),\n    nonBinary: {\n      General_Category: wordsRegexp(unicodeGeneralCategoryValues),\n      Script: wordsRegexp(unicodeScriptValues[ecmaVersion])\n    }\n  };\n  d.nonBinary.Script_Extensions = d.nonBinary.Script;\n\n  d.nonBinary.gc = d.nonBinary.General_Category;\n  d.nonBinary.sc = d.nonBinary.Script;\n  d.nonBinary.scx = d.nonBinary.Script_Extensions;\n}\n\nfor (var i = 0, list = [9, 10, 11, 12, 13, 14]; i < list.length; i += 1) {\n  var ecmaVersion = list[i];\n\n  buildUnicodeData(ecmaVersion);\n}\n\nvar pp$1 = Parser.prototype;\n\n// Track disjunction structure to determine whether a duplicate\n// capture group name is allowed because it is in a separate branch.\nvar BranchID = function BranchID(parent, base) {\n  // Parent disjunction branch\n  this.parent = parent;\n  // Identifies this set of sibling branches\n  this.base = base || this;\n};\n\nBranchID.prototype.separatedFrom = function separatedFrom (alt) {\n  // A branch is separate from another branch if they or any of\n  // their parents are siblings in a given disjunction\n  for (var self = this; self; self = self.parent) {\n    for (var other = alt; other; other = other.parent) {\n      if (self.base === other.base && self !== other) { return true }\n    }\n  }\n  return false\n};\n\nBranchID.prototype.sibling = function sibling () {\n  return new BranchID(this.parent, this.base)\n};\n\nvar RegExpValidationState = function RegExpValidationState(parser) {\n  this.parser = parser;\n  this.validFlags = \"gim\" + (parser.options.ecmaVersion >= 6 ? \"uy\" : \"\") + (parser.options.ecmaVersion >= 9 ? \"s\" : \"\") + (parser.options.ecmaVersion >= 13 ? \"d\" : \"\") + (parser.options.ecmaVersion >= 15 ? \"v\" : \"\");\n  this.unicodeProperties = data[parser.options.ecmaVersion >= 14 ? 14 : parser.options.ecmaVersion];\n  this.source = \"\";\n  this.flags = \"\";\n  this.start = 0;\n  this.switchU = false;\n  this.switchV = false;\n  this.switchN = false;\n  this.pos = 0;\n  this.lastIntValue = 0;\n  this.lastStringValue = \"\";\n  this.lastAssertionIsQuantifiable = false;\n  this.numCapturingParens = 0;\n  this.maxBackReference = 0;\n  this.groupNames = Object.create(null);\n  this.backReferenceNames = [];\n  this.branchID = null;\n};\n\nRegExpValidationState.prototype.reset = function reset (start, pattern, flags) {\n  var unicodeSets = flags.indexOf(\"v\") !== -1;\n  var unicode = flags.indexOf(\"u\") !== -1;\n  this.start = start | 0;\n  this.source = pattern + \"\";\n  this.flags = flags;\n  if (unicodeSets && this.parser.options.ecmaVersion >= 15) {\n    this.switchU = true;\n    this.switchV = true;\n    this.switchN = true;\n  } else {\n    this.switchU = unicode && this.parser.options.ecmaVersion >= 6;\n    this.switchV = false;\n    this.switchN = unicode && this.parser.options.ecmaVersion >= 9;\n  }\n};\n\nRegExpValidationState.prototype.raise = function raise (message) {\n  this.parser.raiseRecoverable(this.start, (\"Invalid regular expression: /\" + (this.source) + \"/: \" + message));\n};\n\n// If u flag is given, this returns the code point at the index (it combines a surrogate pair).\n// Otherwise, this returns the code unit of the index (can be a part of a surrogate pair).\nRegExpValidationState.prototype.at = function at (i, forceU) {\n    if ( forceU === void 0 ) forceU = false;\n\n  var s = this.source;\n  var l = s.length;\n  if (i >= l) {\n    return -1\n  }\n  var c = s.charCodeAt(i);\n  if (!(forceU || this.switchU) || c <= 0xD7FF || c >= 0xE000 || i + 1 >= l) {\n    return c\n  }\n  var next = s.charCodeAt(i + 1);\n  return next >= 0xDC00 && next <= 0xDFFF ? (c << 10) + next - 0x35FDC00 : c\n};\n\nRegExpValidationState.prototype.nextIndex = function nextIndex (i, forceU) {\n    if ( forceU === void 0 ) forceU = false;\n\n  var s = this.source;\n  var l = s.length;\n  if (i >= l) {\n    return l\n  }\n  var c = s.charCodeAt(i), next;\n  if (!(forceU || this.switchU) || c <= 0xD7FF || c >= 0xE000 || i + 1 >= l ||\n      (next = s.charCodeAt(i + 1)) < 0xDC00 || next > 0xDFFF) {\n    return i + 1\n  }\n  return i + 2\n};\n\nRegExpValidationState.prototype.current = function current (forceU) {\n    if ( forceU === void 0 ) forceU = false;\n\n  return this.at(this.pos, forceU)\n};\n\nRegExpValidationState.prototype.lookahead = function lookahead (forceU) {\n    if ( forceU === void 0 ) forceU = false;\n\n  return this.at(this.nextIndex(this.pos, forceU), forceU)\n};\n\nRegExpValidationState.prototype.advance = function advance (forceU) {\n    if ( forceU === void 0 ) forceU = false;\n\n  this.pos = this.nextIndex(this.pos, forceU);\n};\n\nRegExpValidationState.prototype.eat = function eat (ch, forceU) {\n    if ( forceU === void 0 ) forceU = false;\n\n  if (this.current(forceU) === ch) {\n    this.advance(forceU);\n    return true\n  }\n  return false\n};\n\nRegExpValidationState.prototype.eatChars = function eatChars (chs, forceU) {\n    if ( forceU === void 0 ) forceU = false;\n\n  var pos = this.pos;\n  for (var i = 0, list = chs; i < list.length; i += 1) {\n    var ch = list[i];\n\n      var current = this.at(pos, forceU);\n    if (current === -1 || current !== ch) {\n      return false\n    }\n    pos = this.nextIndex(pos, forceU);\n  }\n  this.pos = pos;\n  return true\n};\n\n/**\n * Validate the flags part of a given RegExpLiteral.\n *\n * @param {RegExpValidationState} state The state to validate RegExp.\n * @returns {void}\n */\npp$1.validateRegExpFlags = function(state) {\n  var validFlags = state.validFlags;\n  var flags = state.flags;\n\n  var u = false;\n  var v = false;\n\n  for (var i = 0; i < flags.length; i++) {\n    var flag = flags.charAt(i);\n    if (validFlags.indexOf(flag) === -1) {\n      this.raise(state.start, \"Invalid regular expression flag\");\n    }\n    if (flags.indexOf(flag, i + 1) > -1) {\n      this.raise(state.start, \"Duplicate regular expression flag\");\n    }\n    if (flag === \"u\") { u = true; }\n    if (flag === \"v\") { v = true; }\n  }\n  if (this.options.ecmaVersion >= 15 && u && v) {\n    this.raise(state.start, \"Invalid regular expression flag\");\n  }\n};\n\nfunction hasProp(obj) {\n  for (var _ in obj) { return true }\n  return false\n}\n\n/**\n * Validate the pattern part of a given RegExpLiteral.\n *\n * @param {RegExpValidationState} state The state to validate RegExp.\n * @returns {void}\n */\npp$1.validateRegExpPattern = function(state) {\n  this.regexp_pattern(state);\n\n  // The goal symbol for the parse is |Pattern[~U, ~N]|. If the result of\n  // parsing contains a |GroupName|, reparse with the goal symbol\n  // |Pattern[~U, +N]| and use this result instead. Throw a *SyntaxError*\n  // exception if _P_ did not conform to the grammar, if any elements of _P_\n  // were not matched by the parse, or if any Early Error conditions exist.\n  if (!state.switchN && this.options.ecmaVersion >= 9 && hasProp(state.groupNames)) {\n    state.switchN = true;\n    this.regexp_pattern(state);\n  }\n};\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-Pattern\npp$1.regexp_pattern = function(state) {\n  state.pos = 0;\n  state.lastIntValue = 0;\n  state.lastStringValue = \"\";\n  state.lastAssertionIsQuantifiable = false;\n  state.numCapturingParens = 0;\n  state.maxBackReference = 0;\n  state.groupNames = Object.create(null);\n  state.backReferenceNames.length = 0;\n  state.branchID = null;\n\n  this.regexp_disjunction(state);\n\n  if (state.pos !== state.source.length) {\n    // Make the same messages as V8.\n    if (state.eat(0x29 /* ) */)) {\n      state.raise(\"Unmatched ')'\");\n    }\n    if (state.eat(0x5D /* ] */) || state.eat(0x7D /* } */)) {\n      state.raise(\"Lone quantifier brackets\");\n    }\n  }\n  if (state.maxBackReference > state.numCapturingParens) {\n    state.raise(\"Invalid escape\");\n  }\n  for (var i = 0, list = state.backReferenceNames; i < list.length; i += 1) {\n    var name = list[i];\n\n    if (!state.groupNames[name]) {\n      state.raise(\"Invalid named capture referenced\");\n    }\n  }\n};\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-Disjunction\npp$1.regexp_disjunction = function(state) {\n  var trackDisjunction = this.options.ecmaVersion >= 16;\n  if (trackDisjunction) { state.branchID = new BranchID(state.branchID, null); }\n  this.regexp_alternative(state);\n  while (state.eat(0x7C /* | */)) {\n    if (trackDisjunction) { state.branchID = state.branchID.sibling(); }\n    this.regexp_alternative(state);\n  }\n  if (trackDisjunction) { state.branchID = state.branchID.parent; }\n\n  // Make the same message as V8.\n  if (this.regexp_eatQuantifier(state, true)) {\n    state.raise(\"Nothing to repeat\");\n  }\n  if (state.eat(0x7B /* { */)) {\n    state.raise(\"Lone quantifier brackets\");\n  }\n};\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-Alternative\npp$1.regexp_alternative = function(state) {\n  while (state.pos < state.source.length && this.regexp_eatTerm(state)) {}\n};\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-Term\npp$1.regexp_eatTerm = function(state) {\n  if (this.regexp_eatAssertion(state)) {\n    // Handle `QuantifiableAssertion Quantifier` alternative.\n    // `state.lastAssertionIsQuantifiable` is true if the last eaten Assertion\n    // is a QuantifiableAssertion.\n    if (state.lastAssertionIsQuantifiable && this.regexp_eatQuantifier(state)) {\n      // Make the same message as V8.\n      if (state.switchU) {\n        state.raise(\"Invalid quantifier\");\n      }\n    }\n    return true\n  }\n\n  if (state.switchU ? this.regexp_eatAtom(state) : this.regexp_eatExtendedAtom(state)) {\n    this.regexp_eatQuantifier(state);\n    return true\n  }\n\n  return false\n};\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-Assertion\npp$1.regexp_eatAssertion = function(state) {\n  var start = state.pos;\n  state.lastAssertionIsQuantifiable = false;\n\n  // ^, $\n  if (state.eat(0x5E /* ^ */) || state.eat(0x24 /* $ */)) {\n    return true\n  }\n\n  // \\b \\B\n  if (state.eat(0x5C /* \\ */)) {\n    if (state.eat(0x42 /* B */) || state.eat(0x62 /* b */)) {\n      return true\n    }\n    state.pos = start;\n  }\n\n  // Lookahead / Lookbehind\n  if (state.eat(0x28 /* ( */) && state.eat(0x3F /* ? */)) {\n    var lookbehind = false;\n    if (this.options.ecmaVersion >= 9) {\n      lookbehind = state.eat(0x3C /* < */);\n    }\n    if (state.eat(0x3D /* = */) || state.eat(0x21 /* ! */)) {\n      this.regexp_disjunction(state);\n      if (!state.eat(0x29 /* ) */)) {\n        state.raise(\"Unterminated group\");\n      }\n      state.lastAssertionIsQuantifiable = !lookbehind;\n      return true\n    }\n  }\n\n  state.pos = start;\n  return false\n};\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-Quantifier\npp$1.regexp_eatQuantifier = function(state, noError) {\n  if ( noError === void 0 ) noError = false;\n\n  if (this.regexp_eatQuantifierPrefix(state, noError)) {\n    state.eat(0x3F /* ? */);\n    return true\n  }\n  return false\n};\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-QuantifierPrefix\npp$1.regexp_eatQuantifierPrefix = function(state, noError) {\n  return (\n    state.eat(0x2A /* * */) ||\n    state.eat(0x2B /* + */) ||\n    state.eat(0x3F /* ? */) ||\n    this.regexp_eatBracedQuantifier(state, noError)\n  )\n};\npp$1.regexp_eatBracedQuantifier = function(state, noError) {\n  var start = state.pos;\n  if (state.eat(0x7B /* { */)) {\n    var min = 0, max = -1;\n    if (this.regexp_eatDecimalDigits(state)) {\n      min = state.lastIntValue;\n      if (state.eat(0x2C /* , */) && this.regexp_eatDecimalDigits(state)) {\n        max = state.lastIntValue;\n      }\n      if (state.eat(0x7D /* } */)) {\n        // SyntaxError in https://www.ecma-international.org/ecma-262/8.0/#sec-term\n        if (max !== -1 && max < min && !noError) {\n          state.raise(\"numbers out of order in {} quantifier\");\n        }\n        return true\n      }\n    }\n    if (state.switchU && !noError) {\n      state.raise(\"Incomplete quantifier\");\n    }\n    state.pos = start;\n  }\n  return false\n};\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-Atom\npp$1.regexp_eatAtom = function(state) {\n  return (\n    this.regexp_eatPatternCharacters(state) ||\n    state.eat(0x2E /* . */) ||\n    this.regexp_eatReverseSolidusAtomEscape(state) ||\n    this.regexp_eatCharacterClass(state) ||\n    this.regexp_eatUncapturingGroup(state) ||\n    this.regexp_eatCapturingGroup(state)\n  )\n};\npp$1.regexp_eatReverseSolidusAtomEscape = function(state) {\n  var start = state.pos;\n  if (state.eat(0x5C /* \\ */)) {\n    if (this.regexp_eatAtomEscape(state)) {\n      return true\n    }\n    state.pos = start;\n  }\n  return false\n};\npp$1.regexp_eatUncapturingGroup = function(state) {\n  var start = state.pos;\n  if (state.eat(0x28 /* ( */)) {\n    if (state.eat(0x3F /* ? */)) {\n      if (this.options.ecmaVersion >= 16) {\n        var addModifiers = this.regexp_eatModifiers(state);\n        var hasHyphen = state.eat(0x2D /* - */);\n        if (addModifiers || hasHyphen) {\n          for (var i = 0; i < addModifiers.length; i++) {\n            var modifier = addModifiers.charAt(i);\n            if (addModifiers.indexOf(modifier, i + 1) > -1) {\n              state.raise(\"Duplicate regular expression modifiers\");\n            }\n          }\n          if (hasHyphen) {\n            var removeModifiers = this.regexp_eatModifiers(state);\n            if (!addModifiers && !removeModifiers && state.current() === 0x3A /* : */) {\n              state.raise(\"Invalid regular expression modifiers\");\n            }\n            for (var i$1 = 0; i$1 < removeModifiers.length; i$1++) {\n              var modifier$1 = removeModifiers.charAt(i$1);\n              if (\n                removeModifiers.indexOf(modifier$1, i$1 + 1) > -1 ||\n                addModifiers.indexOf(modifier$1) > -1\n              ) {\n                state.raise(\"Duplicate regular expression modifiers\");\n              }\n            }\n          }\n        }\n      }\n      if (state.eat(0x3A /* : */)) {\n        this.regexp_disjunction(state);\n        if (state.eat(0x29 /* ) */)) {\n          return true\n        }\n        state.raise(\"Unterminated group\");\n      }\n    }\n    state.pos = start;\n  }\n  return false\n};\npp$1.regexp_eatCapturingGroup = function(state) {\n  if (state.eat(0x28 /* ( */)) {\n    if (this.options.ecmaVersion >= 9) {\n      this.regexp_groupSpecifier(state);\n    } else if (state.current() === 0x3F /* ? */) {\n      state.raise(\"Invalid group\");\n    }\n    this.regexp_disjunction(state);\n    if (state.eat(0x29 /* ) */)) {\n      state.numCapturingParens += 1;\n      return true\n    }\n    state.raise(\"Unterminated group\");\n  }\n  return false\n};\n// RegularExpressionModifiers ::\n//   [empty]\n//   RegularExpressionModifiers RegularExpressionModifier\npp$1.regexp_eatModifiers = function(state) {\n  var modifiers = \"\";\n  var ch = 0;\n  while ((ch = state.current()) !== -1 && isRegularExpressionModifier(ch)) {\n    modifiers += codePointToString(ch);\n    state.advance();\n  }\n  return modifiers\n};\n// RegularExpressionModifier :: one of\n//   `i` `m` `s`\nfunction isRegularExpressionModifier(ch) {\n  return ch === 0x69 /* i */ || ch === 0x6d /* m */ || ch === 0x73 /* s */\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-ExtendedAtom\npp$1.regexp_eatExtendedAtom = function(state) {\n  return (\n    state.eat(0x2E /* . */) ||\n    this.regexp_eatReverseSolidusAtomEscape(state) ||\n    this.regexp_eatCharacterClass(state) ||\n    this.regexp_eatUncapturingGroup(state) ||\n    this.regexp_eatCapturingGroup(state) ||\n    this.regexp_eatInvalidBracedQuantifier(state) ||\n    this.regexp_eatExtendedPatternCharacter(state)\n  )\n};\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-InvalidBracedQuantifier\npp$1.regexp_eatInvalidBracedQuantifier = function(state) {\n  if (this.regexp_eatBracedQuantifier(state, true)) {\n    state.raise(\"Nothing to repeat\");\n  }\n  return false\n};\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-SyntaxCharacter\npp$1.regexp_eatSyntaxCharacter = function(state) {\n  var ch = state.current();\n  if (isSyntaxCharacter(ch)) {\n    state.lastIntValue = ch;\n    state.advance();\n    return true\n  }\n  return false\n};\nfunction isSyntaxCharacter(ch) {\n  return (\n    ch === 0x24 /* $ */ ||\n    ch >= 0x28 /* ( */ && ch <= 0x2B /* + */ ||\n    ch === 0x2E /* . */ ||\n    ch === 0x3F /* ? */ ||\n    ch >= 0x5B /* [ */ && ch <= 0x5E /* ^ */ ||\n    ch >= 0x7B /* { */ && ch <= 0x7D /* } */\n  )\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-PatternCharacter\n// But eat eager.\npp$1.regexp_eatPatternCharacters = function(state) {\n  var start = state.pos;\n  var ch = 0;\n  while ((ch = state.current()) !== -1 && !isSyntaxCharacter(ch)) {\n    state.advance();\n  }\n  return state.pos !== start\n};\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-ExtendedPatternCharacter\npp$1.regexp_eatExtendedPatternCharacter = function(state) {\n  var ch = state.current();\n  if (\n    ch !== -1 &&\n    ch !== 0x24 /* $ */ &&\n    !(ch >= 0x28 /* ( */ && ch <= 0x2B /* + */) &&\n    ch !== 0x2E /* . */ &&\n    ch !== 0x3F /* ? */ &&\n    ch !== 0x5B /* [ */ &&\n    ch !== 0x5E /* ^ */ &&\n    ch !== 0x7C /* | */\n  ) {\n    state.advance();\n    return true\n  }\n  return false\n};\n\n// GroupSpecifier ::\n//   [empty]\n//   `?` GroupName\npp$1.regexp_groupSpecifier = function(state) {\n  if (state.eat(0x3F /* ? */)) {\n    if (!this.regexp_eatGroupName(state)) { state.raise(\"Invalid group\"); }\n    var trackDisjunction = this.options.ecmaVersion >= 16;\n    var known = state.groupNames[state.lastStringValue];\n    if (known) {\n      if (trackDisjunction) {\n        for (var i = 0, list = known; i < list.length; i += 1) {\n          var altID = list[i];\n\n          if (!altID.separatedFrom(state.branchID))\n            { state.raise(\"Duplicate capture group name\"); }\n        }\n      } else {\n        state.raise(\"Duplicate capture group name\");\n      }\n    }\n    if (trackDisjunction) {\n      (known || (state.groupNames[state.lastStringValue] = [])).push(state.branchID);\n    } else {\n      state.groupNames[state.lastStringValue] = true;\n    }\n  }\n};\n\n// GroupName ::\n//   `<` RegExpIdentifierName `>`\n// Note: this updates `state.lastStringValue` property with the eaten name.\npp$1.regexp_eatGroupName = function(state) {\n  state.lastStringValue = \"\";\n  if (state.eat(0x3C /* < */)) {\n    if (this.regexp_eatRegExpIdentifierName(state) && state.eat(0x3E /* > */)) {\n      return true\n    }\n    state.raise(\"Invalid capture group name\");\n  }\n  return false\n};\n\n// RegExpIdentifierName ::\n//   RegExpIdentifierStart\n//   RegExpIdentifierName RegExpIdentifierPart\n// Note: this updates `state.lastStringValue` property with the eaten name.\npp$1.regexp_eatRegExpIdentifierName = function(state) {\n  state.lastStringValue = \"\";\n  if (this.regexp_eatRegExpIdentifierStart(state)) {\n    state.lastStringValue += codePointToString(state.lastIntValue);\n    while (this.regexp_eatRegExpIdentifierPart(state)) {\n      state.lastStringValue += codePointToString(state.lastIntValue);\n    }\n    return true\n  }\n  return false\n};\n\n// RegExpIdentifierStart ::\n//   UnicodeIDStart\n//   `$`\n//   `_`\n//   `\\` RegExpUnicodeEscapeSequence[+U]\npp$1.regexp_eatRegExpIdentifierStart = function(state) {\n  var start = state.pos;\n  var forceU = this.options.ecmaVersion >= 11;\n  var ch = state.current(forceU);\n  state.advance(forceU);\n\n  if (ch === 0x5C /* \\ */ && this.regexp_eatRegExpUnicodeEscapeSequence(state, forceU)) {\n    ch = state.lastIntValue;\n  }\n  if (isRegExpIdentifierStart(ch)) {\n    state.lastIntValue = ch;\n    return true\n  }\n\n  state.pos = start;\n  return false\n};\nfunction isRegExpIdentifierStart(ch) {\n  return isIdentifierStart(ch, true) || ch === 0x24 /* $ */ || ch === 0x5F /* _ */\n}\n\n// RegExpIdentifierPart ::\n//   UnicodeIDContinue\n//   `$`\n//   `_`\n//   `\\` RegExpUnicodeEscapeSequence[+U]\n//   <ZWNJ>\n//   <ZWJ>\npp$1.regexp_eatRegExpIdentifierPart = function(state) {\n  var start = state.pos;\n  var forceU = this.options.ecmaVersion >= 11;\n  var ch = state.current(forceU);\n  state.advance(forceU);\n\n  if (ch === 0x5C /* \\ */ && this.regexp_eatRegExpUnicodeEscapeSequence(state, forceU)) {\n    ch = state.lastIntValue;\n  }\n  if (isRegExpIdentifierPart(ch)) {\n    state.lastIntValue = ch;\n    return true\n  }\n\n  state.pos = start;\n  return false\n};\nfunction isRegExpIdentifierPart(ch) {\n  return isIdentifierChar(ch, true) || ch === 0x24 /* $ */ || ch === 0x5F /* _ */ || ch === 0x200C /* <ZWNJ> */ || ch === 0x200D /* <ZWJ> */\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-AtomEscape\npp$1.regexp_eatAtomEscape = function(state) {\n  if (\n    this.regexp_eatBackReference(state) ||\n    this.regexp_eatCharacterClassEscape(state) ||\n    this.regexp_eatCharacterEscape(state) ||\n    (state.switchN && this.regexp_eatKGroupName(state))\n  ) {\n    return true\n  }\n  if (state.switchU) {\n    // Make the same message as V8.\n    if (state.current() === 0x63 /* c */) {\n      state.raise(\"Invalid unicode escape\");\n    }\n    state.raise(\"Invalid escape\");\n  }\n  return false\n};\npp$1.regexp_eatBackReference = function(state) {\n  var start = state.pos;\n  if (this.regexp_eatDecimalEscape(state)) {\n    var n = state.lastIntValue;\n    if (state.switchU) {\n      // For SyntaxError in https://www.ecma-international.org/ecma-262/8.0/#sec-atomescape\n      if (n > state.maxBackReference) {\n        state.maxBackReference = n;\n      }\n      return true\n    }\n    if (n <= state.numCapturingParens) {\n      return true\n    }\n    state.pos = start;\n  }\n  return false\n};\npp$1.regexp_eatKGroupName = function(state) {\n  if (state.eat(0x6B /* k */)) {\n    if (this.regexp_eatGroupName(state)) {\n      state.backReferenceNames.push(state.lastStringValue);\n      return true\n    }\n    state.raise(\"Invalid named reference\");\n  }\n  return false\n};\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-CharacterEscape\npp$1.regexp_eatCharacterEscape = function(state) {\n  return (\n    this.regexp_eatControlEscape(state) ||\n    this.regexp_eatCControlLetter(state) ||\n    this.regexp_eatZero(state) ||\n    this.regexp_eatHexEscapeSequence(state) ||\n    this.regexp_eatRegExpUnicodeEscapeSequence(state, false) ||\n    (!state.switchU && this.regexp_eatLegacyOctalEscapeSequence(state)) ||\n    this.regexp_eatIdentityEscape(state)\n  )\n};\npp$1.regexp_eatCControlLetter = function(state) {\n  var start = state.pos;\n  if (state.eat(0x63 /* c */)) {\n    if (this.regexp_eatControlLetter(state)) {\n      return true\n    }\n    state.pos = start;\n  }\n  return false\n};\npp$1.regexp_eatZero = function(state) {\n  if (state.current() === 0x30 /* 0 */ && !isDecimalDigit(state.lookahead())) {\n    state.lastIntValue = 0;\n    state.advance();\n    return true\n  }\n  return false\n};\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-ControlEscape\npp$1.regexp_eatControlEscape = function(state) {\n  var ch = state.current();\n  if (ch === 0x74 /* t */) {\n    state.lastIntValue = 0x09; /* \\t */\n    state.advance();\n    return true\n  }\n  if (ch === 0x6E /* n */) {\n    state.lastIntValue = 0x0A; /* \\n */\n    state.advance();\n    return true\n  }\n  if (ch === 0x76 /* v */) {\n    state.lastIntValue = 0x0B; /* \\v */\n    state.advance();\n    return true\n  }\n  if (ch === 0x66 /* f */) {\n    state.lastIntValue = 0x0C; /* \\f */\n    state.advance();\n    return true\n  }\n  if (ch === 0x72 /* r */) {\n    state.lastIntValue = 0x0D; /* \\r */\n    state.advance();\n    return true\n  }\n  return false\n};\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-ControlLetter\npp$1.regexp_eatControlLetter = function(state) {\n  var ch = state.current();\n  if (isControlLetter(ch)) {\n    state.lastIntValue = ch % 0x20;\n    state.advance();\n    return true\n  }\n  return false\n};\nfunction isControlLetter(ch) {\n  return (\n    (ch >= 0x41 /* A */ && ch <= 0x5A /* Z */) ||\n    (ch >= 0x61 /* a */ && ch <= 0x7A /* z */)\n  )\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-RegExpUnicodeEscapeSequence\npp$1.regexp_eatRegExpUnicodeEscapeSequence = function(state, forceU) {\n  if ( forceU === void 0 ) forceU = false;\n\n  var start = state.pos;\n  var switchU = forceU || state.switchU;\n\n  if (state.eat(0x75 /* u */)) {\n    if (this.regexp_eatFixedHexDigits(state, 4)) {\n      var lead = state.lastIntValue;\n      if (switchU && lead >= 0xD800 && lead <= 0xDBFF) {\n        var leadSurrogateEnd = state.pos;\n        if (state.eat(0x5C /* \\ */) && state.eat(0x75 /* u */) && this.regexp_eatFixedHexDigits(state, 4)) {\n          var trail = state.lastIntValue;\n          if (trail >= 0xDC00 && trail <= 0xDFFF) {\n            state.lastIntValue = (lead - 0xD800) * 0x400 + (trail - 0xDC00) + 0x10000;\n            return true\n          }\n        }\n        state.pos = leadSurrogateEnd;\n        state.lastIntValue = lead;\n      }\n      return true\n    }\n    if (\n      switchU &&\n      state.eat(0x7B /* { */) &&\n      this.regexp_eatHexDigits(state) &&\n      state.eat(0x7D /* } */) &&\n      isValidUnicode(state.lastIntValue)\n    ) {\n      return true\n    }\n    if (switchU) {\n      state.raise(\"Invalid unicode escape\");\n    }\n    state.pos = start;\n  }\n\n  return false\n};\nfunction isValidUnicode(ch) {\n  return ch >= 0 && ch <= 0x10FFFF\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-IdentityEscape\npp$1.regexp_eatIdentityEscape = function(state) {\n  if (state.switchU) {\n    if (this.regexp_eatSyntaxCharacter(state)) {\n      return true\n    }\n    if (state.eat(0x2F /* / */)) {\n      state.lastIntValue = 0x2F; /* / */\n      return true\n    }\n    return false\n  }\n\n  var ch = state.current();\n  if (ch !== 0x63 /* c */ && (!state.switchN || ch !== 0x6B /* k */)) {\n    state.lastIntValue = ch;\n    state.advance();\n    return true\n  }\n\n  return false\n};\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-DecimalEscape\npp$1.regexp_eatDecimalEscape = function(state) {\n  state.lastIntValue = 0;\n  var ch = state.current();\n  if (ch >= 0x31 /* 1 */ && ch <= 0x39 /* 9 */) {\n    do {\n      state.lastIntValue = 10 * state.lastIntValue + (ch - 0x30 /* 0 */);\n      state.advance();\n    } while ((ch = state.current()) >= 0x30 /* 0 */ && ch <= 0x39 /* 9 */)\n    return true\n  }\n  return false\n};\n\n// Return values used by character set parsing methods, needed to\n// forbid negation of sets that can match strings.\nvar CharSetNone = 0; // Nothing parsed\nvar CharSetOk = 1; // Construct parsed, cannot contain strings\nvar CharSetString = 2; // Construct parsed, can contain strings\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-CharacterClassEscape\npp$1.regexp_eatCharacterClassEscape = function(state) {\n  var ch = state.current();\n\n  if (isCharacterClassEscape(ch)) {\n    state.lastIntValue = -1;\n    state.advance();\n    return CharSetOk\n  }\n\n  var negate = false;\n  if (\n    state.switchU &&\n    this.options.ecmaVersion >= 9 &&\n    ((negate = ch === 0x50 /* P */) || ch === 0x70 /* p */)\n  ) {\n    state.lastIntValue = -1;\n    state.advance();\n    var result;\n    if (\n      state.eat(0x7B /* { */) &&\n      (result = this.regexp_eatUnicodePropertyValueExpression(state)) &&\n      state.eat(0x7D /* } */)\n    ) {\n      if (negate && result === CharSetString) { state.raise(\"Invalid property name\"); }\n      return result\n    }\n    state.raise(\"Invalid property name\");\n  }\n\n  return CharSetNone\n};\n\nfunction isCharacterClassEscape(ch) {\n  return (\n    ch === 0x64 /* d */ ||\n    ch === 0x44 /* D */ ||\n    ch === 0x73 /* s */ ||\n    ch === 0x53 /* S */ ||\n    ch === 0x77 /* w */ ||\n    ch === 0x57 /* W */\n  )\n}\n\n// UnicodePropertyValueExpression ::\n//   UnicodePropertyName `=` UnicodePropertyValue\n//   LoneUnicodePropertyNameOrValue\npp$1.regexp_eatUnicodePropertyValueExpression = function(state) {\n  var start = state.pos;\n\n  // UnicodePropertyName `=` UnicodePropertyValue\n  if (this.regexp_eatUnicodePropertyName(state) && state.eat(0x3D /* = */)) {\n    var name = state.lastStringValue;\n    if (this.regexp_eatUnicodePropertyValue(state)) {\n      var value = state.lastStringValue;\n      this.regexp_validateUnicodePropertyNameAndValue(state, name, value);\n      return CharSetOk\n    }\n  }\n  state.pos = start;\n\n  // LoneUnicodePropertyNameOrValue\n  if (this.regexp_eatLoneUnicodePropertyNameOrValue(state)) {\n    var nameOrValue = state.lastStringValue;\n    return this.regexp_validateUnicodePropertyNameOrValue(state, nameOrValue)\n  }\n  return CharSetNone\n};\n\npp$1.regexp_validateUnicodePropertyNameAndValue = function(state, name, value) {\n  if (!hasOwn(state.unicodeProperties.nonBinary, name))\n    { state.raise(\"Invalid property name\"); }\n  if (!state.unicodeProperties.nonBinary[name].test(value))\n    { state.raise(\"Invalid property value\"); }\n};\n\npp$1.regexp_validateUnicodePropertyNameOrValue = function(state, nameOrValue) {\n  if (state.unicodeProperties.binary.test(nameOrValue)) { return CharSetOk }\n  if (state.switchV && state.unicodeProperties.binaryOfStrings.test(nameOrValue)) { return CharSetString }\n  state.raise(\"Invalid property name\");\n};\n\n// UnicodePropertyName ::\n//   UnicodePropertyNameCharacters\npp$1.regexp_eatUnicodePropertyName = function(state) {\n  var ch = 0;\n  state.lastStringValue = \"\";\n  while (isUnicodePropertyNameCharacter(ch = state.current())) {\n    state.lastStringValue += codePointToString(ch);\n    state.advance();\n  }\n  return state.lastStringValue !== \"\"\n};\n\nfunction isUnicodePropertyNameCharacter(ch) {\n  return isControlLetter(ch) || ch === 0x5F /* _ */\n}\n\n// UnicodePropertyValue ::\n//   UnicodePropertyValueCharacters\npp$1.regexp_eatUnicodePropertyValue = function(state) {\n  var ch = 0;\n  state.lastStringValue = \"\";\n  while (isUnicodePropertyValueCharacter(ch = state.current())) {\n    state.lastStringValue += codePointToString(ch);\n    state.advance();\n  }\n  return state.lastStringValue !== \"\"\n};\nfunction isUnicodePropertyValueCharacter(ch) {\n  return isUnicodePropertyNameCharacter(ch) || isDecimalDigit(ch)\n}\n\n// LoneUnicodePropertyNameOrValue ::\n//   UnicodePropertyValueCharacters\npp$1.regexp_eatLoneUnicodePropertyNameOrValue = function(state) {\n  return this.regexp_eatUnicodePropertyValue(state)\n};\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-CharacterClass\npp$1.regexp_eatCharacterClass = function(state) {\n  if (state.eat(0x5B /* [ */)) {\n    var negate = state.eat(0x5E /* ^ */);\n    var result = this.regexp_classContents(state);\n    if (!state.eat(0x5D /* ] */))\n      { state.raise(\"Unterminated character class\"); }\n    if (negate && result === CharSetString)\n      { state.raise(\"Negated character class may contain strings\"); }\n    return true\n  }\n  return false\n};\n\n// https://tc39.es/ecma262/#prod-ClassContents\n// https://www.ecma-international.org/ecma-262/8.0/#prod-ClassRanges\npp$1.regexp_classContents = function(state) {\n  if (state.current() === 0x5D /* ] */) { return CharSetOk }\n  if (state.switchV) { return this.regexp_classSetExpression(state) }\n  this.regexp_nonEmptyClassRanges(state);\n  return CharSetOk\n};\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-NonemptyClassRanges\n// https://www.ecma-international.org/ecma-262/8.0/#prod-NonemptyClassRangesNoDash\npp$1.regexp_nonEmptyClassRanges = function(state) {\n  while (this.regexp_eatClassAtom(state)) {\n    var left = state.lastIntValue;\n    if (state.eat(0x2D /* - */) && this.regexp_eatClassAtom(state)) {\n      var right = state.lastIntValue;\n      if (state.switchU && (left === -1 || right === -1)) {\n        state.raise(\"Invalid character class\");\n      }\n      if (left !== -1 && right !== -1 && left > right) {\n        state.raise(\"Range out of order in character class\");\n      }\n    }\n  }\n};\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-ClassAtom\n// https://www.ecma-international.org/ecma-262/8.0/#prod-ClassAtomNoDash\npp$1.regexp_eatClassAtom = function(state) {\n  var start = state.pos;\n\n  if (state.eat(0x5C /* \\ */)) {\n    if (this.regexp_eatClassEscape(state)) {\n      return true\n    }\n    if (state.switchU) {\n      // Make the same message as V8.\n      var ch$1 = state.current();\n      if (ch$1 === 0x63 /* c */ || isOctalDigit(ch$1)) {\n        state.raise(\"Invalid class escape\");\n      }\n      state.raise(\"Invalid escape\");\n    }\n    state.pos = start;\n  }\n\n  var ch = state.current();\n  if (ch !== 0x5D /* ] */) {\n    state.lastIntValue = ch;\n    state.advance();\n    return true\n  }\n\n  return false\n};\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-ClassEscape\npp$1.regexp_eatClassEscape = function(state) {\n  var start = state.pos;\n\n  if (state.eat(0x62 /* b */)) {\n    state.lastIntValue = 0x08; /* <BS> */\n    return true\n  }\n\n  if (state.switchU && state.eat(0x2D /* - */)) {\n    state.lastIntValue = 0x2D; /* - */\n    return true\n  }\n\n  if (!state.switchU && state.eat(0x63 /* c */)) {\n    if (this.regexp_eatClassControlLetter(state)) {\n      return true\n    }\n    state.pos = start;\n  }\n\n  return (\n    this.regexp_eatCharacterClassEscape(state) ||\n    this.regexp_eatCharacterEscape(state)\n  )\n};\n\n// https://tc39.es/ecma262/#prod-ClassSetExpression\n// https://tc39.es/ecma262/#prod-ClassUnion\n// https://tc39.es/ecma262/#prod-ClassIntersection\n// https://tc39.es/ecma262/#prod-ClassSubtraction\npp$1.regexp_classSetExpression = function(state) {\n  var result = CharSetOk, subResult;\n  if (this.regexp_eatClassSetRange(state)) ; else if (subResult = this.regexp_eatClassSetOperand(state)) {\n    if (subResult === CharSetString) { result = CharSetString; }\n    // https://tc39.es/ecma262/#prod-ClassIntersection\n    var start = state.pos;\n    while (state.eatChars([0x26, 0x26] /* && */)) {\n      if (\n        state.current() !== 0x26 /* & */ &&\n        (subResult = this.regexp_eatClassSetOperand(state))\n      ) {\n        if (subResult !== CharSetString) { result = CharSetOk; }\n        continue\n      }\n      state.raise(\"Invalid character in character class\");\n    }\n    if (start !== state.pos) { return result }\n    // https://tc39.es/ecma262/#prod-ClassSubtraction\n    while (state.eatChars([0x2D, 0x2D] /* -- */)) {\n      if (this.regexp_eatClassSetOperand(state)) { continue }\n      state.raise(\"Invalid character in character class\");\n    }\n    if (start !== state.pos) { return result }\n  } else {\n    state.raise(\"Invalid character in character class\");\n  }\n  // https://tc39.es/ecma262/#prod-ClassUnion\n  for (;;) {\n    if (this.regexp_eatClassSetRange(state)) { continue }\n    subResult = this.regexp_eatClassSetOperand(state);\n    if (!subResult) { return result }\n    if (subResult === CharSetString) { result = CharSetString; }\n  }\n};\n\n// https://tc39.es/ecma262/#prod-ClassSetRange\npp$1.regexp_eatClassSetRange = function(state) {\n  var start = state.pos;\n  if (this.regexp_eatClassSetCharacter(state)) {\n    var left = state.lastIntValue;\n    if (state.eat(0x2D /* - */) && this.regexp_eatClassSetCharacter(state)) {\n      var right = state.lastIntValue;\n      if (left !== -1 && right !== -1 && left > right) {\n        state.raise(\"Range out of order in character class\");\n      }\n      return true\n    }\n    state.pos = start;\n  }\n  return false\n};\n\n// https://tc39.es/ecma262/#prod-ClassSetOperand\npp$1.regexp_eatClassSetOperand = function(state) {\n  if (this.regexp_eatClassSetCharacter(state)) { return CharSetOk }\n  return this.regexp_eatClassStringDisjunction(state) || this.regexp_eatNestedClass(state)\n};\n\n// https://tc39.es/ecma262/#prod-NestedClass\npp$1.regexp_eatNestedClass = function(state) {\n  var start = state.pos;\n  if (state.eat(0x5B /* [ */)) {\n    var negate = state.eat(0x5E /* ^ */);\n    var result = this.regexp_classContents(state);\n    if (state.eat(0x5D /* ] */)) {\n      if (negate && result === CharSetString) {\n        state.raise(\"Negated character class may contain strings\");\n      }\n      return result\n    }\n    state.pos = start;\n  }\n  if (state.eat(0x5C /* \\ */)) {\n    var result$1 = this.regexp_eatCharacterClassEscape(state);\n    if (result$1) {\n      return result$1\n    }\n    state.pos = start;\n  }\n  return null\n};\n\n// https://tc39.es/ecma262/#prod-ClassStringDisjunction\npp$1.regexp_eatClassStringDisjunction = function(state) {\n  var start = state.pos;\n  if (state.eatChars([0x5C, 0x71] /* \\q */)) {\n    if (state.eat(0x7B /* { */)) {\n      var result = this.regexp_classStringDisjunctionContents(state);\n      if (state.eat(0x7D /* } */)) {\n        return result\n      }\n    } else {\n      // Make the same message as V8.\n      state.raise(\"Invalid escape\");\n    }\n    state.pos = start;\n  }\n  return null\n};\n\n// https://tc39.es/ecma262/#prod-ClassStringDisjunctionContents\npp$1.regexp_classStringDisjunctionContents = function(state) {\n  var result = this.regexp_classString(state);\n  while (state.eat(0x7C /* | */)) {\n    if (this.regexp_classString(state) === CharSetString) { result = CharSetString; }\n  }\n  return result\n};\n\n// https://tc39.es/ecma262/#prod-ClassString\n// https://tc39.es/ecma262/#prod-NonEmptyClassString\npp$1.regexp_classString = function(state) {\n  var count = 0;\n  while (this.regexp_eatClassSetCharacter(state)) { count++; }\n  return count === 1 ? CharSetOk : CharSetString\n};\n\n// https://tc39.es/ecma262/#prod-ClassSetCharacter\npp$1.regexp_eatClassSetCharacter = function(state) {\n  var start = state.pos;\n  if (state.eat(0x5C /* \\ */)) {\n    if (\n      this.regexp_eatCharacterEscape(state) ||\n      this.regexp_eatClassSetReservedPunctuator(state)\n    ) {\n      return true\n    }\n    if (state.eat(0x62 /* b */)) {\n      state.lastIntValue = 0x08; /* <BS> */\n      return true\n    }\n    state.pos = start;\n    return false\n  }\n  var ch = state.current();\n  if (ch < 0 || ch === state.lookahead() && isClassSetReservedDoublePunctuatorCharacter(ch)) { return false }\n  if (isClassSetSyntaxCharacter(ch)) { return false }\n  state.advance();\n  state.lastIntValue = ch;\n  return true\n};\n\n// https://tc39.es/ecma262/#prod-ClassSetReservedDoublePunctuator\nfunction isClassSetReservedDoublePunctuatorCharacter(ch) {\n  return (\n    ch === 0x21 /* ! */ ||\n    ch >= 0x23 /* # */ && ch <= 0x26 /* & */ ||\n    ch >= 0x2A /* * */ && ch <= 0x2C /* , */ ||\n    ch === 0x2E /* . */ ||\n    ch >= 0x3A /* : */ && ch <= 0x40 /* @ */ ||\n    ch === 0x5E /* ^ */ ||\n    ch === 0x60 /* ` */ ||\n    ch === 0x7E /* ~ */\n  )\n}\n\n// https://tc39.es/ecma262/#prod-ClassSetSyntaxCharacter\nfunction isClassSetSyntaxCharacter(ch) {\n  return (\n    ch === 0x28 /* ( */ ||\n    ch === 0x29 /* ) */ ||\n    ch === 0x2D /* - */ ||\n    ch === 0x2F /* / */ ||\n    ch >= 0x5B /* [ */ && ch <= 0x5D /* ] */ ||\n    ch >= 0x7B /* { */ && ch <= 0x7D /* } */\n  )\n}\n\n// https://tc39.es/ecma262/#prod-ClassSetReservedPunctuator\npp$1.regexp_eatClassSetReservedPunctuator = function(state) {\n  var ch = state.current();\n  if (isClassSetReservedPunctuator(ch)) {\n    state.lastIntValue = ch;\n    state.advance();\n    return true\n  }\n  return false\n};\n\n// https://tc39.es/ecma262/#prod-ClassSetReservedPunctuator\nfunction isClassSetReservedPunctuator(ch) {\n  return (\n    ch === 0x21 /* ! */ ||\n    ch === 0x23 /* # */ ||\n    ch === 0x25 /* % */ ||\n    ch === 0x26 /* & */ ||\n    ch === 0x2C /* , */ ||\n    ch === 0x2D /* - */ ||\n    ch >= 0x3A /* : */ && ch <= 0x3E /* > */ ||\n    ch === 0x40 /* @ */ ||\n    ch === 0x60 /* ` */ ||\n    ch === 0x7E /* ~ */\n  )\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-ClassControlLetter\npp$1.regexp_eatClassControlLetter = function(state) {\n  var ch = state.current();\n  if (isDecimalDigit(ch) || ch === 0x5F /* _ */) {\n    state.lastIntValue = ch % 0x20;\n    state.advance();\n    return true\n  }\n  return false\n};\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-HexEscapeSequence\npp$1.regexp_eatHexEscapeSequence = function(state) {\n  var start = state.pos;\n  if (state.eat(0x78 /* x */)) {\n    if (this.regexp_eatFixedHexDigits(state, 2)) {\n      return true\n    }\n    if (state.switchU) {\n      state.raise(\"Invalid escape\");\n    }\n    state.pos = start;\n  }\n  return false\n};\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-DecimalDigits\npp$1.regexp_eatDecimalDigits = function(state) {\n  var start = state.pos;\n  var ch = 0;\n  state.lastIntValue = 0;\n  while (isDecimalDigit(ch = state.current())) {\n    state.lastIntValue = 10 * state.lastIntValue + (ch - 0x30 /* 0 */);\n    state.advance();\n  }\n  return state.pos !== start\n};\nfunction isDecimalDigit(ch) {\n  return ch >= 0x30 /* 0 */ && ch <= 0x39 /* 9 */\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-HexDigits\npp$1.regexp_eatHexDigits = function(state) {\n  var start = state.pos;\n  var ch = 0;\n  state.lastIntValue = 0;\n  while (isHexDigit(ch = state.current())) {\n    state.lastIntValue = 16 * state.lastIntValue + hexToInt(ch);\n    state.advance();\n  }\n  return state.pos !== start\n};\nfunction isHexDigit(ch) {\n  return (\n    (ch >= 0x30 /* 0 */ && ch <= 0x39 /* 9 */) ||\n    (ch >= 0x41 /* A */ && ch <= 0x46 /* F */) ||\n    (ch >= 0x61 /* a */ && ch <= 0x66 /* f */)\n  )\n}\nfunction hexToInt(ch) {\n  if (ch >= 0x41 /* A */ && ch <= 0x46 /* F */) {\n    return 10 + (ch - 0x41 /* A */)\n  }\n  if (ch >= 0x61 /* a */ && ch <= 0x66 /* f */) {\n    return 10 + (ch - 0x61 /* a */)\n  }\n  return ch - 0x30 /* 0 */\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-annexB-LegacyOctalEscapeSequence\n// Allows only 0-377(octal) i.e. 0-255(decimal).\npp$1.regexp_eatLegacyOctalEscapeSequence = function(state) {\n  if (this.regexp_eatOctalDigit(state)) {\n    var n1 = state.lastIntValue;\n    if (this.regexp_eatOctalDigit(state)) {\n      var n2 = state.lastIntValue;\n      if (n1 <= 3 && this.regexp_eatOctalDigit(state)) {\n        state.lastIntValue = n1 * 64 + n2 * 8 + state.lastIntValue;\n      } else {\n        state.lastIntValue = n1 * 8 + n2;\n      }\n    } else {\n      state.lastIntValue = n1;\n    }\n    return true\n  }\n  return false\n};\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-OctalDigit\npp$1.regexp_eatOctalDigit = function(state) {\n  var ch = state.current();\n  if (isOctalDigit(ch)) {\n    state.lastIntValue = ch - 0x30; /* 0 */\n    state.advance();\n    return true\n  }\n  state.lastIntValue = 0;\n  return false\n};\nfunction isOctalDigit(ch) {\n  return ch >= 0x30 /* 0 */ && ch <= 0x37 /* 7 */\n}\n\n// https://www.ecma-international.org/ecma-262/8.0/#prod-Hex4Digits\n// https://www.ecma-international.org/ecma-262/8.0/#prod-HexDigit\n// And HexDigit HexDigit in https://www.ecma-international.org/ecma-262/8.0/#prod-HexEscapeSequence\npp$1.regexp_eatFixedHexDigits = function(state, length) {\n  var start = state.pos;\n  state.lastIntValue = 0;\n  for (var i = 0; i < length; ++i) {\n    var ch = state.current();\n    if (!isHexDigit(ch)) {\n      state.pos = start;\n      return false\n    }\n    state.lastIntValue = 16 * state.lastIntValue + hexToInt(ch);\n    state.advance();\n  }\n  return true\n};\n\n// Object type used to represent tokens. Note that normally, tokens\n// simply exist as properties on the parser object. This is only\n// used for the onToken callback and the external tokenizer.\n\nvar Token = function Token(p) {\n  this.type = p.type;\n  this.value = p.value;\n  this.start = p.start;\n  this.end = p.end;\n  if (p.options.locations)\n    { this.loc = new SourceLocation(p, p.startLoc, p.endLoc); }\n  if (p.options.ranges)\n    { this.range = [p.start, p.end]; }\n};\n\n// ## Tokenizer\n\nvar pp = Parser.prototype;\n\n// Move to the next token\n\npp.next = function(ignoreEscapeSequenceInKeyword) {\n  if (!ignoreEscapeSequenceInKeyword && this.type.keyword && this.containsEsc)\n    { this.raiseRecoverable(this.start, \"Escape sequence in keyword \" + this.type.keyword); }\n  if (this.options.onToken)\n    { this.options.onToken(new Token(this)); }\n\n  this.lastTokEnd = this.end;\n  this.lastTokStart = this.start;\n  this.lastTokEndLoc = this.endLoc;\n  this.lastTokStartLoc = this.startLoc;\n  this.nextToken();\n};\n\npp.getToken = function() {\n  this.next();\n  return new Token(this)\n};\n\n// If we're in an ES6 environment, make parsers iterable\nif (typeof Symbol !== \"undefined\")\n  { pp[Symbol.iterator] = function() {\n    var this$1$1 = this;\n\n    return {\n      next: function () {\n        var token = this$1$1.getToken();\n        return {\n          done: token.type === types$1.eof,\n          value: token\n        }\n      }\n    }\n  }; }\n\n// Toggle strict mode. Re-reads the next number or string to please\n// pedantic tests (`\"use strict\"; 010;` should fail).\n\n// Read a single token, updating the parser object's token-related\n// properties.\n\npp.nextToken = function() {\n  var curContext = this.curContext();\n  if (!curContext || !curContext.preserveSpace) { this.skipSpace(); }\n\n  this.start = this.pos;\n  if (this.options.locations) { this.startLoc = this.curPosition(); }\n  if (this.pos >= this.input.length) { return this.finishToken(types$1.eof) }\n\n  if (curContext.override) { return curContext.override(this) }\n  else { this.readToken(this.fullCharCodeAtPos()); }\n};\n\npp.readToken = function(code) {\n  // Identifier or keyword. '\\uXXXX' sequences are allowed in\n  // identifiers, so '\\' also dispatches to that.\n  if (isIdentifierStart(code, this.options.ecmaVersion >= 6) || code === 92 /* '\\' */)\n    { return this.readWord() }\n\n  return this.getTokenFromCode(code)\n};\n\npp.fullCharCodeAtPos = function() {\n  var code = this.input.charCodeAt(this.pos);\n  if (code <= 0xd7ff || code >= 0xdc00) { return code }\n  var next = this.input.charCodeAt(this.pos + 1);\n  return next <= 0xdbff || next >= 0xe000 ? code : (code << 10) + next - 0x35fdc00\n};\n\npp.skipBlockComment = function() {\n  var startLoc = this.options.onComment && this.curPosition();\n  var start = this.pos, end = this.input.indexOf(\"*/\", this.pos += 2);\n  if (end === -1) { this.raise(this.pos - 2, \"Unterminated comment\"); }\n  this.pos = end + 2;\n  if (this.options.locations) {\n    for (var nextBreak = (void 0), pos = start; (nextBreak = nextLineBreak(this.input, pos, this.pos)) > -1;) {\n      ++this.curLine;\n      pos = this.lineStart = nextBreak;\n    }\n  }\n  if (this.options.onComment)\n    { this.options.onComment(true, this.input.slice(start + 2, end), start, this.pos,\n                           startLoc, this.curPosition()); }\n};\n\npp.skipLineComment = function(startSkip) {\n  var start = this.pos;\n  var startLoc = this.options.onComment && this.curPosition();\n  var ch = this.input.charCodeAt(this.pos += startSkip);\n  while (this.pos < this.input.length && !isNewLine(ch)) {\n    ch = this.input.charCodeAt(++this.pos);\n  }\n  if (this.options.onComment)\n    { this.options.onComment(false, this.input.slice(start + startSkip, this.pos), start, this.pos,\n                           startLoc, this.curPosition()); }\n};\n\n// Called at the start of the parse and after every token. Skips\n// whitespace and comments, and.\n\npp.skipSpace = function() {\n  loop: while (this.pos < this.input.length) {\n    var ch = this.input.charCodeAt(this.pos);\n    switch (ch) {\n    case 32: case 160: // ' '\n      ++this.pos;\n      break\n    case 13:\n      if (this.input.charCodeAt(this.pos + 1) === 10) {\n        ++this.pos;\n      }\n    case 10: case 8232: case 8233:\n      ++this.pos;\n      if (this.options.locations) {\n        ++this.curLine;\n        this.lineStart = this.pos;\n      }\n      break\n    case 47: // '/'\n      switch (this.input.charCodeAt(this.pos + 1)) {\n      case 42: // '*'\n        this.skipBlockComment();\n        break\n      case 47:\n        this.skipLineComment(2);\n        break\n      default:\n        break loop\n      }\n      break\n    default:\n      if (ch > 8 && ch < 14 || ch >= 5760 && nonASCIIwhitespace.test(String.fromCharCode(ch))) {\n        ++this.pos;\n      } else {\n        break loop\n      }\n    }\n  }\n};\n\n// Called at the end of every token. Sets `end`, `val`, and\n// maintains `context` and `exprAllowed`, and skips the space after\n// the token, so that the next one's `start` will point at the\n// right position.\n\npp.finishToken = function(type, val) {\n  this.end = this.pos;\n  if (this.options.locations) { this.endLoc = this.curPosition(); }\n  var prevType = this.type;\n  this.type = type;\n  this.value = val;\n\n  this.updateContext(prevType);\n};\n\n// ### Token reading\n\n// This is the function that is called to fetch the next token. It\n// is somewhat obscure, because it works in character codes rather\n// than characters, and because operator parsing has been inlined\n// into it.\n//\n// All in the name of speed.\n//\npp.readToken_dot = function() {\n  var next = this.input.charCodeAt(this.pos + 1);\n  if (next >= 48 && next <= 57) { return this.readNumber(true) }\n  var next2 = this.input.charCodeAt(this.pos + 2);\n  if (this.options.ecmaVersion >= 6 && next === 46 && next2 === 46) { // 46 = dot '.'\n    this.pos += 3;\n    return this.finishToken(types$1.ellipsis)\n  } else {\n    ++this.pos;\n    return this.finishToken(types$1.dot)\n  }\n};\n\npp.readToken_slash = function() { // '/'\n  var next = this.input.charCodeAt(this.pos + 1);\n  if (this.exprAllowed) { ++this.pos; return this.readRegexp() }\n  if (next === 61) { return this.finishOp(types$1.assign, 2) }\n  return this.finishOp(types$1.slash, 1)\n};\n\npp.readToken_mult_modulo_exp = function(code) { // '%*'\n  var next = this.input.charCodeAt(this.pos + 1);\n  var size = 1;\n  var tokentype = code === 42 ? types$1.star : types$1.modulo;\n\n  // exponentiation operator ** and **=\n  if (this.options.ecmaVersion >= 7 && code === 42 && next === 42) {\n    ++size;\n    tokentype = types$1.starstar;\n    next = this.input.charCodeAt(this.pos + 2);\n  }\n\n  if (next === 61) { return this.finishOp(types$1.assign, size + 1) }\n  return this.finishOp(tokentype, size)\n};\n\npp.readToken_pipe_amp = function(code) { // '|&'\n  var next = this.input.charCodeAt(this.pos + 1);\n  if (next === code) {\n    if (this.options.ecmaVersion >= 12) {\n      var next2 = this.input.charCodeAt(this.pos + 2);\n      if (next2 === 61) { return this.finishOp(types$1.assign, 3) }\n    }\n    return this.finishOp(code === 124 ? types$1.logicalOR : types$1.logicalAND, 2)\n  }\n  if (next === 61) { return this.finishOp(types$1.assign, 2) }\n  return this.finishOp(code === 124 ? types$1.bitwiseOR : types$1.bitwiseAND, 1)\n};\n\npp.readToken_caret = function() { // '^'\n  var next = this.input.charCodeAt(this.pos + 1);\n  if (next === 61) { return this.finishOp(types$1.assign, 2) }\n  return this.finishOp(types$1.bitwiseXOR, 1)\n};\n\npp.readToken_plus_min = function(code) { // '+-'\n  var next = this.input.charCodeAt(this.pos + 1);\n  if (next === code) {\n    if (next === 45 && !this.inModule && this.input.charCodeAt(this.pos + 2) === 62 &&\n        (this.lastTokEnd === 0 || lineBreak.test(this.input.slice(this.lastTokEnd, this.pos)))) {\n      // A `-->` line comment\n      this.skipLineComment(3);\n      this.skipSpace();\n      return this.nextToken()\n    }\n    return this.finishOp(types$1.incDec, 2)\n  }\n  if (next === 61) { return this.finishOp(types$1.assign, 2) }\n  return this.finishOp(types$1.plusMin, 1)\n};\n\npp.readToken_lt_gt = function(code) { // '<>'\n  var next = this.input.charCodeAt(this.pos + 1);\n  var size = 1;\n  if (next === code) {\n    size = code === 62 && this.input.charCodeAt(this.pos + 2) === 62 ? 3 : 2;\n    if (this.input.charCodeAt(this.pos + size) === 61) { return this.finishOp(types$1.assign, size + 1) }\n    return this.finishOp(types$1.bitShift, size)\n  }\n  if (next === 33 && code === 60 && !this.inModule && this.input.charCodeAt(this.pos + 2) === 45 &&\n      this.input.charCodeAt(this.pos + 3) === 45) {\n    // `<!--`, an XML-style comment that should be interpreted as a line comment\n    this.skipLineComment(4);\n    this.skipSpace();\n    return this.nextToken()\n  }\n  if (next === 61) { size = 2; }\n  return this.finishOp(types$1.relational, size)\n};\n\npp.readToken_eq_excl = function(code) { // '=!'\n  var next = this.input.charCodeAt(this.pos + 1);\n  if (next === 61) { return this.finishOp(types$1.equality, this.input.charCodeAt(this.pos + 2) === 61 ? 3 : 2) }\n  if (code === 61 && next === 62 && this.options.ecmaVersion >= 6) { // '=>'\n    this.pos += 2;\n    return this.finishToken(types$1.arrow)\n  }\n  return this.finishOp(code === 61 ? types$1.eq : types$1.prefix, 1)\n};\n\npp.readToken_question = function() { // '?'\n  var ecmaVersion = this.options.ecmaVersion;\n  if (ecmaVersion >= 11) {\n    var next = this.input.charCodeAt(this.pos + 1);\n    if (next === 46) {\n      var next2 = this.input.charCodeAt(this.pos + 2);\n      if (next2 < 48 || next2 > 57) { return this.finishOp(types$1.questionDot, 2) }\n    }\n    if (next === 63) {\n      if (ecmaVersion >= 12) {\n        var next2$1 = this.input.charCodeAt(this.pos + 2);\n        if (next2$1 === 61) { return this.finishOp(types$1.assign, 3) }\n      }\n      return this.finishOp(types$1.coalesce, 2)\n    }\n  }\n  return this.finishOp(types$1.question, 1)\n};\n\npp.readToken_numberSign = function() { // '#'\n  var ecmaVersion = this.options.ecmaVersion;\n  var code = 35; // '#'\n  if (ecmaVersion >= 13) {\n    ++this.pos;\n    code = this.fullCharCodeAtPos();\n    if (isIdentifierStart(code, true) || code === 92 /* '\\' */) {\n      return this.finishToken(types$1.privateId, this.readWord1())\n    }\n  }\n\n  this.raise(this.pos, \"Unexpected character '\" + codePointToString(code) + \"'\");\n};\n\npp.getTokenFromCode = function(code) {\n  switch (code) {\n  // The interpretation of a dot depends on whether it is followed\n  // by a digit or another two dots.\n  case 46: // '.'\n    return this.readToken_dot()\n\n  // Punctuation tokens.\n  case 40: ++this.pos; return this.finishToken(types$1.parenL)\n  case 41: ++this.pos; return this.finishToken(types$1.parenR)\n  case 59: ++this.pos; return this.finishToken(types$1.semi)\n  case 44: ++this.pos; return this.finishToken(types$1.comma)\n  case 91: ++this.pos; return this.finishToken(types$1.bracketL)\n  case 93: ++this.pos; return this.finishToken(types$1.bracketR)\n  case 123: ++this.pos; return this.finishToken(types$1.braceL)\n  case 125: ++this.pos; return this.finishToken(types$1.braceR)\n  case 58: ++this.pos; return this.finishToken(types$1.colon)\n\n  case 96: // '`'\n    if (this.options.ecmaVersion < 6) { break }\n    ++this.pos;\n    return this.finishToken(types$1.backQuote)\n\n  case 48: // '0'\n    var next = this.input.charCodeAt(this.pos + 1);\n    if (next === 120 || next === 88) { return this.readRadixNumber(16) } // '0x', '0X' - hex number\n    if (this.options.ecmaVersion >= 6) {\n      if (next === 111 || next === 79) { return this.readRadixNumber(8) } // '0o', '0O' - octal number\n      if (next === 98 || next === 66) { return this.readRadixNumber(2) } // '0b', '0B' - binary number\n    }\n\n  // Anything else beginning with a digit is an integer, octal\n  // number, or float.\n  case 49: case 50: case 51: case 52: case 53: case 54: case 55: case 56: case 57: // 1-9\n    return this.readNumber(false)\n\n  // Quotes produce strings.\n  case 34: case 39: // '\"', \"'\"\n    return this.readString(code)\n\n  // Operators are parsed inline in tiny state machines. '=' (61) is\n  // often referred to. `finishOp` simply skips the amount of\n  // characters it is given as second argument, and returns a token\n  // of the type given by its first argument.\n  case 47: // '/'\n    return this.readToken_slash()\n\n  case 37: case 42: // '%*'\n    return this.readToken_mult_modulo_exp(code)\n\n  case 124: case 38: // '|&'\n    return this.readToken_pipe_amp(code)\n\n  case 94: // '^'\n    return this.readToken_caret()\n\n  case 43: case 45: // '+-'\n    return this.readToken_plus_min(code)\n\n  case 60: case 62: // '<>'\n    return this.readToken_lt_gt(code)\n\n  case 61: case 33: // '=!'\n    return this.readToken_eq_excl(code)\n\n  case 63: // '?'\n    return this.readToken_question()\n\n  case 126: // '~'\n    return this.finishOp(types$1.prefix, 1)\n\n  case 35: // '#'\n    return this.readToken_numberSign()\n  }\n\n  this.raise(this.pos, \"Unexpected character '\" + codePointToString(code) + \"'\");\n};\n\npp.finishOp = function(type, size) {\n  var str = this.input.slice(this.pos, this.pos + size);\n  this.pos += size;\n  return this.finishToken(type, str)\n};\n\npp.readRegexp = function() {\n  var escaped, inClass, start = this.pos;\n  for (;;) {\n    if (this.pos >= this.input.length) { this.raise(start, \"Unterminated regular expression\"); }\n    var ch = this.input.charAt(this.pos);\n    if (lineBreak.test(ch)) { this.raise(start, \"Unterminated regular expression\"); }\n    if (!escaped) {\n      if (ch === \"[\") { inClass = true; }\n      else if (ch === \"]\" && inClass) { inClass = false; }\n      else if (ch === \"/\" && !inClass) { break }\n      escaped = ch === \"\\\\\";\n    } else { escaped = false; }\n    ++this.pos;\n  }\n  var pattern = this.input.slice(start, this.pos);\n  ++this.pos;\n  var flagsStart = this.pos;\n  var flags = this.readWord1();\n  if (this.containsEsc) { this.unexpected(flagsStart); }\n\n  // Validate pattern\n  var state = this.regexpState || (this.regexpState = new RegExpValidationState(this));\n  state.reset(start, pattern, flags);\n  this.validateRegExpFlags(state);\n  this.validateRegExpPattern(state);\n\n  // Create Literal#value property value.\n  var value = null;\n  try {\n    value = new RegExp(pattern, flags);\n  } catch (e) {\n    // ESTree requires null if it failed to instantiate RegExp object.\n    // https://github.com/estree/estree/blob/a27003adf4fd7bfad44de9cef372a2eacd527b1c/es5.md#regexpliteral\n  }\n\n  return this.finishToken(types$1.regexp, {pattern: pattern, flags: flags, value: value})\n};\n\n// Read an integer in the given radix. Return null if zero digits\n// were read, the integer value otherwise. When `len` is given, this\n// will return `null` unless the integer has exactly `len` digits.\n\npp.readInt = function(radix, len, maybeLegacyOctalNumericLiteral) {\n  // `len` is used for character escape sequences. In that case, disallow separators.\n  var allowSeparators = this.options.ecmaVersion >= 12 && len === undefined;\n\n  // `maybeLegacyOctalNumericLiteral` is true if it doesn't have prefix (0x,0o,0b)\n  // and isn't fraction part nor exponent part. In that case, if the first digit\n  // is zero then disallow separators.\n  var isLegacyOctalNumericLiteral = maybeLegacyOctalNumericLiteral && this.input.charCodeAt(this.pos) === 48;\n\n  var start = this.pos, total = 0, lastCode = 0;\n  for (var i = 0, e = len == null ? Infinity : len; i < e; ++i, ++this.pos) {\n    var code = this.input.charCodeAt(this.pos), val = (void 0);\n\n    if (allowSeparators && code === 95) {\n      if (isLegacyOctalNumericLiteral) { this.raiseRecoverable(this.pos, \"Numeric separator is not allowed in legacy octal numeric literals\"); }\n      if (lastCode === 95) { this.raiseRecoverable(this.pos, \"Numeric separator must be exactly one underscore\"); }\n      if (i === 0) { this.raiseRecoverable(this.pos, \"Numeric separator is not allowed at the first of digits\"); }\n      lastCode = code;\n      continue\n    }\n\n    if (code >= 97) { val = code - 97 + 10; } // a\n    else if (code >= 65) { val = code - 65 + 10; } // A\n    else if (code >= 48 && code <= 57) { val = code - 48; } // 0-9\n    else { val = Infinity; }\n    if (val >= radix) { break }\n    lastCode = code;\n    total = total * radix + val;\n  }\n\n  if (allowSeparators && lastCode === 95) { this.raiseRecoverable(this.pos - 1, \"Numeric separator is not allowed at the last of digits\"); }\n  if (this.pos === start || len != null && this.pos - start !== len) { return null }\n\n  return total\n};\n\nfunction stringToNumber(str, isLegacyOctalNumericLiteral) {\n  if (isLegacyOctalNumericLiteral) {\n    return parseInt(str, 8)\n  }\n\n  // `parseFloat(value)` stops parsing at the first numeric separator then returns a wrong value.\n  return parseFloat(str.replace(/_/g, \"\"))\n}\n\nfunction stringToBigInt(str) {\n  if (typeof BigInt !== \"function\") {\n    return null\n  }\n\n  // `BigInt(value)` throws syntax error if the string contains numeric separators.\n  return BigInt(str.replace(/_/g, \"\"))\n}\n\npp.readRadixNumber = function(radix) {\n  var start = this.pos;\n  this.pos += 2; // 0x\n  var val = this.readInt(radix);\n  if (val == null) { this.raise(this.start + 2, \"Expected number in radix \" + radix); }\n  if (this.options.ecmaVersion >= 11 && this.input.charCodeAt(this.pos) === 110) {\n    val = stringToBigInt(this.input.slice(start, this.pos));\n    ++this.pos;\n  } else if (isIdentifierStart(this.fullCharCodeAtPos())) { this.raise(this.pos, \"Identifier directly after number\"); }\n  return this.finishToken(types$1.num, val)\n};\n\n// Read an integer, octal integer, or floating-point number.\n\npp.readNumber = function(startsWithDot) {\n  var start = this.pos;\n  if (!startsWithDot && this.readInt(10, undefined, true) === null) { this.raise(start, \"Invalid number\"); }\n  var octal = this.pos - start >= 2 && this.input.charCodeAt(start) === 48;\n  if (octal && this.strict) { this.raise(start, \"Invalid number\"); }\n  var next = this.input.charCodeAt(this.pos);\n  if (!octal && !startsWithDot && this.options.ecmaVersion >= 11 && next === 110) {\n    var val$1 = stringToBigInt(this.input.slice(start, this.pos));\n    ++this.pos;\n    if (isIdentifierStart(this.fullCharCodeAtPos())) { this.raise(this.pos, \"Identifier directly after number\"); }\n    return this.finishToken(types$1.num, val$1)\n  }\n  if (octal && /[89]/.test(this.input.slice(start, this.pos))) { octal = false; }\n  if (next === 46 && !octal) { // '.'\n    ++this.pos;\n    this.readInt(10);\n    next = this.input.charCodeAt(this.pos);\n  }\n  if ((next === 69 || next === 101) && !octal) { // 'eE'\n    next = this.input.charCodeAt(++this.pos);\n    if (next === 43 || next === 45) { ++this.pos; } // '+-'\n    if (this.readInt(10) === null) { this.raise(start, \"Invalid number\"); }\n  }\n  if (isIdentifierStart(this.fullCharCodeAtPos())) { this.raise(this.pos, \"Identifier directly after number\"); }\n\n  var val = stringToNumber(this.input.slice(start, this.pos), octal);\n  return this.finishToken(types$1.num, val)\n};\n\n// Read a string value, interpreting backslash-escapes.\n\npp.readCodePoint = function() {\n  var ch = this.input.charCodeAt(this.pos), code;\n\n  if (ch === 123) { // '{'\n    if (this.options.ecmaVersion < 6) { this.unexpected(); }\n    var codePos = ++this.pos;\n    code = this.readHexChar(this.input.indexOf(\"}\", this.pos) - this.pos);\n    ++this.pos;\n    if (code > 0x10FFFF) { this.invalidStringToken(codePos, \"Code point out of bounds\"); }\n  } else {\n    code = this.readHexChar(4);\n  }\n  return code\n};\n\npp.readString = function(quote) {\n  var out = \"\", chunkStart = ++this.pos;\n  for (;;) {\n    if (this.pos >= this.input.length) { this.raise(this.start, \"Unterminated string constant\"); }\n    var ch = this.input.charCodeAt(this.pos);\n    if (ch === quote) { break }\n    if (ch === 92) { // '\\'\n      out += this.input.slice(chunkStart, this.pos);\n      out += this.readEscapedChar(false);\n      chunkStart = this.pos;\n    } else if (ch === 0x2028 || ch === 0x2029) {\n      if (this.options.ecmaVersion < 10) { this.raise(this.start, \"Unterminated string constant\"); }\n      ++this.pos;\n      if (this.options.locations) {\n        this.curLine++;\n        this.lineStart = this.pos;\n      }\n    } else {\n      if (isNewLine(ch)) { this.raise(this.start, \"Unterminated string constant\"); }\n      ++this.pos;\n    }\n  }\n  out += this.input.slice(chunkStart, this.pos++);\n  return this.finishToken(types$1.string, out)\n};\n\n// Reads template string tokens.\n\nvar INVALID_TEMPLATE_ESCAPE_ERROR = {};\n\npp.tryReadTemplateToken = function() {\n  this.inTemplateElement = true;\n  try {\n    this.readTmplToken();\n  } catch (err) {\n    if (err === INVALID_TEMPLATE_ESCAPE_ERROR) {\n      this.readInvalidTemplateToken();\n    } else {\n      throw err\n    }\n  }\n\n  this.inTemplateElement = false;\n};\n\npp.invalidStringToken = function(position, message) {\n  if (this.inTemplateElement && this.options.ecmaVersion >= 9) {\n    throw INVALID_TEMPLATE_ESCAPE_ERROR\n  } else {\n    this.raise(position, message);\n  }\n};\n\npp.readTmplToken = function() {\n  var out = \"\", chunkStart = this.pos;\n  for (;;) {\n    if (this.pos >= this.input.length) { this.raise(this.start, \"Unterminated template\"); }\n    var ch = this.input.charCodeAt(this.pos);\n    if (ch === 96 || ch === 36 && this.input.charCodeAt(this.pos + 1) === 123) { // '`', '${'\n      if (this.pos === this.start && (this.type === types$1.template || this.type === types$1.invalidTemplate)) {\n        if (ch === 36) {\n          this.pos += 2;\n          return this.finishToken(types$1.dollarBraceL)\n        } else {\n          ++this.pos;\n          return this.finishToken(types$1.backQuote)\n        }\n      }\n      out += this.input.slice(chunkStart, this.pos);\n      return this.finishToken(types$1.template, out)\n    }\n    if (ch === 92) { // '\\'\n      out += this.input.slice(chunkStart, this.pos);\n      out += this.readEscapedChar(true);\n      chunkStart = this.pos;\n    } else if (isNewLine(ch)) {\n      out += this.input.slice(chunkStart, this.pos);\n      ++this.pos;\n      switch (ch) {\n      case 13:\n        if (this.input.charCodeAt(this.pos) === 10) { ++this.pos; }\n      case 10:\n        out += \"\\n\";\n        break\n      default:\n        out += String.fromCharCode(ch);\n        break\n      }\n      if (this.options.locations) {\n        ++this.curLine;\n        this.lineStart = this.pos;\n      }\n      chunkStart = this.pos;\n    } else {\n      ++this.pos;\n    }\n  }\n};\n\n// Reads a template token to search for the end, without validating any escape sequences\npp.readInvalidTemplateToken = function() {\n  for (; this.pos < this.input.length; this.pos++) {\n    switch (this.input[this.pos]) {\n    case \"\\\\\":\n      ++this.pos;\n      break\n\n    case \"$\":\n      if (this.input[this.pos + 1] !== \"{\") { break }\n      // fall through\n    case \"`\":\n      return this.finishToken(types$1.invalidTemplate, this.input.slice(this.start, this.pos))\n\n    case \"\\r\":\n      if (this.input[this.pos + 1] === \"\\n\") { ++this.pos; }\n      // fall through\n    case \"\\n\": case \"\\u2028\": case \"\\u2029\":\n      ++this.curLine;\n      this.lineStart = this.pos + 1;\n      break\n    }\n  }\n  this.raise(this.start, \"Unterminated template\");\n};\n\n// Used to read escaped characters\n\npp.readEscapedChar = function(inTemplate) {\n  var ch = this.input.charCodeAt(++this.pos);\n  ++this.pos;\n  switch (ch) {\n  case 110: return \"\\n\" // 'n' -> '\\n'\n  case 114: return \"\\r\" // 'r' -> '\\r'\n  case 120: return String.fromCharCode(this.readHexChar(2)) // 'x'\n  case 117: return codePointToString(this.readCodePoint()) // 'u'\n  case 116: return \"\\t\" // 't' -> '\\t'\n  case 98: return \"\\b\" // 'b' -> '\\b'\n  case 118: return \"\\u000b\" // 'v' -> '\\u000b'\n  case 102: return \"\\f\" // 'f' -> '\\f'\n  case 13: if (this.input.charCodeAt(this.pos) === 10) { ++this.pos; } // '\\r\\n'\n  case 10: // ' \\n'\n    if (this.options.locations) { this.lineStart = this.pos; ++this.curLine; }\n    return \"\"\n  case 56:\n  case 57:\n    if (this.strict) {\n      this.invalidStringToken(\n        this.pos - 1,\n        \"Invalid escape sequence\"\n      );\n    }\n    if (inTemplate) {\n      var codePos = this.pos - 1;\n\n      this.invalidStringToken(\n        codePos,\n        \"Invalid escape sequence in template string\"\n      );\n    }\n  default:\n    if (ch >= 48 && ch <= 55) {\n      var octalStr = this.input.substr(this.pos - 1, 3).match(/^[0-7]+/)[0];\n      var octal = parseInt(octalStr, 8);\n      if (octal > 255) {\n        octalStr = octalStr.slice(0, -1);\n        octal = parseInt(octalStr, 8);\n      }\n      this.pos += octalStr.length - 1;\n      ch = this.input.charCodeAt(this.pos);\n      if ((octalStr !== \"0\" || ch === 56 || ch === 57) && (this.strict || inTemplate)) {\n        this.invalidStringToken(\n          this.pos - 1 - octalStr.length,\n          inTemplate\n            ? \"Octal literal in template string\"\n            : \"Octal literal in strict mode\"\n        );\n      }\n      return String.fromCharCode(octal)\n    }\n    if (isNewLine(ch)) {\n      // Unicode new line characters after \\ get removed from output in both\n      // template literals and strings\n      if (this.options.locations) { this.lineStart = this.pos; ++this.curLine; }\n      return \"\"\n    }\n    return String.fromCharCode(ch)\n  }\n};\n\n// Used to read character escape sequences ('\\x', '\\u', '\\U').\n\npp.readHexChar = function(len) {\n  var codePos = this.pos;\n  var n = this.readInt(16, len);\n  if (n === null) { this.invalidStringToken(codePos, \"Bad character escape sequence\"); }\n  return n\n};\n\n// Read an identifier, and return it as a string. Sets `this.containsEsc`\n// to whether the word contained a '\\u' escape.\n//\n// Incrementally adds only escaped chars, adding other chunks as-is\n// as a micro-optimization.\n\npp.readWord1 = function() {\n  this.containsEsc = false;\n  var word = \"\", first = true, chunkStart = this.pos;\n  var astral = this.options.ecmaVersion >= 6;\n  while (this.pos < this.input.length) {\n    var ch = this.fullCharCodeAtPos();\n    if (isIdentifierChar(ch, astral)) {\n      this.pos += ch <= 0xffff ? 1 : 2;\n    } else if (ch === 92) { // \"\\\"\n      this.containsEsc = true;\n      word += this.input.slice(chunkStart, this.pos);\n      var escStart = this.pos;\n      if (this.input.charCodeAt(++this.pos) !== 117) // \"u\"\n        { this.invalidStringToken(this.pos, \"Expecting Unicode escape sequence \\\\uXXXX\"); }\n      ++this.pos;\n      var esc = this.readCodePoint();\n      if (!(first ? isIdentifierStart : isIdentifierChar)(esc, astral))\n        { this.invalidStringToken(escStart, \"Invalid Unicode escape\"); }\n      word += codePointToString(esc);\n      chunkStart = this.pos;\n    } else {\n      break\n    }\n    first = false;\n  }\n  return word + this.input.slice(chunkStart, this.pos)\n};\n\n// Read an identifier or keyword token. Will check for reserved\n// words when necessary.\n\npp.readWord = function() {\n  var word = this.readWord1();\n  var type = types$1.name;\n  if (this.keywords.test(word)) {\n    type = keywords[word];\n  }\n  return this.finishToken(type, word)\n};\n\n// Acorn is a tiny, fast JavaScript parser written in JavaScript.\n//\n// Acorn was written by Marijn Haverbeke, Ingvar Stepanyan, and\n// various contributors and released under an MIT license.\n//\n// Git repositories for Acorn are available at\n//\n//     http://marijnhaverbeke.nl/git/acorn\n//     https://github.com/acornjs/acorn.git\n//\n// Please use the [github bug tracker][ghbt] to report issues.\n//\n// [ghbt]: https://github.com/acornjs/acorn/issues\n\n\nvar version = \"8.15.0\";\n\nParser.acorn = {\n  Parser: Parser,\n  version: version,\n  defaultOptions: defaultOptions,\n  Position: Position,\n  SourceLocation: SourceLocation,\n  getLineInfo: getLineInfo,\n  Node: Node,\n  TokenType: TokenType,\n  tokTypes: types$1,\n  keywordTypes: keywords,\n  TokContext: TokContext,\n  tokContexts: types,\n  isIdentifierChar: isIdentifierChar,\n  isIdentifierStart: isIdentifierStart,\n  Token: Token,\n  isNewLine: isNewLine,\n  lineBreak: lineBreak,\n  lineBreakG: lineBreakG,\n  nonASCIIwhitespace: nonASCIIwhitespace\n};\n\n// The main exported interface (under `self.acorn` when in the\n// browser) is a `parse` function that takes a code string and returns\n// an abstract syntax tree as specified by the [ESTree spec][estree].\n//\n// [estree]: https://github.com/estree/estree\n\nfunction parse(input, options) {\n  return Parser.parse(input, options)\n}\n\n// This function tries to parse a single expression at a given\n// offset in a string. Useful for parsing mixed-language formats\n// that embed JavaScript expressions.\n\nfunction parseExpressionAt(input, pos, options) {\n  return Parser.parseExpressionAt(input, pos, options)\n}\n\n// Acorn is organized as a tokenizer and a recursive-descent parser.\n// The `tokenizer` export provides an interface to the tokenizer.\n\nfunction tokenizer(input, options) {\n  return Parser.tokenizer(input, options)\n}\n\nexport { Node, Parser, Position, SourceLocation, TokContext, Token, TokenType, defaultOptions, getLineInfo, isIdentifierChar, isIdentifierStart, isNewLine, keywords as keywordTypes, lineBreak, lineBreakG, nonASCIIwhitespace, parse, parseExpressionAt, types as tokContexts, types$1 as tokTypes, tokenizer, version };\n"], "mappings": ";;;AACA,IAAI,wBAAwB,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,<PERSON>G,<PERSON>G,<PERSON>G,GAAG,<PERSON>G,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK,IAAI,KAAK,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,KAAK,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,MAAM,GAAG,GAAG,IAAI,OAAO,IAAI,MAAM,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,OAAO,GAAG,MAAM,GAAG,KAAK,IAAI,GAAG,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,KAAK,GAAG,MAAM,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,MAAM,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,KAAK,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,MAAM,GAAG,QAAQ,GAAG;AAGzoC,IAAI,6BAA6B,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,MAAM,IAAI,IAAI,MAAM,IAAI,GAAG,IAAI,MAAM,GAAG,KAAK,MAAM,IAAI,MAAM,KAAK,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,MAAM,GAAG,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,KAAK,MAAM,KAAK,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,MAAM,IAAI,GAAG,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,GAAG,MAAM,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,MAAM,OAAO,IAAI,MAAM,GAAG,KAAK,GAAG,MAAM,IAAI,MAAM,IAAI,KAAK,MAAM,KAAK,MAAM,MAAM,GAAG,IAAI;AAGnpE,IAAI,0BAA0B;AAG9B,IAAI,+BAA+B;AASnC,IAAI,gBAAgB;AAAA,EAClB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,QAAQ;AAAA,EACR,YAAY;AACd;AAIA,IAAI,uBAAuB;AAE3B,IAAI,aAAa;AAAA,EACf,GAAG;AAAA,EACH,WAAW,uBAAuB;AAAA,EAClC,GAAG,uBAAuB;AAC5B;AAEA,IAAI,4BAA4B;AAIhC,IAAI,0BAA0B,IAAI,OAAO,MAAM,+BAA+B,GAAG;AACjF,IAAI,qBAAqB,IAAI,OAAO,MAAM,+BAA+B,0BAA0B,GAAG;AAKtG,SAAS,cAAc,MAAM,KAAK;AAChC,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AACtC,WAAO,IAAI,CAAC;AACZ,QAAI,MAAM,MAAM;AAAE,aAAO;AAAA,IAAM;AAC/B,WAAO,IAAI,IAAI,CAAC;AAChB,QAAI,OAAO,MAAM;AAAE,aAAO;AAAA,IAAK;AAAA,EACjC;AACA,SAAO;AACT;AAIA,SAAS,kBAAkB,MAAM,QAAQ;AACvC,MAAI,OAAO,IAAI;AAAE,WAAO,SAAS;AAAA,EAAG;AACpC,MAAI,OAAO,IAAI;AAAE,WAAO;AAAA,EAAK;AAC7B,MAAI,OAAO,IAAI;AAAE,WAAO,SAAS;AAAA,EAAG;AACpC,MAAI,OAAO,KAAK;AAAE,WAAO;AAAA,EAAK;AAC9B,MAAI,QAAQ,OAAQ;AAAE,WAAO,QAAQ,OAAQ,wBAAwB,KAAK,OAAO,aAAa,IAAI,CAAC;AAAA,EAAE;AACrG,MAAI,WAAW,OAAO;AAAE,WAAO;AAAA,EAAM;AACrC,SAAO,cAAc,MAAM,0BAA0B;AACvD;AAIA,SAAS,iBAAiB,MAAM,QAAQ;AACtC,MAAI,OAAO,IAAI;AAAE,WAAO,SAAS;AAAA,EAAG;AACpC,MAAI,OAAO,IAAI;AAAE,WAAO;AAAA,EAAK;AAC7B,MAAI,OAAO,IAAI;AAAE,WAAO;AAAA,EAAM;AAC9B,MAAI,OAAO,IAAI;AAAE,WAAO;AAAA,EAAK;AAC7B,MAAI,OAAO,IAAI;AAAE,WAAO,SAAS;AAAA,EAAG;AACpC,MAAI,OAAO,KAAK;AAAE,WAAO;AAAA,EAAK;AAC9B,MAAI,QAAQ,OAAQ;AAAE,WAAO,QAAQ,OAAQ,mBAAmB,KAAK,OAAO,aAAa,IAAI,CAAC;AAAA,EAAE;AAChG,MAAI,WAAW,OAAO;AAAE,WAAO;AAAA,EAAM;AACrC,SAAO,cAAc,MAAM,0BAA0B,KAAK,cAAc,MAAM,qBAAqB;AACrG;AAyBA,IAAI,YAAY,SAASA,WAAU,OAAO,MAAM;AAC9C,MAAK,SAAS,OAAS,QAAO,CAAC;AAE/B,OAAK,QAAQ;AACb,OAAK,UAAU,KAAK;AACpB,OAAK,aAAa,CAAC,CAAC,KAAK;AACzB,OAAK,aAAa,CAAC,CAAC,KAAK;AACzB,OAAK,SAAS,CAAC,CAAC,KAAK;AACrB,OAAK,WAAW,CAAC,CAAC,KAAK;AACvB,OAAK,SAAS,CAAC,CAAC,KAAK;AACrB,OAAK,UAAU,CAAC,CAAC,KAAK;AACtB,OAAK,QAAQ,KAAK,SAAS;AAC3B,OAAK,gBAAgB;AACvB;AAEA,SAAS,MAAM,MAAM,MAAM;AACzB,SAAO,IAAI,UAAU,MAAM,EAAC,YAAY,MAAM,OAAO,KAAI,CAAC;AAC5D;AACA,IAAI,aAAa,EAAC,YAAY,KAAI;AAAlC,IAAqC,aAAa,EAAC,YAAY,KAAI;AAInE,IAAI,WAAW,CAAC;AAGhB,SAAS,GAAG,MAAM,SAAS;AACzB,MAAK,YAAY,OAAS,WAAU,CAAC;AAErC,UAAQ,UAAU;AAClB,SAAO,SAAS,IAAI,IAAI,IAAI,UAAU,MAAM,OAAO;AACrD;AAEA,IAAI,UAAU;AAAA,EACZ,KAAK,IAAI,UAAU,OAAO,UAAU;AAAA,EACpC,QAAQ,IAAI,UAAU,UAAU,UAAU;AAAA,EAC1C,QAAQ,IAAI,UAAU,UAAU,UAAU;AAAA,EAC1C,MAAM,IAAI,UAAU,QAAQ,UAAU;AAAA,EACtC,WAAW,IAAI,UAAU,aAAa,UAAU;AAAA,EAChD,KAAK,IAAI,UAAU,KAAK;AAAA;AAAA,EAGxB,UAAU,IAAI,UAAU,KAAK,EAAC,YAAY,MAAM,YAAY,KAAI,CAAC;AAAA,EACjE,UAAU,IAAI,UAAU,GAAG;AAAA,EAC3B,QAAQ,IAAI,UAAU,KAAK,EAAC,YAAY,MAAM,YAAY,KAAI,CAAC;AAAA,EAC/D,QAAQ,IAAI,UAAU,GAAG;AAAA,EACzB,QAAQ,IAAI,UAAU,KAAK,EAAC,YAAY,MAAM,YAAY,KAAI,CAAC;AAAA,EAC/D,QAAQ,IAAI,UAAU,GAAG;AAAA,EACzB,OAAO,IAAI,UAAU,KAAK,UAAU;AAAA,EACpC,MAAM,IAAI,UAAU,KAAK,UAAU;AAAA,EACnC,OAAO,IAAI,UAAU,KAAK,UAAU;AAAA,EACpC,KAAK,IAAI,UAAU,GAAG;AAAA,EACtB,UAAU,IAAI,UAAU,KAAK,UAAU;AAAA,EACvC,aAAa,IAAI,UAAU,IAAI;AAAA,EAC/B,OAAO,IAAI,UAAU,MAAM,UAAU;AAAA,EACrC,UAAU,IAAI,UAAU,UAAU;AAAA,EAClC,iBAAiB,IAAI,UAAU,iBAAiB;AAAA,EAChD,UAAU,IAAI,UAAU,OAAO,UAAU;AAAA,EACzC,WAAW,IAAI,UAAU,KAAK,UAAU;AAAA,EACxC,cAAc,IAAI,UAAU,MAAM,EAAC,YAAY,MAAM,YAAY,KAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBtE,IAAI,IAAI,UAAU,KAAK,EAAC,YAAY,MAAM,UAAU,KAAI,CAAC;AAAA,EACzD,QAAQ,IAAI,UAAU,MAAM,EAAC,YAAY,MAAM,UAAU,KAAI,CAAC;AAAA,EAC9D,QAAQ,IAAI,UAAU,SAAS,EAAC,QAAQ,MAAM,SAAS,MAAM,YAAY,KAAI,CAAC;AAAA,EAC9E,QAAQ,IAAI,UAAU,OAAO,EAAC,YAAY,MAAM,QAAQ,MAAM,YAAY,KAAI,CAAC;AAAA,EAC/E,WAAW,MAAM,MAAM,CAAC;AAAA,EACxB,YAAY,MAAM,MAAM,CAAC;AAAA,EACzB,WAAW,MAAM,KAAK,CAAC;AAAA,EACvB,YAAY,MAAM,KAAK,CAAC;AAAA,EACxB,YAAY,MAAM,KAAK,CAAC;AAAA,EACxB,UAAU,MAAM,iBAAiB,CAAC;AAAA,EAClC,YAAY,MAAM,aAAa,CAAC;AAAA,EAChC,UAAU,MAAM,aAAa,CAAC;AAAA,EAC9B,SAAS,IAAI,UAAU,OAAO,EAAC,YAAY,MAAM,OAAO,GAAG,QAAQ,MAAM,YAAY,KAAI,CAAC;AAAA,EAC1F,QAAQ,MAAM,KAAK,EAAE;AAAA,EACrB,MAAM,MAAM,KAAK,EAAE;AAAA,EACnB,OAAO,MAAM,KAAK,EAAE;AAAA,EACpB,UAAU,IAAI,UAAU,MAAM,EAAC,YAAY,KAAI,CAAC;AAAA,EAChD,UAAU,MAAM,MAAM,CAAC;AAAA;AAAA,EAGvB,QAAQ,GAAG,OAAO;AAAA,EAClB,OAAO,GAAG,QAAQ,UAAU;AAAA,EAC5B,QAAQ,GAAG,OAAO;AAAA,EAClB,WAAW,GAAG,UAAU;AAAA,EACxB,WAAW,GAAG,UAAU;AAAA,EACxB,UAAU,GAAG,WAAW,UAAU;AAAA,EAClC,KAAK,GAAG,MAAM,EAAC,QAAQ,MAAM,YAAY,KAAI,CAAC;AAAA,EAC9C,OAAO,GAAG,QAAQ,UAAU;AAAA,EAC5B,UAAU,GAAG,SAAS;AAAA,EACtB,MAAM,GAAG,OAAO,EAAC,QAAQ,KAAI,CAAC;AAAA,EAC9B,WAAW,GAAG,YAAY,UAAU;AAAA,EACpC,KAAK,GAAG,IAAI;AAAA,EACZ,SAAS,GAAG,UAAU,UAAU;AAAA,EAChC,SAAS,GAAG,QAAQ;AAAA,EACpB,QAAQ,GAAG,SAAS,UAAU;AAAA,EAC9B,MAAM,GAAG,KAAK;AAAA,EACd,MAAM,GAAG,KAAK;AAAA,EACd,QAAQ,GAAG,OAAO;AAAA,EAClB,QAAQ,GAAG,SAAS,EAAC,QAAQ,KAAI,CAAC;AAAA,EAClC,OAAO,GAAG,MAAM;AAAA,EAChB,MAAM,GAAG,OAAO,EAAC,YAAY,MAAM,YAAY,KAAI,CAAC;AAAA,EACpD,OAAO,GAAG,QAAQ,UAAU;AAAA,EAC5B,QAAQ,GAAG,SAAS,UAAU;AAAA,EAC9B,QAAQ,GAAG,SAAS,UAAU;AAAA,EAC9B,UAAU,GAAG,WAAW,UAAU;AAAA,EAClC,SAAS,GAAG,QAAQ;AAAA,EACpB,SAAS,GAAG,UAAU,UAAU;AAAA,EAChC,OAAO,GAAG,QAAQ,UAAU;AAAA,EAC5B,OAAO,GAAG,QAAQ,UAAU;AAAA,EAC5B,QAAQ,GAAG,SAAS,UAAU;AAAA,EAC9B,KAAK,GAAG,MAAM,EAAC,YAAY,MAAM,OAAO,EAAC,CAAC;AAAA,EAC1C,aAAa,GAAG,cAAc,EAAC,YAAY,MAAM,OAAO,EAAC,CAAC;AAAA,EAC1D,SAAS,GAAG,UAAU,EAAC,YAAY,MAAM,QAAQ,MAAM,YAAY,KAAI,CAAC;AAAA,EACxE,OAAO,GAAG,QAAQ,EAAC,YAAY,MAAM,QAAQ,MAAM,YAAY,KAAI,CAAC;AAAA,EACpE,SAAS,GAAG,UAAU,EAAC,YAAY,MAAM,QAAQ,MAAM,YAAY,KAAI,CAAC;AAC1E;AAKA,IAAI,YAAY;AAChB,IAAI,aAAa,IAAI,OAAO,UAAU,QAAQ,GAAG;AAEjD,SAAS,UAAU,MAAM;AACvB,SAAO,SAAS,MAAM,SAAS,MAAM,SAAS,QAAU,SAAS;AACnE;AAEA,SAAS,cAAc,MAAM,MAAM,KAAK;AACtC,MAAK,QAAQ,OAAS,OAAM,KAAK;AAEjC,WAAS,IAAI,MAAM,IAAI,KAAK,KAAK;AAC/B,QAAI,OAAO,KAAK,WAAW,CAAC;AAC5B,QAAI,UAAU,IAAI,GAChB;AAAE,aAAO,IAAI,MAAM,KAAK,SAAS,MAAM,KAAK,WAAW,IAAI,CAAC,MAAM,KAAK,IAAI,IAAI,IAAI;AAAA,IAAE;AAAA,EACzF;AACA,SAAO;AACT;AAEA,IAAI,qBAAqB;AAEzB,IAAI,iBAAiB;AAErB,IAAI,MAAM,OAAO;AACjB,IAAI,iBAAiB,IAAI;AACzB,IAAI,WAAW,IAAI;AAEnB,IAAI,SAAS,OAAO,UAAW,SAAU,KAAK,UAAU;AAAE,SACxD,eAAe,KAAK,KAAK,QAAQ;AAChC;AAEH,IAAI,UAAU,MAAM,WAAY,SAAU,KAAK;AAAE,SAC/C,SAAS,KAAK,GAAG,MAAM;AACtB;AAEH,IAAI,cAAc,uBAAO,OAAO,IAAI;AAEpC,SAAS,YAAY,OAAO;AAC1B,SAAO,YAAY,KAAK,MAAM,YAAY,KAAK,IAAI,IAAI,OAAO,SAAS,MAAM,QAAQ,MAAM,GAAG,IAAI,IAAI;AACxG;AAEA,SAAS,kBAAkB,MAAM;AAE/B,MAAI,QAAQ,OAAQ;AAAE,WAAO,OAAO,aAAa,IAAI;AAAA,EAAE;AACvD,UAAQ;AACR,SAAO,OAAO,cAAc,QAAQ,MAAM,QAAS,OAAO,QAAQ,KAAM;AAC1E;AAEA,IAAI,gBAAgB;AAKpB,IAAI,WAAW,SAASC,UAAS,MAAM,KAAK;AAC1C,OAAK,OAAO;AACZ,OAAK,SAAS;AAChB;AAEA,SAAS,UAAU,SAAS,SAAS,OAAQ,GAAG;AAC9C,SAAO,IAAI,SAAS,KAAK,MAAM,KAAK,SAAS,CAAC;AAChD;AAEA,IAAI,iBAAiB,SAASC,gBAAe,GAAG,OAAO,KAAK;AAC1D,OAAK,QAAQ;AACb,OAAK,MAAM;AACX,MAAI,EAAE,eAAe,MAAM;AAAE,SAAK,SAAS,EAAE;AAAA,EAAY;AAC3D;AAQA,SAAS,YAAY,OAAOC,SAAQ;AAClC,WAAS,OAAO,GAAG,MAAM,OAAK;AAC5B,QAAI,YAAY,cAAc,OAAO,KAAKA,OAAM;AAChD,QAAI,YAAY,GAAG;AAAE,aAAO,IAAI,SAAS,MAAMA,UAAS,GAAG;AAAA,IAAE;AAC7D,MAAE;AACF,UAAM;AAAA,EACR;AACF;AAKA,IAAI,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOnB,aAAa;AAAA;AAAA;AAAA;AAAA,EAIb,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMZ,qBAAqB;AAAA;AAAA;AAAA,EAGrB,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,eAAe;AAAA;AAAA;AAAA,EAGf,4BAA4B;AAAA;AAAA;AAAA;AAAA,EAI5B,6BAA6B;AAAA;AAAA;AAAA;AAAA,EAI7B,2BAA2B;AAAA;AAAA;AAAA,EAG3B,yBAAyB;AAAA;AAAA;AAAA;AAAA,EAIzB,eAAe;AAAA;AAAA;AAAA;AAAA,EAIf,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMX,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaT,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASX,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMR,SAAS;AAAA;AAAA;AAAA,EAGT,YAAY;AAAA;AAAA;AAAA,EAGZ,kBAAkB;AAAA;AAAA;AAAA,EAGlB,gBAAgB;AAClB;AAIA,IAAI,yBAAyB;AAE7B,SAAS,WAAW,MAAM;AACxB,MAAI,UAAU,CAAC;AAEf,WAAS,OAAO,gBACd;AAAE,YAAQ,GAAG,IAAI,QAAQ,OAAO,MAAM,GAAG,IAAI,KAAK,GAAG,IAAI,eAAe,GAAG;AAAA,EAAG;AAEhF,MAAI,QAAQ,gBAAgB,UAAU;AACpC,YAAQ,cAAc;AAAA,EACxB,WAAW,QAAQ,eAAe,MAAM;AACtC,QAAI,CAAC,0BAA0B,OAAO,YAAY,YAAY,QAAQ,MAAM;AAC1E,+BAAyB;AACzB,cAAQ,KAAK,oHAAoH;AAAA,IACnI;AACA,YAAQ,cAAc;AAAA,EACxB,WAAW,QAAQ,eAAe,MAAM;AACtC,YAAQ,eAAe;AAAA,EACzB;AAEA,MAAI,QAAQ,iBAAiB,MAC3B;AAAE,YAAQ,gBAAgB,QAAQ,cAAc;AAAA,EAAG;AAErD,MAAI,CAAC,QAAQ,KAAK,iBAAiB,MACjC;AAAE,YAAQ,gBAAgB,QAAQ,eAAe;AAAA,EAAI;AAEvD,MAAI,QAAQ,QAAQ,OAAO,GAAG;AAC5B,QAAI,SAAS,QAAQ;AACrB,YAAQ,UAAU,SAAU,OAAO;AAAE,aAAO,OAAO,KAAK,KAAK;AAAA,IAAG;AAAA,EAClE;AACA,MAAI,QAAQ,QAAQ,SAAS,GAC3B;AAAE,YAAQ,YAAY,YAAY,SAAS,QAAQ,SAAS;AAAA,EAAG;AAEjE,SAAO;AACT;AAEA,SAAS,YAAY,SAAS,OAAO;AACnC,SAAO,SAAS,OAAO,MAAM,OAAO,KAAK,UAAU,QAAQ;AACzD,QAAI,UAAU;AAAA,MACZ,MAAM,QAAQ,UAAU;AAAA,MACxB,OAAO;AAAA,MACP;AAAA,MACA;AAAA,IACF;AACA,QAAI,QAAQ,WACV;AAAE,cAAQ,MAAM,IAAI,eAAe,MAAM,UAAU,MAAM;AAAA,IAAG;AAC9D,QAAI,QAAQ,QACV;AAAE,cAAQ,QAAQ,CAAC,OAAO,GAAG;AAAA,IAAG;AAClC,UAAM,KAAK,OAAO;AAAA,EACpB;AACF;AAGA,IACI,YAAY;AADhB,IAEI,iBAAiB;AAFrB,IAGI,cAAc;AAHlB,IAII,kBAAkB;AAJtB,IAKI,cAAc;AALlB,IAMI,qBAAqB;AANzB,IAOI,cAAc;AAPlB,IAQI,qBAAqB;AARzB,IASI,2BAA2B;AAT/B,IAUI,yBAAyB;AAV7B,IAWI,YAAY,YAAY,iBAAiB;AAE7C,SAAS,cAAc,OAAO,WAAW;AACvC,SAAO,kBAAkB,QAAQ,cAAc,MAAM,YAAY,kBAAkB;AACrF;AAGA,IACI,YAAY;AADhB,IAEI,WAAW;AAFf,IAGI,eAAe;AAHnB,IAII,gBAAgB;AAJpB,IAKI,oBAAoB;AALxB,IAMI,eAAe;AAEnB,IAAI,SAAS,SAASC,QAAO,SAAS,OAAO,UAAU;AACrD,OAAK,UAAU,UAAU,WAAW,OAAO;AAC3C,OAAK,aAAa,QAAQ;AAC1B,OAAK,WAAW,YAAY,WAAW,QAAQ,eAAe,IAAI,IAAI,QAAQ,eAAe,WAAW,YAAY,CAAC,CAAC;AACtH,MAAI,WAAW;AACf,MAAI,QAAQ,kBAAkB,MAAM;AAClC,eAAW,cAAc,QAAQ,eAAe,IAAI,IAAI,QAAQ,gBAAgB,IAAI,IAAI,CAAC;AACzF,QAAI,QAAQ,eAAe,UAAU;AAAE,kBAAY;AAAA,IAAU;AAAA,EAC/D;AACA,OAAK,gBAAgB,YAAY,QAAQ;AACzC,MAAI,kBAAkB,WAAW,WAAW,MAAM,MAAM,cAAc;AACtE,OAAK,sBAAsB,YAAY,cAAc;AACrD,OAAK,0BAA0B,YAAY,iBAAiB,MAAM,cAAc,UAAU;AAC1F,OAAK,QAAQ,OAAO,KAAK;AAKzB,OAAK,cAAc;AAKnB,MAAI,UAAU;AACZ,SAAK,MAAM;AACX,SAAK,YAAY,KAAK,MAAM,YAAY,MAAM,WAAW,CAAC,IAAI;AAC9D,SAAK,UAAU,KAAK,MAAM,MAAM,GAAG,KAAK,SAAS,EAAE,MAAM,SAAS,EAAE;AAAA,EACtE,OAAO;AACL,SAAK,MAAM,KAAK,YAAY;AAC5B,SAAK,UAAU;AAAA,EACjB;AAIA,OAAK,OAAO,QAAQ;AAEpB,OAAK,QAAQ;AAEb,OAAK,QAAQ,KAAK,MAAM,KAAK;AAG7B,OAAK,WAAW,KAAK,SAAS,KAAK,YAAY;AAG/C,OAAK,gBAAgB,KAAK,kBAAkB;AAC5C,OAAK,eAAe,KAAK,aAAa,KAAK;AAK3C,OAAK,UAAU,KAAK,eAAe;AACnC,OAAK,cAAc;AAGnB,OAAK,WAAW,QAAQ,eAAe;AACvC,OAAK,SAAS,KAAK,YAAY,KAAK,gBAAgB,KAAK,GAAG;AAG5D,OAAK,mBAAmB;AACxB,OAAK,2BAA2B;AAGhC,OAAK,WAAW,KAAK,WAAW,KAAK,gBAAgB;AAErD,OAAK,SAAS,CAAC;AAEf,OAAK,mBAAmB,uBAAO,OAAO,IAAI;AAG1C,MAAI,KAAK,QAAQ,KAAK,QAAQ,iBAAiB,KAAK,MAAM,MAAM,GAAG,CAAC,MAAM,MACxE;AAAE,SAAK,gBAAgB,CAAC;AAAA,EAAG;AAG7B,OAAK,aAAa,CAAC;AACnB,OAAK,WAAW,SAAS;AAGzB,OAAK,cAAc;AAKnB,OAAK,mBAAmB,CAAC;AAC3B;AAEA,IAAI,qBAAqB,EAAE,YAAY,EAAE,cAAc,KAAK,GAAE,aAAa,EAAE,cAAc,KAAK,GAAE,SAAS,EAAE,cAAc,KAAK,GAAE,UAAU,EAAE,cAAc,KAAK,GAAE,YAAY,EAAE,cAAc,KAAK,GAAE,kBAAkB,EAAE,cAAc,KAAK,GAAE,qBAAqB,EAAE,cAAc,KAAK,GAAE,mBAAmB,EAAE,cAAc,KAAK,GAAE,oBAAoB,EAAE,cAAc,KAAK,EAAE;AAEhX,OAAO,UAAU,QAAQ,SAAS,QAAS;AACzC,MAAI,OAAO,KAAK,QAAQ,WAAW,KAAK,UAAU;AAClD,OAAK,UAAU;AACf,SAAO,KAAK,cAAc,IAAI;AAChC;AAEA,mBAAmB,WAAW,MAAM,WAAY;AAAE,UAAQ,KAAK,gBAAgB,EAAE,QAAQ,kBAAkB;AAAE;AAE7G,mBAAmB,YAAY,MAAM,WAAY;AAAE,UAAQ,KAAK,gBAAgB,EAAE,QAAQ,mBAAmB;AAAE;AAE/G,mBAAmB,QAAQ,MAAM,WAAY;AAAE,UAAQ,KAAK,gBAAgB,EAAE,QAAQ,eAAe;AAAE;AAEvG,mBAAmB,SAAS,MAAM,WAAY;AAC5C,WAAS,IAAI,KAAK,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AACpD,QAAIC,OAAM,KAAK,WAAW,CAAC;AACzB,QAAI,QAAQA,KAAI;AAClB,QAAI,SAAS,2BAA2B,yBAAyB;AAAE,aAAO;AAAA,IAAM;AAChF,QAAI,QAAQ,gBAAgB;AAAE,cAAQ,QAAQ,eAAe;AAAA,IAAE;AAAA,EACjE;AACA,SAAQ,KAAK,YAAY,KAAK,QAAQ,eAAe,MAAO,KAAK,QAAQ;AAC3E;AAEA,mBAAmB,WAAW,MAAM,WAAY;AAC9C,MAAIA,OAAM,KAAK,iBAAiB;AAC9B,MAAI,QAAQA,KAAI;AAClB,UAAQ,QAAQ,eAAe,KAAK,KAAK,QAAQ;AACnD;AAEA,mBAAmB,iBAAiB,MAAM,WAAY;AAAE,UAAQ,KAAK,iBAAiB,EAAE,QAAQ,sBAAsB;AAAE;AAExH,mBAAmB,oBAAoB,MAAM,WAAY;AAAE,SAAO,KAAK,2BAA2B,KAAK,aAAa,CAAC;AAAE;AAEvH,mBAAmB,kBAAkB,MAAM,WAAY;AACrD,WAAS,IAAI,KAAK,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AACpD,QAAIA,OAAM,KAAK,WAAW,CAAC;AACzB,QAAI,QAAQA,KAAI;AAClB,QAAI,SAAS,2BAA2B,2BAClC,QAAQ,kBAAmB,EAAE,QAAQ,cAAe;AAAE,aAAO;AAAA,IAAK;AAAA,EAC1E;AACA,SAAO;AACT;AAEA,mBAAmB,mBAAmB,MAAM,WAAY;AACtD,UAAQ,KAAK,gBAAgB,EAAE,QAAQ,4BAA4B;AACrE;AAEA,OAAO,SAAS,SAAS,SAAU;AAC/B,MAAI,UAAU,CAAC,GAAG,MAAM,UAAU;AAClC,SAAQ,MAAQ,SAAS,GAAI,IAAI,UAAW,GAAI;AAElD,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AAAE,UAAM,QAAQ,CAAC,EAAE,GAAG;AAAA,EAAG;AAClE,SAAO;AACT;AAEA,OAAO,QAAQ,SAASC,OAAO,OAAO,SAAS;AAC7C,SAAO,IAAI,KAAK,SAAS,KAAK,EAAE,MAAM;AACxC;AAEA,OAAO,oBAAoB,SAAS,kBAAmB,OAAO,KAAK,SAAS;AAC1E,MAAI,SAAS,IAAI,KAAK,SAAS,OAAO,GAAG;AACzC,SAAO,UAAU;AACjB,SAAO,OAAO,gBAAgB;AAChC;AAEA,OAAO,YAAY,SAAS,UAAW,OAAO,SAAS;AACrD,SAAO,IAAI,KAAK,SAAS,KAAK;AAChC;AAEA,OAAO,iBAAkB,OAAO,WAAW,kBAAmB;AAE9D,IAAI,OAAO,OAAO;AAIlB,IAAI,UAAU;AACd,KAAK,kBAAkB,SAAS,OAAO;AACrC,MAAI,KAAK,QAAQ,cAAc,GAAG;AAAE,WAAO;AAAA,EAAM;AACjD,aAAS;AAEP,mBAAe,YAAY;AAC3B,aAAS,eAAe,KAAK,KAAK,KAAK,EAAE,CAAC,EAAE;AAC5C,QAAI,QAAQ,QAAQ,KAAK,KAAK,MAAM,MAAM,KAAK,CAAC;AAChD,QAAI,CAAC,OAAO;AAAE,aAAO;AAAA,IAAM;AAC3B,SAAK,MAAM,CAAC,KAAK,MAAM,CAAC,OAAO,cAAc;AAC3C,qBAAe,YAAY,QAAQ,MAAM,CAAC,EAAE;AAC5C,UAAI,aAAa,eAAe,KAAK,KAAK,KAAK,GAAG,MAAM,WAAW,QAAQ,WAAW,CAAC,EAAE;AACzF,UAAI,OAAO,KAAK,MAAM,OAAO,GAAG;AAChC,aAAO,SAAS,OAAO,SAAS,OAC7B,UAAU,KAAK,WAAW,CAAC,CAAC,KAC5B,EAAE,sBAAsB,KAAK,IAAI,KAAK,SAAS,OAAO,KAAK,MAAM,OAAO,MAAM,CAAC,MAAM;AAAA,IAC1F;AACA,aAAS,MAAM,CAAC,EAAE;AAGlB,mBAAe,YAAY;AAC3B,aAAS,eAAe,KAAK,KAAK,KAAK,EAAE,CAAC,EAAE;AAC5C,QAAI,KAAK,MAAM,KAAK,MAAM,KACxB;AAAE;AAAA,IAAS;AAAA,EACf;AACF;AAKA,KAAK,MAAM,SAAS,MAAM;AACxB,MAAI,KAAK,SAAS,MAAM;AACtB,SAAK,KAAK;AACV,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAIA,KAAK,eAAe,SAAS,MAAM;AACjC,SAAO,KAAK,SAAS,QAAQ,QAAQ,KAAK,UAAU,QAAQ,CAAC,KAAK;AACpE;AAIA,KAAK,gBAAgB,SAAS,MAAM;AAClC,MAAI,CAAC,KAAK,aAAa,IAAI,GAAG;AAAE,WAAO;AAAA,EAAM;AAC7C,OAAK,KAAK;AACV,SAAO;AACT;AAIA,KAAK,mBAAmB,SAAS,MAAM;AACrC,MAAI,CAAC,KAAK,cAAc,IAAI,GAAG;AAAE,SAAK,WAAW;AAAA,EAAG;AACtD;AAIA,KAAK,qBAAqB,WAAW;AACnC,SAAO,KAAK,SAAS,QAAQ,OAC3B,KAAK,SAAS,QAAQ,UACtB,UAAU,KAAK,KAAK,MAAM,MAAM,KAAK,YAAY,KAAK,KAAK,CAAC;AAChE;AAEA,KAAK,kBAAkB,WAAW;AAChC,MAAI,KAAK,mBAAmB,GAAG;AAC7B,QAAI,KAAK,QAAQ,qBACf;AAAE,WAAK,QAAQ,oBAAoB,KAAK,YAAY,KAAK,aAAa;AAAA,IAAG;AAC3E,WAAO;AAAA,EACT;AACF;AAKA,KAAK,YAAY,WAAW;AAC1B,MAAI,CAAC,KAAK,IAAI,QAAQ,IAAI,KAAK,CAAC,KAAK,gBAAgB,GAAG;AAAE,SAAK,WAAW;AAAA,EAAG;AAC/E;AAEA,KAAK,qBAAqB,SAAS,SAAS,SAAS;AACnD,MAAI,KAAK,SAAS,SAAS;AACzB,QAAI,KAAK,QAAQ,iBACf;AAAE,WAAK,QAAQ,gBAAgB,KAAK,cAAc,KAAK,eAAe;AAAA,IAAG;AAC3E,QAAI,CAAC,SACH;AAAE,WAAK,KAAK;AAAA,IAAG;AACjB,WAAO;AAAA,EACT;AACF;AAKA,KAAK,SAAS,SAAS,MAAM;AAC3B,OAAK,IAAI,IAAI,KAAK,KAAK,WAAW;AACpC;AAIA,KAAK,aAAa,SAAS,KAAK;AAC9B,OAAK,MAAM,OAAO,OAAO,MAAM,KAAK,OAAO,kBAAkB;AAC/D;AAEA,IAAI,sBAAsB,SAASC,uBAAsB;AACvD,OAAK,kBACL,KAAK,gBACL,KAAK,sBACL,KAAK,oBACL,KAAK,cACH;AACJ;AAEA,KAAK,qBAAqB,SAAS,wBAAwB,UAAU;AACnE,MAAI,CAAC,wBAAwB;AAAE;AAAA,EAAO;AACtC,MAAI,uBAAuB,gBAAgB,IACzC;AAAE,SAAK,iBAAiB,uBAAuB,eAAe,+CAA+C;AAAA,EAAG;AAClH,MAAI,SAAS,WAAW,uBAAuB,sBAAsB,uBAAuB;AAC5F,MAAI,SAAS,IAAI;AAAE,SAAK,iBAAiB,QAAQ,WAAW,wBAAwB,uBAAuB;AAAA,EAAG;AAChH;AAEA,KAAK,wBAAwB,SAAS,wBAAwB,UAAU;AACtE,MAAI,CAAC,wBAAwB;AAAE,WAAO;AAAA,EAAM;AAC5C,MAAI,kBAAkB,uBAAuB;AAC7C,MAAI,cAAc,uBAAuB;AACzC,MAAI,CAAC,UAAU;AAAE,WAAO,mBAAmB,KAAK,eAAe;AAAA,EAAE;AACjE,MAAI,mBAAmB,GACrB;AAAE,SAAK,MAAM,iBAAiB,yEAAyE;AAAA,EAAG;AAC5G,MAAI,eAAe,GACjB;AAAE,SAAK,iBAAiB,aAAa,oCAAoC;AAAA,EAAG;AAChF;AAEA,KAAK,iCAAiC,WAAW;AAC/C,MAAI,KAAK,aAAa,CAAC,KAAK,YAAY,KAAK,WAAW,KAAK,WAC3D;AAAE,SAAK,MAAM,KAAK,UAAU,4CAA4C;AAAA,EAAG;AAC7E,MAAI,KAAK,UACP;AAAE,SAAK,MAAM,KAAK,UAAU,4CAA4C;AAAA,EAAG;AAC/E;AAEA,KAAK,uBAAuB,SAAS,MAAM;AACzC,MAAI,KAAK,SAAS,2BAChB;AAAE,WAAO,KAAK,qBAAqB,KAAK,UAAU;AAAA,EAAE;AACtD,SAAO,KAAK,SAAS,gBAAgB,KAAK,SAAS;AACrD;AAEA,IAAI,OAAO,OAAO;AASlB,KAAK,gBAAgB,SAAS,MAAM;AAClC,MAAI,UAAU,uBAAO,OAAO,IAAI;AAChC,MAAI,CAAC,KAAK,MAAM;AAAE,SAAK,OAAO,CAAC;AAAA,EAAG;AAClC,SAAO,KAAK,SAAS,QAAQ,KAAK;AAChC,QAAI,OAAO,KAAK,eAAe,MAAM,MAAM,OAAO;AAClD,SAAK,KAAK,KAAK,IAAI;AAAA,EACrB;AACA,MAAI,KAAK,UACP;AAAE,aAAS,IAAI,GAAG,OAAO,OAAO,KAAK,KAAK,gBAAgB,GAAG,IAAI,KAAK,QAAQ,KAAK,GACjF;AACE,UAAI,OAAO,KAAK,CAAC;AAEjB,WAAK,iBAAiB,KAAK,iBAAiB,IAAI,EAAE,OAAQ,aAAa,OAAO,kBAAmB;AAAA,IACnG;AAAA,EAAE;AACN,OAAK,uBAAuB,KAAK,IAAI;AACrC,OAAK,KAAK;AACV,OAAK,aAAa,KAAK,QAAQ;AAC/B,SAAO,KAAK,WAAW,MAAM,SAAS;AACxC;AAEA,IAAI,YAAY,EAAC,MAAM,OAAM;AAA7B,IAAgC,cAAc,EAAC,MAAM,SAAQ;AAE7D,KAAK,QAAQ,SAAS,SAAS;AAC7B,MAAI,KAAK,QAAQ,cAAc,KAAK,CAAC,KAAK,aAAa,KAAK,GAAG;AAAE,WAAO;AAAA,EAAM;AAC9E,iBAAe,YAAY,KAAK;AAChC,MAAI,OAAO,eAAe,KAAK,KAAK,KAAK;AACzC,MAAI,OAAO,KAAK,MAAM,KAAK,CAAC,EAAE,QAAQ,SAAS,KAAK,MAAM,WAAW,IAAI;AAKzE,MAAI,WAAW,MAAM,WAAW,IAAI;AAAE,WAAO;AAAA,EAAK;AAClD,MAAI,SAAS;AAAE,WAAO;AAAA,EAAM;AAE5B,MAAI,WAAW,OAAO,SAAS,SAAU,SAAS,OAAQ;AAAE,WAAO;AAAA,EAAK;AACxE,MAAI,kBAAkB,QAAQ,IAAI,GAAG;AACnC,QAAI,MAAM,OAAO;AACjB,WAAO,iBAAiB,SAAS,KAAK,MAAM,WAAW,GAAG,GAAG,IAAI,GAAG;AAAE,QAAE;AAAA,IAAK;AAC7E,QAAI,WAAW,MAAM,SAAS,SAAU,SAAS,OAAQ;AAAE,aAAO;AAAA,IAAK;AACvE,QAAI,QAAQ,KAAK,MAAM,MAAM,MAAM,GAAG;AACtC,QAAI,CAAC,0BAA0B,KAAK,KAAK,GAAG;AAAE,aAAO;AAAA,IAAK;AAAA,EAC5D;AACA,SAAO;AACT;AAKA,KAAK,kBAAkB,WAAW;AAChC,MAAI,KAAK,QAAQ,cAAc,KAAK,CAAC,KAAK,aAAa,OAAO,GAC5D;AAAE,WAAO;AAAA,EAAM;AAEjB,iBAAe,YAAY,KAAK;AAChC,MAAI,OAAO,eAAe,KAAK,KAAK,KAAK;AACzC,MAAI,OAAO,KAAK,MAAM,KAAK,CAAC,EAAE,QAAQ;AACtC,SAAO,CAAC,UAAU,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,IAAI,CAAC,KACrD,KAAK,MAAM,MAAM,MAAM,OAAO,CAAC,MAAM,eACpC,OAAO,MAAM,KAAK,MAAM,UACxB,EAAE,iBAAiB,QAAQ,KAAK,MAAM,WAAW,OAAO,CAAC,CAAC,KAAK,QAAQ,SAAU,QAAQ;AAC9F;AAEA,KAAK,iBAAiB,SAAS,cAAc,OAAO;AAClD,MAAI,KAAK,QAAQ,cAAc,MAAM,CAAC,KAAK,aAAa,eAAe,UAAU,OAAO,GACtF;AAAE,WAAO;AAAA,EAAM;AAEjB,iBAAe,YAAY,KAAK;AAChC,MAAI,OAAO,eAAe,KAAK,KAAK,KAAK;AACzC,MAAI,OAAO,KAAK,MAAM,KAAK,CAAC,EAAE;AAE9B,MAAI,UAAU,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,IAAI,CAAC,GAAG;AAAE,WAAO;AAAA,EAAM;AAErE,MAAI,cAAc;AAChB,QAAI,cAAc,OAAO,GAAe;AACxC,QAAI,KAAK,MAAM,MAAM,MAAM,WAAW,MAAM,WAC1C,gBAAgB,KAAK,MAAM,UAC3B,iBAAiB,QAAQ,KAAK,MAAM,WAAW,WAAW,CAAC,KAC1D,QAAQ,SAAU,QAAQ,OAC3B;AAAE,aAAO;AAAA,IAAM;AAEjB,mBAAe,YAAY;AAC3B,QAAI,iBAAiB,eAAe,KAAK,KAAK,KAAK;AACnD,QAAI,kBAAkB,UAAU,KAAK,KAAK,MAAM,MAAM,aAAa,cAAc,eAAe,CAAC,EAAE,MAAM,CAAC,GAAG;AAAE,aAAO;AAAA,IAAM;AAAA,EAC9H;AAEA,MAAI,OAAO;AACT,QAAI,WAAW,OAAO,GAAY;AAClC,QAAI,KAAK,MAAM,MAAM,MAAM,QAAQ,MAAM,MAAM;AAC7C,UAAI,aAAa,KAAK,MAAM,UACzB,CAAC,iBAAiB,UAAU,KAAK,MAAM,WAAW,QAAQ,CAAC,KAAK,EAAE,UAAU,SAAU,UAAU,QAAU;AAAE,eAAO;AAAA,MAAM;AAAA,IAC9H;AAAA,EACF;AAEA,MAAI,KAAK,KAAK,MAAM,WAAW,IAAI;AACnC,SAAO,kBAAkB,IAAI,IAAI,KAAK,OAAO;AAC/C;AAEA,KAAK,eAAe,SAAS,OAAO;AAClC,SAAO,KAAK,eAAe,MAAM,KAAK;AACxC;AAEA,KAAK,UAAU,SAAS,OAAO;AAC7B,SAAO,KAAK,eAAe,OAAO,KAAK;AACzC;AASA,KAAK,iBAAiB,SAAS,SAAS,UAAU,SAAS;AACzD,MAAI,YAAY,KAAK,MAAM,OAAO,KAAK,UAAU,GAAG;AAEpD,MAAI,KAAK,MAAM,OAAO,GAAG;AACvB,gBAAY,QAAQ;AACpB,WAAO;AAAA,EACT;AAMA,UAAQ,WAAW;AAAA,IACnB,KAAK,QAAQ;AAAA,IAAQ,KAAK,QAAQ;AAAW,aAAO,KAAK,4BAA4B,MAAM,UAAU,OAAO;AAAA,IAC5G,KAAK,QAAQ;AAAW,aAAO,KAAK,uBAAuB,IAAI;AAAA,IAC/D,KAAK,QAAQ;AAAK,aAAO,KAAK,iBAAiB,IAAI;AAAA,IACnD,KAAK,QAAQ;AAAM,aAAO,KAAK,kBAAkB,IAAI;AAAA,IACrD,KAAK,QAAQ;AAIX,UAAK,YAAY,KAAK,UAAU,YAAY,QAAQ,YAAY,YAAa,KAAK,QAAQ,eAAe,GAAG;AAAE,aAAK,WAAW;AAAA,MAAG;AACjI,aAAO,KAAK,uBAAuB,MAAM,OAAO,CAAC,OAAO;AAAA,IAC1D,KAAK,QAAQ;AACX,UAAI,SAAS;AAAE,aAAK,WAAW;AAAA,MAAG;AAClC,aAAO,KAAK,WAAW,MAAM,IAAI;AAAA,IACnC,KAAK,QAAQ;AAAK,aAAO,KAAK,iBAAiB,IAAI;AAAA,IACnD,KAAK,QAAQ;AAAS,aAAO,KAAK,qBAAqB,IAAI;AAAA,IAC3D,KAAK,QAAQ;AAAS,aAAO,KAAK,qBAAqB,IAAI;AAAA,IAC3D,KAAK,QAAQ;AAAQ,aAAO,KAAK,oBAAoB,IAAI;AAAA,IACzD,KAAK,QAAQ;AAAM,aAAO,KAAK,kBAAkB,IAAI;AAAA,IACrD,KAAK,QAAQ;AAAA,IAAQ,KAAK,QAAQ;AAChC,aAAO,QAAQ,KAAK;AACpB,UAAI,WAAW,SAAS,OAAO;AAAE,aAAK,WAAW;AAAA,MAAG;AACpD,aAAO,KAAK,kBAAkB,MAAM,IAAI;AAAA,IAC1C,KAAK,QAAQ;AAAQ,aAAO,KAAK,oBAAoB,IAAI;AAAA,IACzD,KAAK,QAAQ;AAAO,aAAO,KAAK,mBAAmB,IAAI;AAAA,IACvD,KAAK,QAAQ;AAAQ,aAAO,KAAK,WAAW,MAAM,IAAI;AAAA,IACtD,KAAK,QAAQ;AAAM,aAAO,KAAK,oBAAoB,IAAI;AAAA,IACvD,KAAK,QAAQ;AAAA,IACb,KAAK,QAAQ;AACX,UAAI,KAAK,QAAQ,cAAc,MAAM,cAAc,QAAQ,SAAS;AAClE,uBAAe,YAAY,KAAK;AAChC,YAAI,OAAO,eAAe,KAAK,KAAK,KAAK;AACzC,YAAI,OAAO,KAAK,MAAM,KAAK,CAAC,EAAE,QAAQ,SAAS,KAAK,MAAM,WAAW,IAAI;AACzE,YAAI,WAAW,MAAM,WAAW,IAC9B;AAAE,iBAAO,KAAK,yBAAyB,MAAM,KAAK,gBAAgB,CAAC;AAAA,QAAE;AAAA,MACzE;AAEA,UAAI,CAAC,KAAK,QAAQ,6BAA6B;AAC7C,YAAI,CAAC,UACH;AAAE,eAAK,MAAM,KAAK,OAAO,wDAAwD;AAAA,QAAG;AACtF,YAAI,CAAC,KAAK,UACR;AAAE,eAAK,MAAM,KAAK,OAAO,iEAAiE;AAAA,QAAG;AAAA,MACjG;AACA,aAAO,cAAc,QAAQ,UAAU,KAAK,YAAY,IAAI,IAAI,KAAK,YAAY,MAAM,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOhG;AACE,UAAI,KAAK,gBAAgB,GAAG;AAC1B,YAAI,SAAS;AAAE,eAAK,WAAW;AAAA,QAAG;AAClC,aAAK,KAAK;AACV,eAAO,KAAK,uBAAuB,MAAM,MAAM,CAAC,OAAO;AAAA,MACzD;AAEA,UAAI,YAAY,KAAK,aAAa,KAAK,IAAI,gBAAgB,KAAK,QAAQ,KAAK,IAAI,UAAU;AAC3F,UAAI,WAAW;AACb,YAAI,YAAY,KAAK,QAAQ,eAAe,UAAU;AACpD,eAAK,MAAM,KAAK,OAAO,+EAA+E;AAAA,QACxG;AACA,YAAI,cAAc,eAAe;AAC/B,cAAI,CAAC,KAAK,UAAU;AAClB,iBAAK,MAAM,KAAK,OAAO,qDAAqD;AAAA,UAC9E;AACA,eAAK,KAAK;AAAA,QACZ;AACA,aAAK,KAAK;AACV,aAAK,SAAS,MAAM,OAAO,SAAS;AACpC,aAAK,UAAU;AACf,eAAO,KAAK,WAAW,MAAM,qBAAqB;AAAA,MACpD;AAEA,UAAI,YAAY,KAAK,OAAO,OAAO,KAAK,gBAAgB;AACxD,UAAI,cAAc,QAAQ,QAAQ,KAAK,SAAS,gBAAgB,KAAK,IAAI,QAAQ,KAAK,GACpF;AAAE,eAAO,KAAK,sBAAsB,MAAM,WAAW,MAAM,OAAO;AAAA,MAAE,OACjE;AAAE,eAAO,KAAK,yBAAyB,MAAM,IAAI;AAAA,MAAE;AAAA,EAC1D;AACF;AAEA,KAAK,8BAA8B,SAAS,MAAM,SAAS;AACzD,MAAI,UAAU,YAAY;AAC1B,OAAK,KAAK;AACV,MAAI,KAAK,IAAI,QAAQ,IAAI,KAAK,KAAK,gBAAgB,GAAG;AAAE,SAAK,QAAQ;AAAA,EAAM,WAClE,KAAK,SAAS,QAAQ,MAAM;AAAE,SAAK,WAAW;AAAA,EAAG,OACrD;AACH,SAAK,QAAQ,KAAK,WAAW;AAC7B,SAAK,UAAU;AAAA,EACjB;AAIA,MAAI,IAAI;AACR,SAAO,IAAI,KAAK,OAAO,QAAQ,EAAE,GAAG;AAClC,QAAI,MAAM,KAAK,OAAO,CAAC;AACvB,QAAI,KAAK,SAAS,QAAQ,IAAI,SAAS,KAAK,MAAM,MAAM;AACtD,UAAI,IAAI,QAAQ,SAAS,WAAW,IAAI,SAAS,SAAS;AAAE;AAAA,MAAM;AAClE,UAAI,KAAK,SAAS,SAAS;AAAE;AAAA,MAAM;AAAA,IACrC;AAAA,EACF;AACA,MAAI,MAAM,KAAK,OAAO,QAAQ;AAAE,SAAK,MAAM,KAAK,OAAO,iBAAiB,OAAO;AAAA,EAAG;AAClF,SAAO,KAAK,WAAW,MAAM,UAAU,mBAAmB,mBAAmB;AAC/E;AAEA,KAAK,yBAAyB,SAAS,MAAM;AAC3C,OAAK,KAAK;AACV,OAAK,UAAU;AACf,SAAO,KAAK,WAAW,MAAM,mBAAmB;AAClD;AAEA,KAAK,mBAAmB,SAAS,MAAM;AACrC,OAAK,KAAK;AACV,OAAK,OAAO,KAAK,SAAS;AAC1B,OAAK,OAAO,KAAK,eAAe,IAAI;AACpC,OAAK,OAAO,IAAI;AAChB,OAAK,OAAO,QAAQ,MAAM;AAC1B,OAAK,OAAO,KAAK,qBAAqB;AACtC,MAAI,KAAK,QAAQ,eAAe,GAC9B;AAAE,SAAK,IAAI,QAAQ,IAAI;AAAA,EAAG,OAE1B;AAAE,SAAK,UAAU;AAAA,EAAG;AACtB,SAAO,KAAK,WAAW,MAAM,kBAAkB;AACjD;AAUA,KAAK,oBAAoB,SAAS,MAAM;AACtC,OAAK,KAAK;AACV,MAAI,UAAW,KAAK,QAAQ,eAAe,KAAK,KAAK,YAAY,KAAK,cAAc,OAAO,IAAK,KAAK,eAAe;AACpH,OAAK,OAAO,KAAK,SAAS;AAC1B,OAAK,WAAW,CAAC;AACjB,OAAK,OAAO,QAAQ,MAAM;AAC1B,MAAI,KAAK,SAAS,QAAQ,MAAM;AAC9B,QAAI,UAAU,IAAI;AAAE,WAAK,WAAW,OAAO;AAAA,IAAG;AAC9C,WAAO,KAAK,SAAS,MAAM,IAAI;AAAA,EACjC;AACA,MAAI,QAAQ,KAAK,MAAM;AACvB,MAAI,KAAK,SAAS,QAAQ,QAAQ,KAAK,SAAS,QAAQ,UAAU,OAAO;AACvE,QAAI,SAAS,KAAK,UAAU,GAAG,OAAO,QAAQ,QAAQ,KAAK;AAC3D,SAAK,KAAK;AACV,SAAK,SAAS,QAAQ,MAAM,IAAI;AAChC,SAAK,WAAW,QAAQ,qBAAqB;AAC7C,WAAO,KAAK,kBAAkB,MAAM,QAAQ,OAAO;AAAA,EACrD;AACA,MAAI,gBAAgB,KAAK,aAAa,KAAK,GAAG,UAAU;AAExD,MAAI,YAAY,KAAK,QAAQ,IAAI,IAAI,UAAU,KAAK,aAAa,IAAI,IAAI,gBAAgB;AACzF,MAAI,WAAW;AACb,QAAI,SAAS,KAAK,UAAU;AAC5B,SAAK,KAAK;AACV,QAAI,cAAc,eAAe;AAAE,WAAK,KAAK;AAAA,IAAG;AAChD,SAAK,SAAS,QAAQ,MAAM,SAAS;AACrC,SAAK,WAAW,QAAQ,qBAAqB;AAC7C,WAAO,KAAK,kBAAkB,MAAM,QAAQ,OAAO;AAAA,EACrD;AACA,MAAI,cAAc,KAAK;AACvB,MAAI,yBAAyB,IAAI;AACjC,MAAI,UAAU,KAAK;AACnB,MAAI,OAAO,UAAU,KACjB,KAAK,oBAAoB,wBAAwB,OAAO,IACxD,KAAK,gBAAgB,MAAM,sBAAsB;AACrD,MAAI,KAAK,SAAS,QAAQ,QAAQ,UAAU,KAAK,QAAQ,eAAe,KAAK,KAAK,aAAa,IAAI,IAAI;AACrG,QAAI,UAAU,IAAI;AAChB,UAAI,KAAK,SAAS,QAAQ,KAAK;AAAE,aAAK,WAAW,OAAO;AAAA,MAAG;AAC3D,WAAK,QAAQ;AAAA,IACf,WAAW,WAAW,KAAK,QAAQ,eAAe,GAAG;AACnD,UAAI,KAAK,UAAU,WAAW,CAAC,eAAe,KAAK,SAAS,gBAAgB,KAAK,SAAS,SAAS;AAAE,aAAK,WAAW;AAAA,MAAG,WAC/G,KAAK,QAAQ,eAAe,GAAG;AAAE,aAAK,QAAQ;AAAA,MAAO;AAAA,IAChE;AACA,QAAI,iBAAiB,SAAS;AAAE,WAAK,MAAM,KAAK,OAAO,+DAA+D;AAAA,IAAG;AACzH,SAAK,aAAa,MAAM,OAAO,sBAAsB;AACrD,SAAK,iBAAiB,IAAI;AAC1B,WAAO,KAAK,WAAW,MAAM,IAAI;AAAA,EACnC,OAAO;AACL,SAAK,sBAAsB,wBAAwB,IAAI;AAAA,EACzD;AACA,MAAI,UAAU,IAAI;AAAE,SAAK,WAAW,OAAO;AAAA,EAAG;AAC9C,SAAO,KAAK,SAAS,MAAM,IAAI;AACjC;AAGA,KAAK,oBAAoB,SAAS,MAAM,MAAM,SAAS;AACrD,OAAK,KAAK,SAAS,QAAQ,OAAQ,KAAK,QAAQ,eAAe,KAAK,KAAK,aAAa,IAAI,MAAO,KAAK,aAAa,WAAW,GAAG;AAC/H,QAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,UAAI,KAAK,SAAS,QAAQ,KAAK;AAC7B,YAAI,UAAU,IAAI;AAAE,eAAK,WAAW,OAAO;AAAA,QAAG;AAAA,MAChD,OAAO;AAAE,aAAK,QAAQ,UAAU;AAAA,MAAI;AAAA,IACtC;AACA,WAAO,KAAK,WAAW,MAAM,IAAI;AAAA,EACnC;AACA,MAAI,UAAU,IAAI;AAAE,SAAK,WAAW,OAAO;AAAA,EAAG;AAC9C,SAAO,KAAK,SAAS,MAAM,IAAI;AACjC;AAEA,KAAK,yBAAyB,SAAS,MAAM,SAAS,qBAAqB;AACzE,OAAK,KAAK;AACV,SAAO,KAAK,cAAc,MAAM,kBAAkB,sBAAsB,IAAI,yBAAyB,OAAO,OAAO;AACrH;AAEA,KAAK,mBAAmB,SAAS,MAAM;AACrC,OAAK,KAAK;AACV,OAAK,OAAO,KAAK,qBAAqB;AAEtC,OAAK,aAAa,KAAK,eAAe,IAAI;AAC1C,OAAK,YAAY,KAAK,IAAI,QAAQ,KAAK,IAAI,KAAK,eAAe,IAAI,IAAI;AACvE,SAAO,KAAK,WAAW,MAAM,aAAa;AAC5C;AAEA,KAAK,uBAAuB,SAAS,MAAM;AACzC,MAAI,CAAC,KAAK,cAAc,CAAC,KAAK,QAAQ,4BACpC;AAAE,SAAK,MAAM,KAAK,OAAO,8BAA8B;AAAA,EAAG;AAC5D,OAAK,KAAK;AAMV,MAAI,KAAK,IAAI,QAAQ,IAAI,KAAK,KAAK,gBAAgB,GAAG;AAAE,SAAK,WAAW;AAAA,EAAM,OACzE;AAAE,SAAK,WAAW,KAAK,gBAAgB;AAAG,SAAK,UAAU;AAAA,EAAG;AACjE,SAAO,KAAK,WAAW,MAAM,iBAAiB;AAChD;AAEA,KAAK,uBAAuB,SAAS,MAAM;AACzC,OAAK,KAAK;AACV,OAAK,eAAe,KAAK,qBAAqB;AAC9C,OAAK,QAAQ,CAAC;AACd,OAAK,OAAO,QAAQ,MAAM;AAC1B,OAAK,OAAO,KAAK,WAAW;AAC5B,OAAK,WAAW,CAAC;AAMjB,MAAI;AACJ,WAAS,aAAa,OAAO,KAAK,SAAS,QAAQ,UAAS;AAC1D,QAAI,KAAK,SAAS,QAAQ,SAAS,KAAK,SAAS,QAAQ,UAAU;AACjE,UAAI,SAAS,KAAK,SAAS,QAAQ;AACnC,UAAI,KAAK;AAAE,aAAK,WAAW,KAAK,YAAY;AAAA,MAAG;AAC/C,WAAK,MAAM,KAAK,MAAM,KAAK,UAAU,CAAC;AACtC,UAAI,aAAa,CAAC;AAClB,WAAK,KAAK;AACV,UAAI,QAAQ;AACV,YAAI,OAAO,KAAK,gBAAgB;AAAA,MAClC,OAAO;AACL,YAAI,YAAY;AAAE,eAAK,iBAAiB,KAAK,cAAc,0BAA0B;AAAA,QAAG;AACxF,qBAAa;AACb,YAAI,OAAO;AAAA,MACb;AACA,WAAK,OAAO,QAAQ,KAAK;AAAA,IAC3B,OAAO;AACL,UAAI,CAAC,KAAK;AAAE,aAAK,WAAW;AAAA,MAAG;AAC/B,UAAI,WAAW,KAAK,KAAK,eAAe,IAAI,CAAC;AAAA,IAC/C;AAAA,EACF;AACA,OAAK,UAAU;AACf,MAAI,KAAK;AAAE,SAAK,WAAW,KAAK,YAAY;AAAA,EAAG;AAC/C,OAAK,KAAK;AACV,OAAK,OAAO,IAAI;AAChB,SAAO,KAAK,WAAW,MAAM,iBAAiB;AAChD;AAEA,KAAK,sBAAsB,SAAS,MAAM;AACxC,OAAK,KAAK;AACV,MAAI,UAAU,KAAK,KAAK,MAAM,MAAM,KAAK,YAAY,KAAK,KAAK,CAAC,GAC9D;AAAE,SAAK,MAAM,KAAK,YAAY,6BAA6B;AAAA,EAAG;AAChE,OAAK,WAAW,KAAK,gBAAgB;AACrC,OAAK,UAAU;AACf,SAAO,KAAK,WAAW,MAAM,gBAAgB;AAC/C;AAIA,IAAI,UAAU,CAAC;AAEf,KAAK,wBAAwB,WAAW;AACtC,MAAI,QAAQ,KAAK,iBAAiB;AAClC,MAAI,SAAS,MAAM,SAAS;AAC5B,OAAK,WAAW,SAAS,qBAAqB,CAAC;AAC/C,OAAK,iBAAiB,OAAO,SAAS,oBAAoB,YAAY;AACtE,OAAK,OAAO,QAAQ,MAAM;AAE1B,SAAO;AACT;AAEA,KAAK,oBAAoB,SAAS,MAAM;AACtC,OAAK,KAAK;AACV,OAAK,QAAQ,KAAK,WAAW;AAC7B,OAAK,UAAU;AACf,MAAI,KAAK,SAAS,QAAQ,QAAQ;AAChC,QAAI,SAAS,KAAK,UAAU;AAC5B,SAAK,KAAK;AACV,QAAI,KAAK,IAAI,QAAQ,MAAM,GAAG;AAC5B,aAAO,QAAQ,KAAK,sBAAsB;AAAA,IAC5C,OAAO;AACL,UAAI,KAAK,QAAQ,cAAc,IAAI;AAAE,aAAK,WAAW;AAAA,MAAG;AACxD,aAAO,QAAQ;AACf,WAAK,WAAW,CAAC;AAAA,IACnB;AACA,WAAO,OAAO,KAAK,WAAW,KAAK;AACnC,SAAK,UAAU;AACf,SAAK,UAAU,KAAK,WAAW,QAAQ,aAAa;AAAA,EACtD;AACA,OAAK,YAAY,KAAK,IAAI,QAAQ,QAAQ,IAAI,KAAK,WAAW,IAAI;AAClE,MAAI,CAAC,KAAK,WAAW,CAAC,KAAK,WACzB;AAAE,SAAK,MAAM,KAAK,OAAO,iCAAiC;AAAA,EAAG;AAC/D,SAAO,KAAK,WAAW,MAAM,cAAc;AAC7C;AAEA,KAAK,oBAAoB,SAAS,MAAM,MAAM,yBAAyB;AACrE,OAAK,KAAK;AACV,OAAK,SAAS,MAAM,OAAO,MAAM,uBAAuB;AACxD,OAAK,UAAU;AACf,SAAO,KAAK,WAAW,MAAM,qBAAqB;AACpD;AAEA,KAAK,sBAAsB,SAAS,MAAM;AACxC,OAAK,KAAK;AACV,OAAK,OAAO,KAAK,qBAAqB;AACtC,OAAK,OAAO,KAAK,SAAS;AAC1B,OAAK,OAAO,KAAK,eAAe,OAAO;AACvC,OAAK,OAAO,IAAI;AAChB,SAAO,KAAK,WAAW,MAAM,gBAAgB;AAC/C;AAEA,KAAK,qBAAqB,SAAS,MAAM;AACvC,MAAI,KAAK,QAAQ;AAAE,SAAK,MAAM,KAAK,OAAO,uBAAuB;AAAA,EAAG;AACpE,OAAK,KAAK;AACV,OAAK,SAAS,KAAK,qBAAqB;AACxC,OAAK,OAAO,KAAK,eAAe,MAAM;AACtC,SAAO,KAAK,WAAW,MAAM,eAAe;AAC9C;AAEA,KAAK,sBAAsB,SAAS,MAAM;AACxC,OAAK,KAAK;AACV,SAAO,KAAK,WAAW,MAAM,gBAAgB;AAC/C;AAEA,KAAK,wBAAwB,SAAS,MAAM,WAAW,MAAM,SAAS;AACpE,WAAS,MAAM,GAAG,OAAO,KAAK,QAAQ,MAAM,KAAK,QAAQ,OAAO,GAC9D;AACA,QAAI,QAAQ,KAAK,GAAG;AAEpB,QAAI,MAAM,SAAS,WACjB;AAAE,WAAK,MAAM,KAAK,OAAO,YAAY,YAAY,uBAAuB;AAAA,IAC5E;AAAA,EAAE;AACF,MAAI,OAAO,KAAK,KAAK,SAAS,SAAS,KAAK,SAAS,QAAQ,UAAU,WAAW;AAClF,WAAS,IAAI,KAAK,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAChD,QAAI,UAAU,KAAK,OAAO,CAAC;AAC3B,QAAI,QAAQ,mBAAmB,KAAK,OAAO;AAEzC,cAAQ,iBAAiB,KAAK;AAC9B,cAAQ,OAAO;AAAA,IACjB,OAAO;AAAE;AAAA,IAAM;AAAA,EACjB;AACA,OAAK,OAAO,KAAK,EAAC,MAAM,WAAW,MAAY,gBAAgB,KAAK,MAAK,CAAC;AAC1E,OAAK,OAAO,KAAK,eAAe,UAAU,QAAQ,QAAQ,OAAO,MAAM,KAAK,UAAU,UAAU,UAAU,OAAO;AACjH,OAAK,OAAO,IAAI;AAChB,OAAK,QAAQ;AACb,SAAO,KAAK,WAAW,MAAM,kBAAkB;AACjD;AAEA,KAAK,2BAA2B,SAAS,MAAM,MAAM;AACnD,OAAK,aAAa;AAClB,OAAK,UAAU;AACf,SAAO,KAAK,WAAW,MAAM,qBAAqB;AACpD;AAMA,KAAK,aAAa,SAAS,uBAAuB,MAAM,YAAY;AAClE,MAAK,0BAA0B,OAAS,yBAAwB;AAChE,MAAK,SAAS,OAAS,QAAO,KAAK,UAAU;AAE7C,OAAK,OAAO,CAAC;AACb,OAAK,OAAO,QAAQ,MAAM;AAC1B,MAAI,uBAAuB;AAAE,SAAK,WAAW,CAAC;AAAA,EAAG;AACjD,SAAO,KAAK,SAAS,QAAQ,QAAQ;AACnC,QAAI,OAAO,KAAK,eAAe,IAAI;AACnC,SAAK,KAAK,KAAK,IAAI;AAAA,EACrB;AACA,MAAI,YAAY;AAAE,SAAK,SAAS;AAAA,EAAO;AACvC,OAAK,KAAK;AACV,MAAI,uBAAuB;AAAE,SAAK,UAAU;AAAA,EAAG;AAC/C,SAAO,KAAK,WAAW,MAAM,gBAAgB;AAC/C;AAMA,KAAK,WAAW,SAAS,MAAM,MAAM;AACnC,OAAK,OAAO;AACZ,OAAK,OAAO,QAAQ,IAAI;AACxB,OAAK,OAAO,KAAK,SAAS,QAAQ,OAAO,OAAO,KAAK,gBAAgB;AACrE,OAAK,OAAO,QAAQ,IAAI;AACxB,OAAK,SAAS,KAAK,SAAS,QAAQ,SAAS,OAAO,KAAK,gBAAgB;AACzE,OAAK,OAAO,QAAQ,MAAM;AAC1B,OAAK,OAAO,KAAK,eAAe,KAAK;AACrC,OAAK,UAAU;AACf,OAAK,OAAO,IAAI;AAChB,SAAO,KAAK,WAAW,MAAM,cAAc;AAC7C;AAKA,KAAK,aAAa,SAAS,MAAM,MAAM;AACrC,MAAI,UAAU,KAAK,SAAS,QAAQ;AACpC,OAAK,KAAK;AAEV,MACE,KAAK,SAAS,yBACd,KAAK,aAAa,CAAC,EAAE,QAAQ,SAE3B,CAAC,WACD,KAAK,QAAQ,cAAc,KAC3B,KAAK,UACL,KAAK,SAAS,SACd,KAAK,aAAa,CAAC,EAAE,GAAG,SAAS,eAEnC;AACA,SAAK;AAAA,MACH,KAAK;AAAA,OACH,UAAU,WAAW,YAAY;AAAA,IACrC;AAAA,EACF;AACA,OAAK,OAAO;AACZ,OAAK,QAAQ,UAAU,KAAK,gBAAgB,IAAI,KAAK,iBAAiB;AACtE,OAAK,OAAO,QAAQ,MAAM;AAC1B,OAAK,OAAO,KAAK,eAAe,KAAK;AACrC,OAAK,UAAU;AACf,OAAK,OAAO,IAAI;AAChB,SAAO,KAAK,WAAW,MAAM,UAAU,mBAAmB,gBAAgB;AAC5E;AAIA,KAAK,WAAW,SAAS,MAAM,OAAO,MAAM,yBAAyB;AACnE,OAAK,eAAe,CAAC;AACrB,OAAK,OAAO;AACZ,aAAS;AACP,QAAI,OAAO,KAAK,UAAU;AAC1B,SAAK,WAAW,MAAM,IAAI;AAC1B,QAAI,KAAK,IAAI,QAAQ,EAAE,GAAG;AACxB,WAAK,OAAO,KAAK,iBAAiB,KAAK;AAAA,IACzC,WAAW,CAAC,2BAA2B,SAAS,WAAW,EAAE,KAAK,SAAS,QAAQ,OAAQ,KAAK,QAAQ,eAAe,KAAK,KAAK,aAAa,IAAI,IAAK;AACrJ,WAAK,WAAW;AAAA,IAClB,WAAW,CAAC,4BAA4B,SAAS,WAAW,SAAS,kBAAkB,KAAK,QAAQ,eAAe,MAAM,KAAK,SAAS,QAAQ,OAAO,CAAC,KAAK,aAAa,IAAI,GAAG;AAC9K,WAAK,MAAM,KAAK,YAAa,4BAA4B,OAAO,cAAe;AAAA,IACjF,WAAW,CAAC,2BAA2B,KAAK,GAAG,SAAS,gBAAgB,EAAE,UAAU,KAAK,SAAS,QAAQ,OAAO,KAAK,aAAa,IAAI,KAAK;AAC1I,WAAK,MAAM,KAAK,YAAY,0DAA0D;AAAA,IACxF,OAAO;AACL,WAAK,OAAO;AAAA,IACd;AACA,SAAK,aAAa,KAAK,KAAK,WAAW,MAAM,oBAAoB,CAAC;AAClE,QAAI,CAAC,KAAK,IAAI,QAAQ,KAAK,GAAG;AAAE;AAAA,IAAM;AAAA,EACxC;AACA,SAAO;AACT;AAEA,KAAK,aAAa,SAAS,MAAM,MAAM;AACrC,OAAK,KAAK,SAAS,WAAW,SAAS,gBACnC,KAAK,WAAW,IAChB,KAAK,iBAAiB;AAE1B,OAAK,iBAAiB,KAAK,IAAI,SAAS,QAAQ,WAAW,cAAc,KAAK;AAChF;AAEA,IAAI,iBAAiB;AAArB,IAAwB,yBAAyB;AAAjD,IAAoD,mBAAmB;AAMvE,KAAK,gBAAgB,SAAS,MAAM,WAAW,qBAAqB,SAAS,SAAS;AACpF,OAAK,aAAa,IAAI;AACtB,MAAI,KAAK,QAAQ,eAAe,KAAK,KAAK,QAAQ,eAAe,KAAK,CAAC,SAAS;AAC9E,QAAI,KAAK,SAAS,QAAQ,QAAS,YAAY,wBAC7C;AAAE,WAAK,WAAW;AAAA,IAAG;AACvB,SAAK,YAAY,KAAK,IAAI,QAAQ,IAAI;AAAA,EACxC;AACA,MAAI,KAAK,QAAQ,eAAe,GAC9B;AAAE,SAAK,QAAQ,CAAC,CAAC;AAAA,EAAS;AAE5B,MAAI,YAAY,gBAAgB;AAC9B,SAAK,KAAM,YAAY,oBAAqB,KAAK,SAAS,QAAQ,OAAO,OAAO,KAAK,WAAW;AAChG,QAAI,KAAK,MAAM,EAAE,YAAY,yBAK3B;AAAE,WAAK,gBAAgB,KAAK,IAAK,KAAK,UAAU,KAAK,aAAa,KAAK,QAAS,KAAK,sBAAsB,WAAW,eAAe,aAAa;AAAA,IAAG;AAAA,EACzJ;AAEA,MAAI,cAAc,KAAK,UAAU,cAAc,KAAK,UAAU,mBAAmB,KAAK;AACtF,OAAK,WAAW;AAChB,OAAK,WAAW;AAChB,OAAK,gBAAgB;AACrB,OAAK,WAAW,cAAc,KAAK,OAAO,KAAK,SAAS,CAAC;AAEzD,MAAI,EAAE,YAAY,iBAChB;AAAE,SAAK,KAAK,KAAK,SAAS,QAAQ,OAAO,KAAK,WAAW,IAAI;AAAA,EAAM;AAErE,OAAK,oBAAoB,IAAI;AAC7B,OAAK,kBAAkB,MAAM,qBAAqB,OAAO,OAAO;AAEhE,OAAK,WAAW;AAChB,OAAK,WAAW;AAChB,OAAK,gBAAgB;AACrB,SAAO,KAAK,WAAW,MAAO,YAAY,iBAAkB,wBAAwB,oBAAoB;AAC1G;AAEA,KAAK,sBAAsB,SAAS,MAAM;AACxC,OAAK,OAAO,QAAQ,MAAM;AAC1B,OAAK,SAAS,KAAK,iBAAiB,QAAQ,QAAQ,OAAO,KAAK,QAAQ,eAAe,CAAC;AACxF,OAAK,+BAA+B;AACtC;AAKA,KAAK,aAAa,SAAS,MAAM,aAAa;AAC5C,OAAK,KAAK;AAIV,MAAI,YAAY,KAAK;AACrB,OAAK,SAAS;AAEd,OAAK,aAAa,MAAM,WAAW;AACnC,OAAK,gBAAgB,IAAI;AACzB,MAAI,iBAAiB,KAAK,eAAe;AACzC,MAAI,YAAY,KAAK,UAAU;AAC/B,MAAI,iBAAiB;AACrB,YAAU,OAAO,CAAC;AAClB,OAAK,OAAO,QAAQ,MAAM;AAC1B,SAAO,KAAK,SAAS,QAAQ,QAAQ;AACnC,QAAI,UAAU,KAAK,kBAAkB,KAAK,eAAe,IAAI;AAC7D,QAAI,SAAS;AACX,gBAAU,KAAK,KAAK,OAAO;AAC3B,UAAI,QAAQ,SAAS,sBAAsB,QAAQ,SAAS,eAAe;AACzE,YAAI,gBAAgB;AAAE,eAAK,iBAAiB,QAAQ,OAAO,yCAAyC;AAAA,QAAG;AACvG,yBAAiB;AAAA,MACnB,WAAW,QAAQ,OAAO,QAAQ,IAAI,SAAS,uBAAuB,wBAAwB,gBAAgB,OAAO,GAAG;AACtH,aAAK,iBAAiB,QAAQ,IAAI,OAAQ,kBAAmB,QAAQ,IAAI,OAAQ,6BAA8B;AAAA,MACjH;AAAA,IACF;AAAA,EACF;AACA,OAAK,SAAS;AACd,OAAK,KAAK;AACV,OAAK,OAAO,KAAK,WAAW,WAAW,WAAW;AAClD,OAAK,cAAc;AACnB,SAAO,KAAK,WAAW,MAAM,cAAc,qBAAqB,iBAAiB;AACnF;AAEA,KAAK,oBAAoB,SAAS,wBAAwB;AACxD,MAAI,KAAK,IAAI,QAAQ,IAAI,GAAG;AAAE,WAAO;AAAA,EAAK;AAE1C,MAAI,cAAc,KAAK,QAAQ;AAC/B,MAAI,OAAO,KAAK,UAAU;AAC1B,MAAI,UAAU;AACd,MAAI,cAAc;AAClB,MAAI,UAAU;AACd,MAAI,OAAO;AACX,MAAI,WAAW;AAEf,MAAI,KAAK,cAAc,QAAQ,GAAG;AAEhC,QAAI,eAAe,MAAM,KAAK,IAAI,QAAQ,MAAM,GAAG;AACjD,WAAK,sBAAsB,IAAI;AAC/B,aAAO;AAAA,IACT;AACA,QAAI,KAAK,wBAAwB,KAAK,KAAK,SAAS,QAAQ,MAAM;AAChE,iBAAW;AAAA,IACb,OAAO;AACL,gBAAU;AAAA,IACZ;AAAA,EACF;AACA,OAAK,SAAS;AACd,MAAI,CAAC,WAAW,eAAe,KAAK,KAAK,cAAc,OAAO,GAAG;AAC/D,SAAK,KAAK,wBAAwB,KAAK,KAAK,SAAS,QAAQ,SAAS,CAAC,KAAK,mBAAmB,GAAG;AAChG,gBAAU;AAAA,IACZ,OAAO;AACL,gBAAU;AAAA,IACZ;AAAA,EACF;AACA,MAAI,CAAC,YAAY,eAAe,KAAK,CAAC,YAAY,KAAK,IAAI,QAAQ,IAAI,GAAG;AACxE,kBAAc;AAAA,EAChB;AACA,MAAI,CAAC,WAAW,CAAC,WAAW,CAAC,aAAa;AACxC,QAAI,YAAY,KAAK;AACrB,QAAI,KAAK,cAAc,KAAK,KAAK,KAAK,cAAc,KAAK,GAAG;AAC1D,UAAI,KAAK,wBAAwB,GAAG;AAClC,eAAO;AAAA,MACT,OAAO;AACL,kBAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AAGA,MAAI,SAAS;AAGX,SAAK,WAAW;AAChB,SAAK,MAAM,KAAK,YAAY,KAAK,cAAc,KAAK,eAAe;AACnE,SAAK,IAAI,OAAO;AAChB,SAAK,WAAW,KAAK,KAAK,YAAY;AAAA,EACxC,OAAO;AACL,SAAK,sBAAsB,IAAI;AAAA,EACjC;AAGA,MAAI,cAAc,MAAM,KAAK,SAAS,QAAQ,UAAU,SAAS,YAAY,eAAe,SAAS;AACnG,QAAI,gBAAgB,CAAC,KAAK,UAAU,aAAa,MAAM,aAAa;AACpE,QAAI,oBAAoB,iBAAiB;AAEzC,QAAI,iBAAiB,SAAS,UAAU;AAAE,WAAK,MAAM,KAAK,IAAI,OAAO,yCAAyC;AAAA,IAAG;AACjH,SAAK,OAAO,gBAAgB,gBAAgB;AAC5C,SAAK,iBAAiB,MAAM,aAAa,SAAS,iBAAiB;AAAA,EACrE,OAAO;AACL,SAAK,gBAAgB,IAAI;AAAA,EAC3B;AAEA,SAAO;AACT;AAEA,KAAK,0BAA0B,WAAW;AACxC,SACE,KAAK,SAAS,QAAQ,QACtB,KAAK,SAAS,QAAQ,aACtB,KAAK,SAAS,QAAQ,OACtB,KAAK,SAAS,QAAQ,UACtB,KAAK,SAAS,QAAQ,YACtB,KAAK,KAAK;AAEd;AAEA,KAAK,wBAAwB,SAAS,SAAS;AAC7C,MAAI,KAAK,SAAS,QAAQ,WAAW;AACnC,QAAI,KAAK,UAAU,eAAe;AAChC,WAAK,MAAM,KAAK,OAAO,oDAAoD;AAAA,IAC7E;AACA,YAAQ,WAAW;AACnB,YAAQ,MAAM,KAAK,kBAAkB;AAAA,EACvC,OAAO;AACL,SAAK,kBAAkB,OAAO;AAAA,EAChC;AACF;AAEA,KAAK,mBAAmB,SAAS,QAAQ,aAAa,SAAS,mBAAmB;AAEhF,MAAI,MAAM,OAAO;AACjB,MAAI,OAAO,SAAS,eAAe;AACjC,QAAI,aAAa;AAAE,WAAK,MAAM,IAAI,OAAO,kCAAkC;AAAA,IAAG;AAC9E,QAAI,SAAS;AAAE,WAAK,MAAM,IAAI,OAAO,sCAAsC;AAAA,IAAG;AAAA,EAChF,WAAW,OAAO,UAAU,aAAa,QAAQ,WAAW,GAAG;AAC7D,SAAK,MAAM,IAAI,OAAO,wDAAwD;AAAA,EAChF;AAGA,MAAI,QAAQ,OAAO,QAAQ,KAAK,YAAY,aAAa,SAAS,iBAAiB;AAGnF,MAAI,OAAO,SAAS,SAAS,MAAM,OAAO,WAAW,GACnD;AAAE,SAAK,iBAAiB,MAAM,OAAO,8BAA8B;AAAA,EAAG;AACxE,MAAI,OAAO,SAAS,SAAS,MAAM,OAAO,WAAW,GACnD;AAAE,SAAK,iBAAiB,MAAM,OAAO,sCAAsC;AAAA,EAAG;AAChF,MAAI,OAAO,SAAS,SAAS,MAAM,OAAO,CAAC,EAAE,SAAS,eACpD;AAAE,SAAK,iBAAiB,MAAM,OAAO,CAAC,EAAE,OAAO,+BAA+B;AAAA,EAAG;AAEnF,SAAO,KAAK,WAAW,QAAQ,kBAAkB;AACnD;AAEA,KAAK,kBAAkB,SAAS,OAAO;AACrC,MAAI,aAAa,OAAO,aAAa,GAAG;AACtC,SAAK,MAAM,MAAM,IAAI,OAAO,gDAAgD;AAAA,EAC9E,WAAW,MAAM,UAAU,aAAa,OAAO,WAAW,GAAG;AAC3D,SAAK,MAAM,MAAM,IAAI,OAAO,qDAAqD;AAAA,EACnF;AAEA,MAAI,KAAK,IAAI,QAAQ,EAAE,GAAG;AAExB,SAAK,WAAW,yBAAyB,WAAW;AACpD,UAAM,QAAQ,KAAK,iBAAiB;AACpC,SAAK,UAAU;AAAA,EACjB,OAAO;AACL,UAAM,QAAQ;AAAA,EAChB;AACA,OAAK,UAAU;AAEf,SAAO,KAAK,WAAW,OAAO,oBAAoB;AACpD;AAEA,KAAK,wBAAwB,SAAS,MAAM;AAC1C,OAAK,OAAO,CAAC;AAEb,MAAI,YAAY,KAAK;AACrB,OAAK,SAAS,CAAC;AACf,OAAK,WAAW,2BAA2B,WAAW;AACtD,SAAO,KAAK,SAAS,QAAQ,QAAQ;AACnC,QAAI,OAAO,KAAK,eAAe,IAAI;AACnC,SAAK,KAAK,KAAK,IAAI;AAAA,EACrB;AACA,OAAK,KAAK;AACV,OAAK,UAAU;AACf,OAAK,SAAS;AAEd,SAAO,KAAK,WAAW,MAAM,aAAa;AAC5C;AAEA,KAAK,eAAe,SAAS,MAAM,aAAa;AAC9C,MAAI,KAAK,SAAS,QAAQ,MAAM;AAC9B,SAAK,KAAK,KAAK,WAAW;AAC1B,QAAI,aACF;AAAE,WAAK,gBAAgB,KAAK,IAAI,cAAc,KAAK;AAAA,IAAG;AAAA,EAC1D,OAAO;AACL,QAAI,gBAAgB,MAClB;AAAE,WAAK,WAAW;AAAA,IAAG;AACvB,SAAK,KAAK;AAAA,EACZ;AACF;AAEA,KAAK,kBAAkB,SAAS,MAAM;AACpC,OAAK,aAAa,KAAK,IAAI,QAAQ,QAAQ,IAAI,KAAK,oBAAoB,MAAM,KAAK,IAAI;AACzF;AAEA,KAAK,iBAAiB,WAAW;AAC/B,MAAI,UAAU,EAAC,UAAU,uBAAO,OAAO,IAAI,GAAG,MAAM,CAAC,EAAC;AACtD,OAAK,iBAAiB,KAAK,OAAO;AAClC,SAAO,QAAQ;AACjB;AAEA,KAAK,gBAAgB,WAAW;AAC9B,MAAIF,OAAM,KAAK,iBAAiB,IAAI;AACpC,MAAI,WAAWA,KAAI;AACnB,MAAI,OAAOA,KAAI;AACf,MAAI,CAAC,KAAK,QAAQ,oBAAoB;AAAE;AAAA,EAAO;AAC/C,MAAI,MAAM,KAAK,iBAAiB;AAChC,MAAI,SAAS,QAAQ,IAAI,OAAO,KAAK,iBAAiB,MAAM,CAAC;AAC7D,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,QAAI,KAAK,KAAK,CAAC;AACf,QAAI,CAAC,OAAO,UAAU,GAAG,IAAI,GAAG;AAC9B,UAAI,QAAQ;AACV,eAAO,KAAK,KAAK,EAAE;AAAA,MACrB,OAAO;AACL,aAAK,iBAAiB,GAAG,OAAQ,qBAAsB,GAAG,OAAQ,0CAA2C;AAAA,MAC/G;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,wBAAwB,gBAAgB,SAAS;AACxD,MAAI,OAAO,QAAQ,IAAI;AACvB,MAAI,OAAO,eAAe,IAAI;AAE9B,MAAI,OAAO;AACX,MAAI,QAAQ,SAAS,uBAAuB,QAAQ,SAAS,SAAS,QAAQ,SAAS,QAAQ;AAC7F,YAAQ,QAAQ,SAAS,MAAM,OAAO,QAAQ;AAAA,EAChD;AAGA,MACE,SAAS,UAAU,SAAS,UAC5B,SAAS,UAAU,SAAS,UAC5B,SAAS,UAAU,SAAS,UAC5B,SAAS,UAAU,SAAS,QAC5B;AACA,mBAAe,IAAI,IAAI;AACvB,WAAO;AAAA,EACT,WAAW,CAAC,MAAM;AAChB,mBAAe,IAAI,IAAI;AACvB,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,aAAa,MAAM,MAAM;AAChC,MAAI,WAAW,KAAK;AACpB,MAAI,MAAM,KAAK;AACf,SAAO,CAAC,aACN,IAAI,SAAS,gBAAgB,IAAI,SAAS,QAC1C,IAAI,SAAS,aAAa,IAAI,UAAU;AAE5C;AAIA,KAAK,4BAA4B,SAAS,MAAM,SAAS;AACvD,MAAI,KAAK,QAAQ,eAAe,IAAI;AAClC,QAAI,KAAK,cAAc,IAAI,GAAG;AAC5B,WAAK,WAAW,KAAK,sBAAsB;AAC3C,WAAK,YAAY,SAAS,KAAK,UAAU,KAAK,YAAY;AAAA,IAC5D,OAAO;AACL,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AACA,OAAK,iBAAiB,MAAM;AAC5B,MAAI,KAAK,SAAS,QAAQ,QAAQ;AAAE,SAAK,WAAW;AAAA,EAAG;AACvD,OAAK,SAAS,KAAK,cAAc;AACjC,MAAI,KAAK,QAAQ,eAAe,IAC9B;AAAE,SAAK,aAAa,KAAK,gBAAgB;AAAA,EAAG;AAC9C,OAAK,UAAU;AACf,SAAO,KAAK,WAAW,MAAM,sBAAsB;AACrD;AAEA,KAAK,cAAc,SAAS,MAAM,SAAS;AACzC,OAAK,KAAK;AAEV,MAAI,KAAK,IAAI,QAAQ,IAAI,GAAG;AAC1B,WAAO,KAAK,0BAA0B,MAAM,OAAO;AAAA,EACrD;AACA,MAAI,KAAK,IAAI,QAAQ,QAAQ,GAAG;AAC9B,SAAK,YAAY,SAAS,WAAW,KAAK,YAAY;AACtD,SAAK,cAAc,KAAK,8BAA8B;AACtD,WAAO,KAAK,WAAW,MAAM,0BAA0B;AAAA,EACzD;AAEA,MAAI,KAAK,2BAA2B,GAAG;AACrC,SAAK,cAAc,KAAK,uBAAuB,IAAI;AACnD,QAAI,KAAK,YAAY,SAAS,uBAC5B;AAAE,WAAK,oBAAoB,SAAS,KAAK,YAAY,YAAY;AAAA,IAAG,OAEpE;AAAE,WAAK,YAAY,SAAS,KAAK,YAAY,IAAI,KAAK,YAAY,GAAG,KAAK;AAAA,IAAG;AAC/E,SAAK,aAAa,CAAC;AACnB,SAAK,SAAS;AACd,QAAI,KAAK,QAAQ,eAAe,IAC9B;AAAE,WAAK,aAAa,CAAC;AAAA,IAAG;AAAA,EAC5B,OAAO;AACL,SAAK,cAAc;AACnB,SAAK,aAAa,KAAK,sBAAsB,OAAO;AACpD,QAAI,KAAK,cAAc,MAAM,GAAG;AAC9B,UAAI,KAAK,SAAS,QAAQ,QAAQ;AAAE,aAAK,WAAW;AAAA,MAAG;AACvD,WAAK,SAAS,KAAK,cAAc;AACjC,UAAI,KAAK,QAAQ,eAAe,IAC9B;AAAE,aAAK,aAAa,KAAK,gBAAgB;AAAA,MAAG;AAAA,IAChD,OAAO;AACL,eAAS,IAAI,GAAG,OAAO,KAAK,YAAY,IAAI,KAAK,QAAQ,KAAK,GAAG;AAE/D,YAAI,OAAO,KAAK,CAAC;AAEjB,aAAK,gBAAgB,KAAK,KAAK;AAE/B,aAAK,iBAAiB,KAAK,KAAK;AAEhC,YAAI,KAAK,MAAM,SAAS,WAAW;AACjC,eAAK,MAAM,KAAK,MAAM,OAAO,wEAAwE;AAAA,QACvG;AAAA,MACF;AAEA,WAAK,SAAS;AACd,UAAI,KAAK,QAAQ,eAAe,IAC9B;AAAE,aAAK,aAAa,CAAC;AAAA,MAAG;AAAA,IAC5B;AACA,SAAK,UAAU;AAAA,EACjB;AACA,SAAO,KAAK,WAAW,MAAM,wBAAwB;AACvD;AAEA,KAAK,yBAAyB,SAAS,MAAM;AAC3C,SAAO,KAAK,eAAe,IAAI;AACjC;AAEA,KAAK,gCAAgC,WAAW;AAC9C,MAAI;AACJ,MAAI,KAAK,SAAS,QAAQ,cAAc,UAAU,KAAK,gBAAgB,IAAI;AACzE,QAAI,QAAQ,KAAK,UAAU;AAC3B,SAAK,KAAK;AACV,QAAI,SAAS;AAAE,WAAK,KAAK;AAAA,IAAG;AAC5B,WAAO,KAAK,cAAc,OAAO,iBAAiB,kBAAkB,OAAO,OAAO;AAAA,EACpF,WAAW,KAAK,SAAS,QAAQ,QAAQ;AACvC,QAAI,QAAQ,KAAK,UAAU;AAC3B,WAAO,KAAK,WAAW,OAAO,YAAY;AAAA,EAC5C,OAAO;AACL,QAAI,cAAc,KAAK,iBAAiB;AACxC,SAAK,UAAU;AACf,WAAO;AAAA,EACT;AACF;AAEA,KAAK,cAAc,SAAS,SAAS,MAAM,KAAK;AAC9C,MAAI,CAAC,SAAS;AAAE;AAAA,EAAO;AACvB,MAAI,OAAO,SAAS,UAClB;AAAE,WAAO,KAAK,SAAS,eAAe,KAAK,OAAO,KAAK;AAAA,EAAO;AAChE,MAAI,OAAO,SAAS,IAAI,GACtB;AAAE,SAAK,iBAAiB,KAAK,uBAAuB,OAAO,GAAG;AAAA,EAAG;AACnE,UAAQ,IAAI,IAAI;AAClB;AAEA,KAAK,qBAAqB,SAAS,SAAS,KAAK;AAC/C,MAAI,OAAO,IAAI;AACf,MAAI,SAAS,cACX;AAAE,SAAK,YAAY,SAAS,KAAK,IAAI,KAAK;AAAA,EAAG,WACtC,SAAS,iBAChB;AAAE,aAAS,IAAI,GAAG,OAAO,IAAI,YAAY,IAAI,KAAK,QAAQ,KAAK,GAC7D;AACE,UAAI,OAAO,KAAK,CAAC;AAEjB,WAAK,mBAAmB,SAAS,IAAI;AAAA,IACvC;AAAA,EAAE,WACG,SAAS,gBAChB;AAAE,aAAS,MAAM,GAAG,SAAS,IAAI,UAAU,MAAM,OAAO,QAAQ,OAAO,GAAG;AACxE,UAAI,MAAM,OAAO,GAAG;AAElB,UAAI,KAAK;AAAE,aAAK,mBAAmB,SAAS,GAAG;AAAA,MAAG;AAAA,IACtD;AAAA,EAAE,WACK,SAAS,YAChB;AAAE,SAAK,mBAAmB,SAAS,IAAI,KAAK;AAAA,EAAG,WACxC,SAAS,qBAChB;AAAE,SAAK,mBAAmB,SAAS,IAAI,IAAI;AAAA,EAAG,WACvC,SAAS,eAChB;AAAE,SAAK,mBAAmB,SAAS,IAAI,QAAQ;AAAA,EAAG;AACtD;AAEA,KAAK,sBAAsB,SAAS,SAAS,OAAO;AAClD,MAAI,CAAC,SAAS;AAAE;AAAA,EAAO;AACvB,WAAS,IAAI,GAAG,OAAO,OAAO,IAAI,KAAK,QAAQ,KAAK,GAClD;AACA,QAAI,OAAO,KAAK,CAAC;AAEjB,SAAK,mBAAmB,SAAS,KAAK,EAAE;AAAA,EAC1C;AACF;AAEA,KAAK,6BAA6B,WAAW;AAC3C,SAAO,KAAK,KAAK,YAAY,SAC3B,KAAK,KAAK,YAAY,WACtB,KAAK,KAAK,YAAY,WACtB,KAAK,KAAK,YAAY,cACtB,KAAK,MAAM,KACX,KAAK,gBAAgB;AACzB;AAIA,KAAK,uBAAuB,SAAS,SAAS;AAC5C,MAAI,OAAO,KAAK,UAAU;AAC1B,OAAK,QAAQ,KAAK,sBAAsB;AAExC,OAAK,WAAW,KAAK,cAAc,IAAI,IAAI,KAAK,sBAAsB,IAAI,KAAK;AAC/E,OAAK;AAAA,IACH;AAAA,IACA,KAAK;AAAA,IACL,KAAK,SAAS;AAAA,EAChB;AAEA,SAAO,KAAK,WAAW,MAAM,iBAAiB;AAChD;AAEA,KAAK,wBAAwB,SAAS,SAAS;AAC7C,MAAI,QAAQ,CAAC,GAAG,QAAQ;AAExB,OAAK,OAAO,QAAQ,MAAM;AAC1B,SAAO,CAAC,KAAK,IAAI,QAAQ,MAAM,GAAG;AAChC,QAAI,CAAC,OAAO;AACV,WAAK,OAAO,QAAQ,KAAK;AACzB,UAAI,KAAK,mBAAmB,QAAQ,MAAM,GAAG;AAAE;AAAA,MAAM;AAAA,IACvD,OAAO;AAAE,cAAQ;AAAA,IAAO;AAExB,UAAM,KAAK,KAAK,qBAAqB,OAAO,CAAC;AAAA,EAC/C;AACA,SAAO;AACT;AAIA,KAAK,cAAc,SAAS,MAAM;AAChC,OAAK,KAAK;AAGV,MAAI,KAAK,SAAS,QAAQ,QAAQ;AAChC,SAAK,aAAa;AAClB,SAAK,SAAS,KAAK,cAAc;AAAA,EACnC,OAAO;AACL,SAAK,aAAa,KAAK,sBAAsB;AAC7C,SAAK,iBAAiB,MAAM;AAC5B,SAAK,SAAS,KAAK,SAAS,QAAQ,SAAS,KAAK,cAAc,IAAI,KAAK,WAAW;AAAA,EACtF;AACA,MAAI,KAAK,QAAQ,eAAe,IAC9B;AAAE,SAAK,aAAa,KAAK,gBAAgB;AAAA,EAAG;AAC9C,OAAK,UAAU;AACf,SAAO,KAAK,WAAW,MAAM,mBAAmB;AAClD;AAIA,KAAK,uBAAuB,WAAW;AACrC,MAAI,OAAO,KAAK,UAAU;AAC1B,OAAK,WAAW,KAAK,sBAAsB;AAE3C,MAAI,KAAK,cAAc,IAAI,GAAG;AAC5B,SAAK,QAAQ,KAAK,WAAW;AAAA,EAC/B,OAAO;AACL,SAAK,gBAAgB,KAAK,QAAQ;AAClC,SAAK,QAAQ,KAAK;AAAA,EACpB;AACA,OAAK,gBAAgB,KAAK,OAAO,YAAY;AAE7C,SAAO,KAAK,WAAW,MAAM,iBAAiB;AAChD;AAEA,KAAK,8BAA8B,WAAW;AAE5C,MAAI,OAAO,KAAK,UAAU;AAC1B,OAAK,QAAQ,KAAK,WAAW;AAC7B,OAAK,gBAAgB,KAAK,OAAO,YAAY;AAC7C,SAAO,KAAK,WAAW,MAAM,wBAAwB;AACvD;AAEA,KAAK,gCAAgC,WAAW;AAC9C,MAAI,OAAO,KAAK,UAAU;AAC1B,OAAK,KAAK;AACV,OAAK,iBAAiB,IAAI;AAC1B,OAAK,QAAQ,KAAK,WAAW;AAC7B,OAAK,gBAAgB,KAAK,OAAO,YAAY;AAC7C,SAAO,KAAK,WAAW,MAAM,0BAA0B;AACzD;AAEA,KAAK,wBAAwB,WAAW;AACtC,MAAI,QAAQ,CAAC,GAAG,QAAQ;AACxB,MAAI,KAAK,SAAS,QAAQ,MAAM;AAC9B,UAAM,KAAK,KAAK,4BAA4B,CAAC;AAC7C,QAAI,CAAC,KAAK,IAAI,QAAQ,KAAK,GAAG;AAAE,aAAO;AAAA,IAAM;AAAA,EAC/C;AACA,MAAI,KAAK,SAAS,QAAQ,MAAM;AAC9B,UAAM,KAAK,KAAK,8BAA8B,CAAC;AAC/C,WAAO;AAAA,EACT;AACA,OAAK,OAAO,QAAQ,MAAM;AAC1B,SAAO,CAAC,KAAK,IAAI,QAAQ,MAAM,GAAG;AAChC,QAAI,CAAC,OAAO;AACV,WAAK,OAAO,QAAQ,KAAK;AACzB,UAAI,KAAK,mBAAmB,QAAQ,MAAM,GAAG;AAAE;AAAA,MAAM;AAAA,IACvD,OAAO;AAAE,cAAQ;AAAA,IAAO;AAExB,UAAM,KAAK,KAAK,qBAAqB,CAAC;AAAA,EACxC;AACA,SAAO;AACT;AAEA,KAAK,kBAAkB,WAAW;AAChC,MAAI,QAAQ,CAAC;AACb,MAAI,CAAC,KAAK,IAAI,QAAQ,KAAK,GAAG;AAC5B,WAAO;AAAA,EACT;AACA,OAAK,OAAO,QAAQ,MAAM;AAC1B,MAAI,gBAAgB,CAAC;AACrB,MAAI,QAAQ;AACZ,SAAO,CAAC,KAAK,IAAI,QAAQ,MAAM,GAAG;AAChC,QAAI,CAAC,OAAO;AACV,WAAK,OAAO,QAAQ,KAAK;AACzB,UAAI,KAAK,mBAAmB,QAAQ,MAAM,GAAG;AAAE;AAAA,MAAM;AAAA,IACvD,OAAO;AAAE,cAAQ;AAAA,IAAO;AAExB,QAAI,OAAO,KAAK,qBAAqB;AACrC,QAAI,UAAU,KAAK,IAAI,SAAS,eAAe,KAAK,IAAI,OAAO,KAAK,IAAI;AACxE,QAAI,OAAO,eAAe,OAAO,GAC/B;AAAE,WAAK,iBAAiB,KAAK,IAAI,OAAO,8BAA8B,UAAU,GAAG;AAAA,IAAG;AACxF,kBAAc,OAAO,IAAI;AACzB,UAAM,KAAK,IAAI;AAAA,EACjB;AACA,SAAO;AACT;AAEA,KAAK,uBAAuB,WAAW;AACrC,MAAI,OAAO,KAAK,UAAU;AAC1B,OAAK,MAAM,KAAK,SAAS,QAAQ,SAAS,KAAK,cAAc,IAAI,KAAK,WAAW,KAAK,QAAQ,kBAAkB,OAAO;AACvH,OAAK,OAAO,QAAQ,KAAK;AACzB,MAAI,KAAK,SAAS,QAAQ,QAAQ;AAChC,SAAK,WAAW;AAAA,EAClB;AACA,OAAK,QAAQ,KAAK,cAAc;AAChC,SAAO,KAAK,WAAW,MAAM,iBAAiB;AAChD;AAEA,KAAK,wBAAwB,WAAW;AACtC,MAAI,KAAK,QAAQ,eAAe,MAAM,KAAK,SAAS,QAAQ,QAAQ;AAClE,QAAI,gBAAgB,KAAK,aAAa,KAAK,KAAK;AAChD,QAAI,cAAc,KAAK,cAAc,KAAK,GAAG;AAC3C,WAAK,MAAM,cAAc,OAAO,iDAAiD;AAAA,IACnF;AACA,WAAO;AAAA,EACT;AACA,SAAO,KAAK,WAAW,IAAI;AAC7B;AAGA,KAAK,yBAAyB,SAAS,YAAY;AACjD,WAAS,IAAI,GAAG,IAAI,WAAW,UAAU,KAAK,qBAAqB,WAAW,CAAC,CAAC,GAAG,EAAE,GAAG;AACtF,eAAW,CAAC,EAAE,YAAY,WAAW,CAAC,EAAE,WAAW,IAAI,MAAM,GAAG,EAAE;AAAA,EACpE;AACF;AACA,KAAK,uBAAuB,SAAS,WAAW;AAC9C,SACE,KAAK,QAAQ,eAAe,KAC5B,UAAU,SAAS,yBACnB,UAAU,WAAW,SAAS,aAC9B,OAAO,UAAU,WAAW,UAAU;AAAA,GAErC,KAAK,MAAM,UAAU,KAAK,MAAM,OAAQ,KAAK,MAAM,UAAU,KAAK,MAAM;AAE7E;AAEA,IAAI,OAAO,OAAO;AAKlB,KAAK,eAAe,SAAS,MAAM,WAAW,wBAAwB;AACpE,MAAI,KAAK,QAAQ,eAAe,KAAK,MAAM;AACzC,YAAQ,KAAK,MAAM;AAAA,MACnB,KAAK;AACH,YAAI,KAAK,WAAW,KAAK,SAAS,SAChC;AAAE,eAAK,MAAM,KAAK,OAAO,2DAA2D;AAAA,QAAG;AACzF;AAAA,MAEF,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH;AAAA,MAEF,KAAK;AACH,aAAK,OAAO;AACZ,YAAI,wBAAwB;AAAE,eAAK,mBAAmB,wBAAwB,IAAI;AAAA,QAAG;AACrF,iBAAS,IAAI,GAAG,OAAO,KAAK,YAAY,IAAI,KAAK,QAAQ,KAAK,GAAG;AAC/D,cAAI,OAAO,KAAK,CAAC;AAEnB,eAAK,aAAa,MAAM,SAAS;AAM/B,cACE,KAAK,SAAS,kBACb,KAAK,SAAS,SAAS,kBAAkB,KAAK,SAAS,SAAS,kBACjE;AACA,iBAAK,MAAM,KAAK,SAAS,OAAO,kBAAkB;AAAA,UACpD;AAAA,QACF;AACA;AAAA,MAEF,KAAK;AAEH,YAAI,KAAK,SAAS,QAAQ;AAAE,eAAK,MAAM,KAAK,IAAI,OAAO,+CAA+C;AAAA,QAAG;AACzG,aAAK,aAAa,KAAK,OAAO,SAAS;AACvC;AAAA,MAEF,KAAK;AACH,aAAK,OAAO;AACZ,YAAI,wBAAwB;AAAE,eAAK,mBAAmB,wBAAwB,IAAI;AAAA,QAAG;AACrF,aAAK,iBAAiB,KAAK,UAAU,SAAS;AAC9C;AAAA,MAEF,KAAK;AACH,aAAK,OAAO;AACZ,aAAK,aAAa,KAAK,UAAU,SAAS;AAC1C,YAAI,KAAK,SAAS,SAAS,qBACzB;AAAE,eAAK,MAAM,KAAK,SAAS,OAAO,2CAA2C;AAAA,QAAG;AAClF;AAAA,MAEF,KAAK;AACH,YAAI,KAAK,aAAa,KAAK;AAAE,eAAK,MAAM,KAAK,KAAK,KAAK,6DAA6D;AAAA,QAAG;AACvH,aAAK,OAAO;AACZ,eAAO,KAAK;AACZ,aAAK,aAAa,KAAK,MAAM,SAAS;AACtC;AAAA,MAEF,KAAK;AACH,aAAK,aAAa,KAAK,YAAY,WAAW,sBAAsB;AACpE;AAAA,MAEF,KAAK;AACH,aAAK,iBAAiB,KAAK,OAAO,mDAAmD;AACrF;AAAA,MAEF,KAAK;AACH,YAAI,CAAC,WAAW;AAAE;AAAA,QAAM;AAAA,MAE1B;AACE,aAAK,MAAM,KAAK,OAAO,qBAAqB;AAAA,IAC9C;AAAA,EACF,WAAW,wBAAwB;AAAE,SAAK,mBAAmB,wBAAwB,IAAI;AAAA,EAAG;AAC5F,SAAO;AACT;AAIA,KAAK,mBAAmB,SAAS,UAAU,WAAW;AACpD,MAAI,MAAM,SAAS;AACnB,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,QAAI,MAAM,SAAS,CAAC;AACpB,QAAI,KAAK;AAAE,WAAK,aAAa,KAAK,SAAS;AAAA,IAAG;AAAA,EAChD;AACA,MAAI,KAAK;AACP,QAAI,OAAO,SAAS,MAAM,CAAC;AAC3B,QAAI,KAAK,QAAQ,gBAAgB,KAAK,aAAa,QAAQ,KAAK,SAAS,iBAAiB,KAAK,SAAS,SAAS,cAC/G;AAAE,WAAK,WAAW,KAAK,SAAS,KAAK;AAAA,IAAG;AAAA,EAC5C;AACA,SAAO;AACT;AAIA,KAAK,cAAc,SAAS,wBAAwB;AAClD,MAAI,OAAO,KAAK,UAAU;AAC1B,OAAK,KAAK;AACV,OAAK,WAAW,KAAK,iBAAiB,OAAO,sBAAsB;AACnE,SAAO,KAAK,WAAW,MAAM,eAAe;AAC9C;AAEA,KAAK,mBAAmB,WAAW;AACjC,MAAI,OAAO,KAAK,UAAU;AAC1B,OAAK,KAAK;AAGV,MAAI,KAAK,QAAQ,gBAAgB,KAAK,KAAK,SAAS,QAAQ,MAC1D;AAAE,SAAK,WAAW;AAAA,EAAG;AAEvB,OAAK,WAAW,KAAK,iBAAiB;AAEtC,SAAO,KAAK,WAAW,MAAM,aAAa;AAC5C;AAIA,KAAK,mBAAmB,WAAW;AACjC,MAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,YAAQ,KAAK,MAAM;AAAA,MACnB,KAAK,QAAQ;AACX,YAAI,OAAO,KAAK,UAAU;AAC1B,aAAK,KAAK;AACV,aAAK,WAAW,KAAK,iBAAiB,QAAQ,UAAU,MAAM,IAAI;AAClE,eAAO,KAAK,WAAW,MAAM,cAAc;AAAA,MAE7C,KAAK,QAAQ;AACX,eAAO,KAAK,SAAS,IAAI;AAAA,IAC3B;AAAA,EACF;AACA,SAAO,KAAK,WAAW;AACzB;AAEA,KAAK,mBAAmB,SAAS,OAAO,YAAY,oBAAoB,gBAAgB;AACtF,MAAI,OAAO,CAAC,GAAG,QAAQ;AACvB,SAAO,CAAC,KAAK,IAAI,KAAK,GAAG;AACvB,QAAI,OAAO;AAAE,cAAQ;AAAA,IAAO,OACvB;AAAE,WAAK,OAAO,QAAQ,KAAK;AAAA,IAAG;AACnC,QAAI,cAAc,KAAK,SAAS,QAAQ,OAAO;AAC7C,WAAK,KAAK,IAAI;AAAA,IAChB,WAAW,sBAAsB,KAAK,mBAAmB,KAAK,GAAG;AAC/D;AAAA,IACF,WAAW,KAAK,SAAS,QAAQ,UAAU;AACzC,UAAI,OAAO,KAAK,iBAAiB;AACjC,WAAK,qBAAqB,IAAI;AAC9B,WAAK,KAAK,IAAI;AACd,UAAI,KAAK,SAAS,QAAQ,OAAO;AAAE,aAAK,iBAAiB,KAAK,OAAO,+CAA+C;AAAA,MAAG;AACvH,WAAK,OAAO,KAAK;AACjB;AAAA,IACF,OAAO;AACL,WAAK,KAAK,KAAK,wBAAwB,cAAc,CAAC;AAAA,IACxD;AAAA,EACF;AACA,SAAO;AACT;AAEA,KAAK,0BAA0B,SAAS,gBAAgB;AACtD,MAAI,OAAO,KAAK,kBAAkB,KAAK,OAAO,KAAK,QAAQ;AAC3D,OAAK,qBAAqB,IAAI;AAC9B,SAAO;AACT;AAEA,KAAK,uBAAuB,SAAS,OAAO;AAC1C,SAAO;AACT;AAIA,KAAK,oBAAoB,SAAS,UAAU,UAAU,MAAM;AAC1D,SAAO,QAAQ,KAAK,iBAAiB;AACrC,MAAI,KAAK,QAAQ,cAAc,KAAK,CAAC,KAAK,IAAI,QAAQ,EAAE,GAAG;AAAE,WAAO;AAAA,EAAK;AACzE,MAAI,OAAO,KAAK,YAAY,UAAU,QAAQ;AAC9C,OAAK,OAAO;AACZ,OAAK,QAAQ,KAAK,iBAAiB;AACnC,SAAO,KAAK,WAAW,MAAM,mBAAmB;AAClD;AAkEA,KAAK,kBAAkB,SAAS,MAAM,aAAa,cAAc;AAC/D,MAAK,gBAAgB,OAAS,eAAc;AAE5C,MAAI,SAAS,gBAAgB;AAE7B,UAAQ,KAAK,MAAM;AAAA,IACnB,KAAK;AACH,UAAI,KAAK,UAAU,KAAK,wBAAwB,KAAK,KAAK,IAAI,GAC5D;AAAE,aAAK,iBAAiB,KAAK,QAAQ,SAAS,aAAa,mBAAmB,KAAK,OAAO,iBAAiB;AAAA,MAAG;AAChH,UAAI,QAAQ;AACV,YAAI,gBAAgB,gBAAgB,KAAK,SAAS,OAChD;AAAE,eAAK,iBAAiB,KAAK,OAAO,6CAA6C;AAAA,QAAG;AACtF,YAAI,cAAc;AAChB,cAAI,OAAO,cAAc,KAAK,IAAI,GAChC;AAAE,iBAAK,iBAAiB,KAAK,OAAO,qBAAqB;AAAA,UAAG;AAC9D,uBAAa,KAAK,IAAI,IAAI;AAAA,QAC5B;AACA,YAAI,gBAAgB,cAAc;AAAE,eAAK,YAAY,KAAK,MAAM,aAAa,KAAK,KAAK;AAAA,QAAG;AAAA,MAC5F;AACA;AAAA,IAEF,KAAK;AACH,WAAK,iBAAiB,KAAK,OAAO,mDAAmD;AACrF;AAAA,IAEF,KAAK;AACH,UAAI,QAAQ;AAAE,aAAK,iBAAiB,KAAK,OAAO,2BAA2B;AAAA,MAAG;AAC9E;AAAA,IAEF,KAAK;AACH,UAAI,QAAQ;AAAE,aAAK,iBAAiB,KAAK,OAAO,kCAAkC;AAAA,MAAG;AACrF,aAAO,KAAK,gBAAgB,KAAK,YAAY,aAAa,YAAY;AAAA,IAExE;AACE,WAAK,MAAM,KAAK,QAAQ,SAAS,YAAY,kBAAkB,SAAS;AAAA,EAC1E;AACF;AAEA,KAAK,mBAAmB,SAAS,MAAM,aAAa,cAAc;AAChE,MAAK,gBAAgB,OAAS,eAAc;AAE5C,UAAQ,KAAK,MAAM;AAAA,IACnB,KAAK;AACH,eAAS,IAAI,GAAG,OAAO,KAAK,YAAY,IAAI,KAAK,QAAQ,KAAK,GAAG;AAC/D,YAAI,OAAO,KAAK,CAAC;AAEnB,aAAK,sBAAsB,MAAM,aAAa,YAAY;AAAA,MAC1D;AACA;AAAA,IAEF,KAAK;AACH,eAAS,MAAM,GAAG,SAAS,KAAK,UAAU,MAAM,OAAO,QAAQ,OAAO,GAAG;AACvE,YAAI,OAAO,OAAO,GAAG;AAEvB,YAAI,MAAM;AAAE,eAAK,sBAAsB,MAAM,aAAa,YAAY;AAAA,QAAG;AAAA,MACzE;AACA;AAAA,IAEF;AACE,WAAK,gBAAgB,MAAM,aAAa,YAAY;AAAA,EACtD;AACF;AAEA,KAAK,wBAAwB,SAAS,MAAM,aAAa,cAAc;AACrE,MAAK,gBAAgB,OAAS,eAAc;AAE5C,UAAQ,KAAK,MAAM;AAAA,IACnB,KAAK;AAEH,WAAK,sBAAsB,KAAK,OAAO,aAAa,YAAY;AAChE;AAAA,IAEF,KAAK;AACH,WAAK,iBAAiB,KAAK,MAAM,aAAa,YAAY;AAC1D;AAAA,IAEF,KAAK;AACH,WAAK,iBAAiB,KAAK,UAAU,aAAa,YAAY;AAC9D;AAAA,IAEF;AACE,WAAK,iBAAiB,MAAM,aAAa,YAAY;AAAA,EACvD;AACF;AAOA,IAAI,aAAa,SAASG,YAAW,OAAO,QAAQ,eAAe,UAAU,WAAW;AACtF,OAAK,QAAQ;AACb,OAAK,SAAS,CAAC,CAAC;AAChB,OAAK,gBAAgB,CAAC,CAAC;AACvB,OAAK,WAAW;AAChB,OAAK,YAAY,CAAC,CAAC;AACrB;AAEA,IAAI,QAAQ;AAAA,EACV,QAAQ,IAAI,WAAW,KAAK,KAAK;AAAA,EACjC,QAAQ,IAAI,WAAW,KAAK,IAAI;AAAA,EAChC,QAAQ,IAAI,WAAW,MAAM,KAAK;AAAA,EAClC,QAAQ,IAAI,WAAW,KAAK,KAAK;AAAA,EACjC,QAAQ,IAAI,WAAW,KAAK,IAAI;AAAA,EAChC,QAAQ,IAAI,WAAW,KAAK,MAAM,MAAM,SAAU,GAAG;AAAE,WAAO,EAAE,qBAAqB;AAAA,EAAG,CAAC;AAAA,EACzF,QAAQ,IAAI,WAAW,YAAY,KAAK;AAAA,EACxC,QAAQ,IAAI,WAAW,YAAY,IAAI;AAAA,EACvC,YAAY,IAAI,WAAW,YAAY,MAAM,OAAO,MAAM,IAAI;AAAA,EAC9D,OAAO,IAAI,WAAW,YAAY,OAAO,OAAO,MAAM,IAAI;AAC5D;AAEA,IAAI,OAAO,OAAO;AAElB,KAAK,iBAAiB,WAAW;AAC/B,SAAO,CAAC,MAAM,MAAM;AACtB;AAEA,KAAK,aAAa,WAAW;AAC3B,SAAO,KAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC;AAC7C;AAEA,KAAK,eAAe,SAAS,UAAU;AACrC,MAAI,SAAS,KAAK,WAAW;AAC7B,MAAI,WAAW,MAAM,UAAU,WAAW,MAAM,QAC9C;AAAE,WAAO;AAAA,EAAK;AAChB,MAAI,aAAa,QAAQ,UAAU,WAAW,MAAM,UAAU,WAAW,MAAM,SAC7E;AAAE,WAAO,CAAC,OAAO;AAAA,EAAO;AAK1B,MAAI,aAAa,QAAQ,WAAW,aAAa,QAAQ,QAAQ,KAAK,aACpE;AAAE,WAAO,UAAU,KAAK,KAAK,MAAM,MAAM,KAAK,YAAY,KAAK,KAAK,CAAC;AAAA,EAAE;AACzE,MAAI,aAAa,QAAQ,SAAS,aAAa,QAAQ,QAAQ,aAAa,QAAQ,OAAO,aAAa,QAAQ,UAAU,aAAa,QAAQ,OAC7I;AAAE,WAAO;AAAA,EAAK;AAChB,MAAI,aAAa,QAAQ,QACvB;AAAE,WAAO,WAAW,MAAM;AAAA,EAAO;AACnC,MAAI,aAAa,QAAQ,QAAQ,aAAa,QAAQ,UAAU,aAAa,QAAQ,MACnF;AAAE,WAAO;AAAA,EAAM;AACjB,SAAO,CAAC,KAAK;AACf;AAEA,KAAK,qBAAqB,WAAW;AACnC,WAAS,IAAI,KAAK,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AACjD,QAAI,UAAU,KAAK,QAAQ,CAAC;AAC5B,QAAI,QAAQ,UAAU,YACpB;AAAE,aAAO,QAAQ;AAAA,IAAU;AAAA,EAC/B;AACA,SAAO;AACT;AAEA,KAAK,gBAAgB,SAAS,UAAU;AACtC,MAAI,QAAQ,OAAO,KAAK;AACxB,MAAI,KAAK,WAAW,aAAa,QAAQ,KACvC;AAAE,SAAK,cAAc;AAAA,EAAO,WACrB,SAAS,KAAK,eACrB;AAAE,WAAO,KAAK,MAAM,QAAQ;AAAA,EAAG,OAE/B;AAAE,SAAK,cAAc,KAAK;AAAA,EAAY;AAC1C;AAIA,KAAK,kBAAkB,SAAS,UAAU;AACxC,MAAI,KAAK,WAAW,MAAM,UAAU;AAClC,SAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC,IAAI;AAAA,EAC1C;AACF;AAIA,QAAQ,OAAO,gBAAgB,QAAQ,OAAO,gBAAgB,WAAW;AACvE,MAAI,KAAK,QAAQ,WAAW,GAAG;AAC7B,SAAK,cAAc;AACnB;AAAA,EACF;AACA,MAAI,MAAM,KAAK,QAAQ,IAAI;AAC3B,MAAI,QAAQ,MAAM,UAAU,KAAK,WAAW,EAAE,UAAU,YAAY;AAClE,UAAM,KAAK,QAAQ,IAAI;AAAA,EACzB;AACA,OAAK,cAAc,CAAC,IAAI;AAC1B;AAEA,QAAQ,OAAO,gBAAgB,SAAS,UAAU;AAChD,OAAK,QAAQ,KAAK,KAAK,aAAa,QAAQ,IAAI,MAAM,SAAS,MAAM,MAAM;AAC3E,OAAK,cAAc;AACrB;AAEA,QAAQ,aAAa,gBAAgB,WAAW;AAC9C,OAAK,QAAQ,KAAK,MAAM,MAAM;AAC9B,OAAK,cAAc;AACrB;AAEA,QAAQ,OAAO,gBAAgB,SAAS,UAAU;AAChD,MAAI,kBAAkB,aAAa,QAAQ,OAAO,aAAa,QAAQ,QAAQ,aAAa,QAAQ,SAAS,aAAa,QAAQ;AAClI,OAAK,QAAQ,KAAK,kBAAkB,MAAM,SAAS,MAAM,MAAM;AAC/D,OAAK,cAAc;AACrB;AAEA,QAAQ,OAAO,gBAAgB,WAAW;AAE1C;AAEA,QAAQ,UAAU,gBAAgB,QAAQ,OAAO,gBAAgB,SAAS,UAAU;AAClF,MAAI,SAAS,cAAc,aAAa,QAAQ,SAC5C,EAAE,aAAa,QAAQ,QAAQ,KAAK,WAAW,MAAM,MAAM,WAC3D,EAAE,aAAa,QAAQ,WAAW,UAAU,KAAK,KAAK,MAAM,MAAM,KAAK,YAAY,KAAK,KAAK,CAAC,MAC9F,GAAG,aAAa,QAAQ,SAAS,aAAa,QAAQ,WAAW,KAAK,WAAW,MAAM,MAAM,SAC/F;AAAE,SAAK,QAAQ,KAAK,MAAM,MAAM;AAAA,EAAG,OAEnC;AAAE,SAAK,QAAQ,KAAK,MAAM,MAAM;AAAA,EAAG;AACrC,OAAK,cAAc;AACrB;AAEA,QAAQ,MAAM,gBAAgB,WAAW;AACvC,MAAI,KAAK,WAAW,EAAE,UAAU,YAAY;AAAE,SAAK,QAAQ,IAAI;AAAA,EAAG;AAClE,OAAK,cAAc;AACrB;AAEA,QAAQ,UAAU,gBAAgB,WAAW;AAC3C,MAAI,KAAK,WAAW,MAAM,MAAM,QAC9B;AAAE,SAAK,QAAQ,IAAI;AAAA,EAAG,OAEtB;AAAE,SAAK,QAAQ,KAAK,MAAM,MAAM;AAAA,EAAG;AACrC,OAAK,cAAc;AACrB;AAEA,QAAQ,KAAK,gBAAgB,SAAS,UAAU;AAC9C,MAAI,aAAa,QAAQ,WAAW;AAClC,QAAI,QAAQ,KAAK,QAAQ,SAAS;AAClC,QAAI,KAAK,QAAQ,KAAK,MAAM,MAAM,QAChC;AAAE,WAAK,QAAQ,KAAK,IAAI,MAAM;AAAA,IAAY,OAE1C;AAAE,WAAK,QAAQ,KAAK,IAAI,MAAM;AAAA,IAAO;AAAA,EACzC;AACA,OAAK,cAAc;AACrB;AAEA,QAAQ,KAAK,gBAAgB,SAAS,UAAU;AAC9C,MAAI,UAAU;AACd,MAAI,KAAK,QAAQ,eAAe,KAAK,aAAa,QAAQ,KAAK;AAC7D,QAAI,KAAK,UAAU,QAAQ,CAAC,KAAK,eAC7B,KAAK,UAAU,WAAW,KAAK,mBAAmB,GACpD;AAAE,gBAAU;AAAA,IAAM;AAAA,EACtB;AACA,OAAK,cAAc;AACrB;AAqBA,IAAI,OAAO,OAAO;AAOlB,KAAK,iBAAiB,SAAS,MAAM,UAAU,wBAAwB;AACrE,MAAI,KAAK,QAAQ,eAAe,KAAK,KAAK,SAAS,iBACjD;AAAE;AAAA,EAAO;AACX,MAAI,KAAK,QAAQ,eAAe,MAAM,KAAK,YAAY,KAAK,UAAU,KAAK,YACzE;AAAE;AAAA,EAAO;AACX,MAAI,MAAM,KAAK;AACf,MAAI;AACJ,UAAQ,IAAI,MAAM;AAAA,IAClB,KAAK;AAAc,aAAO,IAAI;AAAM;AAAA,IACpC,KAAK;AAAW,aAAO,OAAO,IAAI,KAAK;AAAG;AAAA,IAC1C;AAAS;AAAA,EACT;AACA,MAAI,OAAO,KAAK;AAChB,MAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,QAAI,SAAS,eAAe,SAAS,QAAQ;AAC3C,UAAI,SAAS,OAAO;AAClB,YAAI,wBAAwB;AAC1B,cAAI,uBAAuB,cAAc,GAAG;AAC1C,mCAAuB,cAAc,IAAI;AAAA,UAC3C;AAAA,QACF,OAAO;AACL,eAAK,iBAAiB,IAAI,OAAO,oCAAoC;AAAA,QACvE;AAAA,MACF;AACA,eAAS,QAAQ;AAAA,IACnB;AACA;AAAA,EACF;AACA,SAAO,MAAM;AACb,MAAI,QAAQ,SAAS,IAAI;AACzB,MAAI,OAAO;AACT,QAAI;AACJ,QAAI,SAAS,QAAQ;AACnB,qBAAe,KAAK,UAAU,MAAM,QAAQ,MAAM,OAAO,MAAM;AAAA,IACjE,OAAO;AACL,qBAAe,MAAM,QAAQ,MAAM,IAAI;AAAA,IACzC;AACA,QAAI,cACF;AAAE,WAAK,iBAAiB,IAAI,OAAO,0BAA0B;AAAA,IAAG;AAAA,EACpE,OAAO;AACL,YAAQ,SAAS,IAAI,IAAI;AAAA,MACvB,MAAM;AAAA,MACN,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACA,QAAM,IAAI,IAAI;AAChB;AAiBA,KAAK,kBAAkB,SAAS,SAAS,wBAAwB;AAC/D,MAAI,WAAW,KAAK,OAAO,WAAW,KAAK;AAC3C,MAAI,OAAO,KAAK,iBAAiB,SAAS,sBAAsB;AAChE,MAAI,KAAK,SAAS,QAAQ,OAAO;AAC/B,QAAI,OAAO,KAAK,YAAY,UAAU,QAAQ;AAC9C,SAAK,cAAc,CAAC,IAAI;AACxB,WAAO,KAAK,IAAI,QAAQ,KAAK,GAAG;AAAE,WAAK,YAAY,KAAK,KAAK,iBAAiB,SAAS,sBAAsB,CAAC;AAAA,IAAG;AACjH,WAAO,KAAK,WAAW,MAAM,oBAAoB;AAAA,EACnD;AACA,SAAO;AACT;AAKA,KAAK,mBAAmB,SAAS,SAAS,wBAAwB,gBAAgB;AAChF,MAAI,KAAK,aAAa,OAAO,GAAG;AAC9B,QAAI,KAAK,aAAa;AAAE,aAAO,KAAK,WAAW,OAAO;AAAA,IAAE,OAGnD;AAAE,WAAK,cAAc;AAAA,IAAO;AAAA,EACnC;AAEA,MAAI,yBAAyB,OAAO,iBAAiB,IAAI,mBAAmB,IAAI,iBAAiB;AACjG,MAAI,wBAAwB;AAC1B,qBAAiB,uBAAuB;AACxC,uBAAmB,uBAAuB;AAC1C,qBAAiB,uBAAuB;AACxC,2BAAuB,sBAAsB,uBAAuB,gBAAgB;AAAA,EACtF,OAAO;AACL,6BAAyB,IAAI;AAC7B,6BAAyB;AAAA,EAC3B;AAEA,MAAI,WAAW,KAAK,OAAO,WAAW,KAAK;AAC3C,MAAI,KAAK,SAAS,QAAQ,UAAU,KAAK,SAAS,QAAQ,MAAM;AAC9D,SAAK,mBAAmB,KAAK;AAC7B,SAAK,2BAA2B,YAAY;AAAA,EAC9C;AACA,MAAI,OAAO,KAAK,sBAAsB,SAAS,sBAAsB;AACrE,MAAI,gBAAgB;AAAE,WAAO,eAAe,KAAK,MAAM,MAAM,UAAU,QAAQ;AAAA,EAAG;AAClF,MAAI,KAAK,KAAK,UAAU;AACtB,QAAI,OAAO,KAAK,YAAY,UAAU,QAAQ;AAC9C,SAAK,WAAW,KAAK;AACrB,QAAI,KAAK,SAAS,QAAQ,IACxB;AAAE,aAAO,KAAK,aAAa,MAAM,OAAO,sBAAsB;AAAA,IAAG;AACnE,QAAI,CAAC,wBAAwB;AAC3B,6BAAuB,sBAAsB,uBAAuB,gBAAgB,uBAAuB,cAAc;AAAA,IAC3H;AACA,QAAI,uBAAuB,mBAAmB,KAAK,OACjD;AAAE,6BAAuB,kBAAkB;AAAA,IAAI;AACjD,QAAI,KAAK,SAAS,QAAQ,IACxB;AAAE,WAAK,iBAAiB,IAAI;AAAA,IAAG,OAE/B;AAAE,WAAK,gBAAgB,IAAI;AAAA,IAAG;AAChC,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,QAAQ,KAAK,iBAAiB,OAAO;AAC1C,QAAI,iBAAiB,IAAI;AAAE,6BAAuB,cAAc;AAAA,IAAgB;AAChF,WAAO,KAAK,WAAW,MAAM,sBAAsB;AAAA,EACrD,OAAO;AACL,QAAI,wBAAwB;AAAE,WAAK,sBAAsB,wBAAwB,IAAI;AAAA,IAAG;AAAA,EAC1F;AACA,MAAI,iBAAiB,IAAI;AAAE,2BAAuB,sBAAsB;AAAA,EAAgB;AACxF,MAAI,mBAAmB,IAAI;AAAE,2BAAuB,gBAAgB;AAAA,EAAkB;AACtF,SAAO;AACT;AAIA,KAAK,wBAAwB,SAAS,SAAS,wBAAwB;AACrE,MAAI,WAAW,KAAK,OAAO,WAAW,KAAK;AAC3C,MAAI,OAAO,KAAK,aAAa,SAAS,sBAAsB;AAC5D,MAAI,KAAK,sBAAsB,sBAAsB,GAAG;AAAE,WAAO;AAAA,EAAK;AACtE,MAAI,KAAK,IAAI,QAAQ,QAAQ,GAAG;AAC9B,QAAI,OAAO,KAAK,YAAY,UAAU,QAAQ;AAC9C,SAAK,OAAO;AACZ,SAAK,aAAa,KAAK,iBAAiB;AACxC,SAAK,OAAO,QAAQ,KAAK;AACzB,SAAK,YAAY,KAAK,iBAAiB,OAAO;AAC9C,WAAO,KAAK,WAAW,MAAM,uBAAuB;AAAA,EACtD;AACA,SAAO;AACT;AAIA,KAAK,eAAe,SAAS,SAAS,wBAAwB;AAC5D,MAAI,WAAW,KAAK,OAAO,WAAW,KAAK;AAC3C,MAAI,OAAO,KAAK,gBAAgB,wBAAwB,OAAO,OAAO,OAAO;AAC7E,MAAI,KAAK,sBAAsB,sBAAsB,GAAG;AAAE,WAAO;AAAA,EAAK;AACtE,SAAO,KAAK,UAAU,YAAY,KAAK,SAAS,4BAA4B,OAAO,KAAK,YAAY,MAAM,UAAU,UAAU,IAAI,OAAO;AAC3I;AAQA,KAAK,cAAc,SAAS,MAAM,cAAc,cAAc,SAAS,SAAS;AAC9E,MAAI,OAAO,KAAK,KAAK;AACrB,MAAI,QAAQ,SAAS,CAAC,WAAW,KAAK,SAAS,QAAQ,MAAM;AAC3D,QAAI,OAAO,SAAS;AAClB,UAAI,UAAU,KAAK,SAAS,QAAQ,aAAa,KAAK,SAAS,QAAQ;AACvE,UAAI,WAAW,KAAK,SAAS,QAAQ;AACrC,UAAI,UAAU;AAGZ,eAAO,QAAQ,WAAW;AAAA,MAC5B;AACA,UAAI,KAAK,KAAK;AACd,WAAK,KAAK;AACV,UAAI,WAAW,KAAK,OAAO,WAAW,KAAK;AAC3C,UAAI,QAAQ,KAAK,YAAY,KAAK,gBAAgB,MAAM,OAAO,OAAO,OAAO,GAAG,UAAU,UAAU,MAAM,OAAO;AACjH,UAAI,OAAO,KAAK,YAAY,cAAc,cAAc,MAAM,OAAO,IAAI,WAAW,QAAQ;AAC5F,UAAK,WAAW,KAAK,SAAS,QAAQ,YAAc,aAAa,KAAK,SAAS,QAAQ,aAAa,KAAK,SAAS,QAAQ,aAAc;AACtI,aAAK,iBAAiB,KAAK,OAAO,0FAA0F;AAAA,MAC9H;AACA,aAAO,KAAK,YAAY,MAAM,cAAc,cAAc,SAAS,OAAO;AAAA,IAC5E;AAAA,EACF;AACA,SAAO;AACT;AAEA,KAAK,cAAc,SAAS,UAAU,UAAU,MAAM,OAAO,IAAI,SAAS;AACxE,MAAI,MAAM,SAAS,qBAAqB;AAAE,SAAK,MAAM,MAAM,OAAO,+DAA+D;AAAA,EAAG;AACpI,MAAI,OAAO,KAAK,YAAY,UAAU,QAAQ;AAC9C,OAAK,OAAO;AACZ,OAAK,WAAW;AAChB,OAAK,QAAQ;AACb,SAAO,KAAK,WAAW,MAAM,UAAU,sBAAsB,kBAAkB;AACjF;AAIA,KAAK,kBAAkB,SAAS,wBAAwB,UAAU,QAAQ,SAAS;AACjF,MAAI,WAAW,KAAK,OAAO,WAAW,KAAK,UAAU;AACrD,MAAI,KAAK,aAAa,OAAO,KAAK,KAAK,UAAU;AAC/C,WAAO,KAAK,WAAW,OAAO;AAC9B,eAAW;AAAA,EACb,WAAW,KAAK,KAAK,QAAQ;AAC3B,QAAI,OAAO,KAAK,UAAU,GAAG,SAAS,KAAK,SAAS,QAAQ;AAC5D,SAAK,WAAW,KAAK;AACrB,SAAK,SAAS;AACd,SAAK,KAAK;AACV,SAAK,WAAW,KAAK,gBAAgB,MAAM,MAAM,QAAQ,OAAO;AAChE,SAAK,sBAAsB,wBAAwB,IAAI;AACvD,QAAI,QAAQ;AAAE,WAAK,gBAAgB,KAAK,QAAQ;AAAA,IAAG,WAC1C,KAAK,UAAU,KAAK,aAAa,YAAY,sBAAsB,KAAK,QAAQ,GACvF;AAAE,WAAK,iBAAiB,KAAK,OAAO,wCAAwC;AAAA,IAAG,WACxE,KAAK,aAAa,YAAY,qBAAqB,KAAK,QAAQ,GACvE;AAAE,WAAK,iBAAiB,KAAK,OAAO,mCAAmC;AAAA,IAAG,OACvE;AAAE,iBAAW;AAAA,IAAM;AACxB,WAAO,KAAK,WAAW,MAAM,SAAS,qBAAqB,iBAAiB;AAAA,EAC9E,WAAW,CAAC,YAAY,KAAK,SAAS,QAAQ,WAAW;AACvD,SAAK,WAAW,KAAK,iBAAiB,WAAW,MAAM,KAAK,QAAQ,oBAAoB;AAAE,WAAK,WAAW;AAAA,IAAG;AAC7G,WAAO,KAAK,kBAAkB;AAE9B,QAAI,KAAK,SAAS,QAAQ,KAAK;AAAE,WAAK,WAAW;AAAA,IAAG;AAAA,EACtD,OAAO;AACL,WAAO,KAAK,oBAAoB,wBAAwB,OAAO;AAC/D,QAAI,KAAK,sBAAsB,sBAAsB,GAAG;AAAE,aAAO;AAAA,IAAK;AACtE,WAAO,KAAK,KAAK,WAAW,CAAC,KAAK,mBAAmB,GAAG;AACtD,UAAI,SAAS,KAAK,YAAY,UAAU,QAAQ;AAChD,aAAO,WAAW,KAAK;AACvB,aAAO,SAAS;AAChB,aAAO,WAAW;AAClB,WAAK,gBAAgB,IAAI;AACzB,WAAK,KAAK;AACV,aAAO,KAAK,WAAW,QAAQ,kBAAkB;AAAA,IACnD;AAAA,EACF;AAEA,MAAI,CAAC,UAAU,KAAK,IAAI,QAAQ,QAAQ,GAAG;AACzC,QAAI,UACF;AAAE,WAAK,WAAW,KAAK,YAAY;AAAA,IAAG,OAEtC;AAAE,aAAO,KAAK,YAAY,UAAU,UAAU,MAAM,KAAK,gBAAgB,MAAM,OAAO,OAAO,OAAO,GAAG,MAAM,KAAK;AAAA,IAAE;AAAA,EACxH,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,sBAAsB,MAAM;AACnC,SACE,KAAK,SAAS,gBACd,KAAK,SAAS,6BAA6B,sBAAsB,KAAK,UAAU;AAEpF;AAEA,SAAS,qBAAqB,MAAM;AAClC,SACE,KAAK,SAAS,sBAAsB,KAAK,SAAS,SAAS,uBAC3D,KAAK,SAAS,qBAAqB,qBAAqB,KAAK,UAAU,KACvE,KAAK,SAAS,6BAA6B,qBAAqB,KAAK,UAAU;AAEnF;AAIA,KAAK,sBAAsB,SAAS,wBAAwB,SAAS;AACnE,MAAI,WAAW,KAAK,OAAO,WAAW,KAAK;AAC3C,MAAI,OAAO,KAAK,cAAc,wBAAwB,OAAO;AAC7D,MAAI,KAAK,SAAS,6BAA6B,KAAK,MAAM,MAAM,KAAK,cAAc,KAAK,UAAU,MAAM,KACtG;AAAE,WAAO;AAAA,EAAK;AAChB,MAAI,SAAS,KAAK,gBAAgB,MAAM,UAAU,UAAU,OAAO,OAAO;AAC1E,MAAI,0BAA0B,OAAO,SAAS,oBAAoB;AAChE,QAAI,uBAAuB,uBAAuB,OAAO,OAAO;AAAE,6BAAuB,sBAAsB;AAAA,IAAI;AACnH,QAAI,uBAAuB,qBAAqB,OAAO,OAAO;AAAE,6BAAuB,oBAAoB;AAAA,IAAI;AAC/G,QAAI,uBAAuB,iBAAiB,OAAO,OAAO;AAAE,6BAAuB,gBAAgB;AAAA,IAAI;AAAA,EACzG;AACA,SAAO;AACT;AAEA,KAAK,kBAAkB,SAAS,MAAM,UAAU,UAAU,SAAS,SAAS;AAC1E,MAAI,kBAAkB,KAAK,QAAQ,eAAe,KAAK,KAAK,SAAS,gBAAgB,KAAK,SAAS,WAC/F,KAAK,eAAe,KAAK,OAAO,CAAC,KAAK,mBAAmB,KAAK,KAAK,MAAM,KAAK,UAAU,KACxF,KAAK,qBAAqB,KAAK;AACnC,MAAI,kBAAkB;AAEtB,SAAO,MAAM;AACX,QAAI,UAAU,KAAK,eAAe,MAAM,UAAU,UAAU,SAAS,iBAAiB,iBAAiB,OAAO;AAE9G,QAAI,QAAQ,UAAU;AAAE,wBAAkB;AAAA,IAAM;AAChD,QAAI,YAAY,QAAQ,QAAQ,SAAS,2BAA2B;AAClE,UAAI,iBAAiB;AACnB,YAAI,YAAY,KAAK,YAAY,UAAU,QAAQ;AACnD,kBAAU,aAAa;AACvB,kBAAU,KAAK,WAAW,WAAW,iBAAiB;AAAA,MACxD;AACA,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AACF;AAEA,KAAK,wBAAwB,WAAW;AACtC,SAAO,CAAC,KAAK,mBAAmB,KAAK,KAAK,IAAI,QAAQ,KAAK;AAC7D;AAEA,KAAK,2BAA2B,SAAS,UAAU,UAAU,UAAU,SAAS;AAC9E,SAAO,KAAK,qBAAqB,KAAK,YAAY,UAAU,QAAQ,GAAG,UAAU,MAAM,OAAO;AAChG;AAEA,KAAK,iBAAiB,SAAS,MAAM,UAAU,UAAU,SAAS,iBAAiB,iBAAiB,SAAS;AAC3G,MAAI,oBAAoB,KAAK,QAAQ,eAAe;AACpD,MAAI,WAAW,qBAAqB,KAAK,IAAI,QAAQ,WAAW;AAChE,MAAI,WAAW,UAAU;AAAE,SAAK,MAAM,KAAK,cAAc,kEAAkE;AAAA,EAAG;AAE9H,MAAI,WAAW,KAAK,IAAI,QAAQ,QAAQ;AACxC,MAAI,YAAa,YAAY,KAAK,SAAS,QAAQ,UAAU,KAAK,SAAS,QAAQ,aAAc,KAAK,IAAI,QAAQ,GAAG,GAAG;AACtH,QAAI,OAAO,KAAK,YAAY,UAAU,QAAQ;AAC9C,SAAK,SAAS;AACd,QAAI,UAAU;AACZ,WAAK,WAAW,KAAK,gBAAgB;AACrC,WAAK,OAAO,QAAQ,QAAQ;AAAA,IAC9B,WAAW,KAAK,SAAS,QAAQ,aAAa,KAAK,SAAS,SAAS;AACnE,WAAK,WAAW,KAAK,kBAAkB;AAAA,IACzC,OAAO;AACL,WAAK,WAAW,KAAK,WAAW,KAAK,QAAQ,kBAAkB,OAAO;AAAA,IACxE;AACA,SAAK,WAAW,CAAC,CAAC;AAClB,QAAI,mBAAmB;AACrB,WAAK,WAAW;AAAA,IAClB;AACA,WAAO,KAAK,WAAW,MAAM,kBAAkB;AAAA,EACjD,WAAW,CAAC,WAAW,KAAK,IAAI,QAAQ,MAAM,GAAG;AAC/C,QAAI,yBAAyB,IAAI,uBAAqB,cAAc,KAAK,UAAU,cAAc,KAAK,UAAU,mBAAmB,KAAK;AACxI,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,gBAAgB;AACrB,QAAI,WAAW,KAAK,cAAc,QAAQ,QAAQ,KAAK,QAAQ,eAAe,GAAG,OAAO,sBAAsB;AAC9G,QAAI,mBAAmB,CAAC,YAAY,KAAK,sBAAsB,GAAG;AAChE,WAAK,mBAAmB,wBAAwB,KAAK;AACrD,WAAK,+BAA+B;AACpC,UAAI,KAAK,gBAAgB,GACvB;AAAE,aAAK,MAAM,KAAK,eAAe,2DAA2D;AAAA,MAAG;AACjG,WAAK,WAAW;AAChB,WAAK,WAAW;AAChB,WAAK,gBAAgB;AACrB,aAAO,KAAK,yBAAyB,UAAU,UAAU,UAAU,OAAO;AAAA,IAC5E;AACA,SAAK,sBAAsB,wBAAwB,IAAI;AACvD,SAAK,WAAW,eAAe,KAAK;AACpC,SAAK,WAAW,eAAe,KAAK;AACpC,SAAK,gBAAgB,oBAAoB,KAAK;AAC9C,QAAI,SAAS,KAAK,YAAY,UAAU,QAAQ;AAChD,WAAO,SAAS;AAChB,WAAO,YAAY;AACnB,QAAI,mBAAmB;AACrB,aAAO,WAAW;AAAA,IACpB;AACA,WAAO,KAAK,WAAW,QAAQ,gBAAgB;AAAA,EACjD,WAAW,KAAK,SAAS,QAAQ,WAAW;AAC1C,QAAI,YAAY,iBAAiB;AAC/B,WAAK,MAAM,KAAK,OAAO,2EAA2E;AAAA,IACpG;AACA,QAAI,SAAS,KAAK,YAAY,UAAU,QAAQ;AAChD,WAAO,MAAM;AACb,WAAO,QAAQ,KAAK,cAAc,EAAC,UAAU,KAAI,CAAC;AAClD,WAAO,KAAK,WAAW,QAAQ,0BAA0B;AAAA,EAC3D;AACA,SAAO;AACT;AAOA,KAAK,gBAAgB,SAAS,wBAAwB,SAAS,QAAQ;AAGrE,MAAI,KAAK,SAAS,QAAQ,OAAO;AAAE,SAAK,WAAW;AAAA,EAAG;AAEtD,MAAI,MAAM,aAAa,KAAK,qBAAqB,KAAK;AACtD,UAAQ,KAAK,MAAM;AAAA,IACnB,KAAK,QAAQ;AACX,UAAI,CAAC,KAAK,YACR;AAAE,aAAK,MAAM,KAAK,OAAO,kCAAkC;AAAA,MAAG;AAChE,aAAO,KAAK,UAAU;AACtB,WAAK,KAAK;AACV,UAAI,KAAK,SAAS,QAAQ,UAAU,CAAC,KAAK,kBACxC;AAAE,aAAK,MAAM,KAAK,OAAO,gDAAgD;AAAA,MAAG;AAO9E,UAAI,KAAK,SAAS,QAAQ,OAAO,KAAK,SAAS,QAAQ,YAAY,KAAK,SAAS,QAAQ,QACvF;AAAE,aAAK,WAAW;AAAA,MAAG;AACvB,aAAO,KAAK,WAAW,MAAM,OAAO;AAAA,IAEtC,KAAK,QAAQ;AACX,aAAO,KAAK,UAAU;AACtB,WAAK,KAAK;AACV,aAAO,KAAK,WAAW,MAAM,gBAAgB;AAAA,IAE/C,KAAK,QAAQ;AACX,UAAI,WAAW,KAAK,OAAO,WAAW,KAAK,UAAU,cAAc,KAAK;AACxE,UAAI,KAAK,KAAK,WAAW,KAAK;AAC9B,UAAI,KAAK,QAAQ,eAAe,KAAK,CAAC,eAAe,GAAG,SAAS,WAAW,CAAC,KAAK,mBAAmB,KAAK,KAAK,IAAI,QAAQ,SAAS,GAAG;AACrI,aAAK,gBAAgB,MAAM,MAAM;AACjC,eAAO,KAAK,cAAc,KAAK,YAAY,UAAU,QAAQ,GAAG,GAAG,OAAO,MAAM,OAAO;AAAA,MACzF;AACA,UAAI,cAAc,CAAC,KAAK,mBAAmB,GAAG;AAC5C,YAAI,KAAK,IAAI,QAAQ,KAAK,GACxB;AAAE,iBAAO,KAAK,qBAAqB,KAAK,YAAY,UAAU,QAAQ,GAAG,CAAC,EAAE,GAAG,OAAO,OAAO;AAAA,QAAE;AACjG,YAAI,KAAK,QAAQ,eAAe,KAAK,GAAG,SAAS,WAAW,KAAK,SAAS,QAAQ,QAAQ,CAAC,gBACtF,CAAC,KAAK,4BAA4B,KAAK,UAAU,QAAQ,KAAK,cAAc;AAC/E,eAAK,KAAK,WAAW,KAAK;AAC1B,cAAI,KAAK,mBAAmB,KAAK,CAAC,KAAK,IAAI,QAAQ,KAAK,GACtD;AAAE,iBAAK,WAAW;AAAA,UAAG;AACvB,iBAAO,KAAK,qBAAqB,KAAK,YAAY,UAAU,QAAQ,GAAG,CAAC,EAAE,GAAG,MAAM,OAAO;AAAA,QAC5F;AAAA,MACF;AACA,aAAO;AAAA,IAET,KAAK,QAAQ;AACX,UAAI,QAAQ,KAAK;AACjB,aAAO,KAAK,aAAa,MAAM,KAAK;AACpC,WAAK,QAAQ,EAAC,SAAS,MAAM,SAAS,OAAO,MAAM,MAAK;AACxD,aAAO;AAAA,IAET,KAAK,QAAQ;AAAA,IAAK,KAAK,QAAQ;AAC7B,aAAO,KAAK,aAAa,KAAK,KAAK;AAAA,IAErC,KAAK,QAAQ;AAAA,IAAO,KAAK,QAAQ;AAAA,IAAO,KAAK,QAAQ;AACnD,aAAO,KAAK,UAAU;AACtB,WAAK,QAAQ,KAAK,SAAS,QAAQ,QAAQ,OAAO,KAAK,SAAS,QAAQ;AACxE,WAAK,MAAM,KAAK,KAAK;AACrB,WAAK,KAAK;AACV,aAAO,KAAK,WAAW,MAAM,SAAS;AAAA,IAExC,KAAK,QAAQ;AACX,UAAI,QAAQ,KAAK,OAAO,OAAO,KAAK,mCAAmC,YAAY,OAAO;AAC1F,UAAI,wBAAwB;AAC1B,YAAI,uBAAuB,sBAAsB,KAAK,CAAC,KAAK,qBAAqB,IAAI,GACnF;AAAE,iCAAuB,sBAAsB;AAAA,QAAO;AACxD,YAAI,uBAAuB,oBAAoB,GAC7C;AAAE,iCAAuB,oBAAoB;AAAA,QAAO;AAAA,MACxD;AACA,aAAO;AAAA,IAET,KAAK,QAAQ;AACX,aAAO,KAAK,UAAU;AACtB,WAAK,KAAK;AACV,WAAK,WAAW,KAAK,cAAc,QAAQ,UAAU,MAAM,MAAM,sBAAsB;AACvF,aAAO,KAAK,WAAW,MAAM,iBAAiB;AAAA,IAEhD,KAAK,QAAQ;AACX,WAAK,gBAAgB,MAAM,MAAM;AACjC,aAAO,KAAK,SAAS,OAAO,sBAAsB;AAAA,IAEpD,KAAK,QAAQ;AACX,aAAO,KAAK,UAAU;AACtB,WAAK,KAAK;AACV,aAAO,KAAK,cAAc,MAAM,CAAC;AAAA,IAEnC,KAAK,QAAQ;AACX,aAAO,KAAK,WAAW,KAAK,UAAU,GAAG,KAAK;AAAA,IAEhD,KAAK,QAAQ;AACX,aAAO,KAAK,SAAS;AAAA,IAEvB,KAAK,QAAQ;AACX,aAAO,KAAK,cAAc;AAAA,IAE5B,KAAK,QAAQ;AACX,UAAI,KAAK,QAAQ,eAAe,IAAI;AAClC,eAAO,KAAK,gBAAgB,MAAM;AAAA,MACpC,OAAO;AACL,eAAO,KAAK,WAAW;AAAA,MACzB;AAAA,IAEF;AACE,aAAO,KAAK,qBAAqB;AAAA,EACnC;AACF;AAEA,KAAK,uBAAuB,WAAW;AACrC,OAAK,WAAW;AAClB;AAEA,KAAK,kBAAkB,SAAS,QAAQ;AACtC,MAAI,OAAO,KAAK,UAAU;AAI1B,MAAI,KAAK,aAAa;AAAE,SAAK,iBAAiB,KAAK,OAAO,mCAAmC;AAAA,EAAG;AAChG,OAAK,KAAK;AAEV,MAAI,KAAK,SAAS,QAAQ,UAAU,CAAC,QAAQ;AAC3C,WAAO,KAAK,mBAAmB,IAAI;AAAA,EACrC,WAAW,KAAK,SAAS,QAAQ,KAAK;AACpC,QAAI,OAAO,KAAK,YAAY,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,KAAK;AAClE,SAAK,OAAO;AACZ,SAAK,OAAO,KAAK,WAAW,MAAM,YAAY;AAC9C,WAAO,KAAK,gBAAgB,IAAI;AAAA,EAClC,OAAO;AACL,SAAK,WAAW;AAAA,EAClB;AACF;AAEA,KAAK,qBAAqB,SAAS,MAAM;AACvC,OAAK,KAAK;AAGV,OAAK,SAAS,KAAK,iBAAiB;AAEpC,MAAI,KAAK,QAAQ,eAAe,IAAI;AAClC,QAAI,CAAC,KAAK,IAAI,QAAQ,MAAM,GAAG;AAC7B,WAAK,OAAO,QAAQ,KAAK;AACzB,UAAI,CAAC,KAAK,mBAAmB,QAAQ,MAAM,GAAG;AAC5C,aAAK,UAAU,KAAK,iBAAiB;AACrC,YAAI,CAAC,KAAK,IAAI,QAAQ,MAAM,GAAG;AAC7B,eAAK,OAAO,QAAQ,KAAK;AACzB,cAAI,CAAC,KAAK,mBAAmB,QAAQ,MAAM,GAAG;AAC5C,iBAAK,WAAW;AAAA,UAClB;AAAA,QACF;AAAA,MACF,OAAO;AACL,aAAK,UAAU;AAAA,MACjB;AAAA,IACF,OAAO;AACL,WAAK,UAAU;AAAA,IACjB;AAAA,EACF,OAAO;AAEL,QAAI,CAAC,KAAK,IAAI,QAAQ,MAAM,GAAG;AAC7B,UAAI,WAAW,KAAK;AACpB,UAAI,KAAK,IAAI,QAAQ,KAAK,KAAK,KAAK,IAAI,QAAQ,MAAM,GAAG;AACvD,aAAK,iBAAiB,UAAU,2CAA2C;AAAA,MAC7E,OAAO;AACL,aAAK,WAAW,QAAQ;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AAEA,SAAO,KAAK,WAAW,MAAM,kBAAkB;AACjD;AAEA,KAAK,kBAAkB,SAAS,MAAM;AACpC,OAAK,KAAK;AAEV,MAAI,cAAc,KAAK;AACvB,OAAK,WAAW,KAAK,WAAW,IAAI;AAEpC,MAAI,KAAK,SAAS,SAAS,QACzB;AAAE,SAAK,iBAAiB,KAAK,SAAS,OAAO,0DAA0D;AAAA,EAAG;AAC5G,MAAI,aACF;AAAE,SAAK,iBAAiB,KAAK,OAAO,mDAAmD;AAAA,EAAG;AAC5F,MAAI,KAAK,QAAQ,eAAe,YAAY,CAAC,KAAK,QAAQ,6BACxD;AAAE,SAAK,iBAAiB,KAAK,OAAO,2CAA2C;AAAA,EAAG;AAEpF,SAAO,KAAK,WAAW,MAAM,cAAc;AAC7C;AAEA,KAAK,eAAe,SAAS,OAAO;AAClC,MAAI,OAAO,KAAK,UAAU;AAC1B,OAAK,QAAQ;AACb,OAAK,MAAM,KAAK,MAAM,MAAM,KAAK,OAAO,KAAK,GAAG;AAChD,MAAI,KAAK,IAAI,WAAW,KAAK,IAAI,SAAS,CAAC,MAAM,KAC/C;AAAE,SAAK,SAAS,KAAK,SAAS,OAAO,KAAK,MAAM,SAAS,IAAI,KAAK,IAAI,MAAM,GAAG,EAAE,EAAE,QAAQ,MAAM,EAAE;AAAA,EAAG;AACxG,OAAK,KAAK;AACV,SAAO,KAAK,WAAW,MAAM,SAAS;AACxC;AAEA,KAAK,uBAAuB,WAAW;AACrC,OAAK,OAAO,QAAQ,MAAM;AAC1B,MAAI,MAAM,KAAK,gBAAgB;AAC/B,OAAK,OAAO,QAAQ,MAAM;AAC1B,SAAO;AACT;AAEA,KAAK,mBAAmB,SAAS,UAAU;AACzC,SAAO,CAAC,KAAK,mBAAmB;AAClC;AAEA,KAAK,qCAAqC,SAAS,YAAY,SAAS;AACtE,MAAI,WAAW,KAAK,OAAO,WAAW,KAAK,UAAU,KAAK,qBAAqB,KAAK,QAAQ,eAAe;AAC3G,MAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,SAAK,KAAK;AAEV,QAAI,gBAAgB,KAAK,OAAO,gBAAgB,KAAK;AACrD,QAAI,WAAW,CAAC,GAAG,QAAQ,MAAM,cAAc;AAC/C,QAAI,yBAAyB,IAAI,uBAAqB,cAAc,KAAK,UAAU,cAAc,KAAK,UAAU;AAChH,SAAK,WAAW;AAChB,SAAK,WAAW;AAEhB,WAAO,KAAK,SAAS,QAAQ,QAAQ;AACnC,cAAQ,QAAQ,QAAQ,KAAK,OAAO,QAAQ,KAAK;AACjD,UAAI,sBAAsB,KAAK,mBAAmB,QAAQ,QAAQ,IAAI,GAAG;AACvE,sBAAc;AACd;AAAA,MACF,WAAW,KAAK,SAAS,QAAQ,UAAU;AACzC,sBAAc,KAAK;AACnB,iBAAS,KAAK,KAAK,eAAe,KAAK,iBAAiB,CAAC,CAAC;AAC1D,YAAI,KAAK,SAAS,QAAQ,OAAO;AAC/B,eAAK;AAAA,YACH,KAAK;AAAA,YACL;AAAA,UACF;AAAA,QACF;AACA;AAAA,MACF,OAAO;AACL,iBAAS,KAAK,KAAK,iBAAiB,OAAO,wBAAwB,KAAK,cAAc,CAAC;AAAA,MACzF;AAAA,IACF;AACA,QAAI,cAAc,KAAK,YAAY,cAAc,KAAK;AACtD,SAAK,OAAO,QAAQ,MAAM;AAE1B,QAAI,cAAc,KAAK,iBAAiB,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,GAAG;AAC5E,WAAK,mBAAmB,wBAAwB,KAAK;AACrD,WAAK,+BAA+B;AACpC,WAAK,WAAW;AAChB,WAAK,WAAW;AAChB,aAAO,KAAK,oBAAoB,UAAU,UAAU,UAAU,OAAO;AAAA,IACvE;AAEA,QAAI,CAAC,SAAS,UAAU,aAAa;AAAE,WAAK,WAAW,KAAK,YAAY;AAAA,IAAG;AAC3E,QAAI,aAAa;AAAE,WAAK,WAAW,WAAW;AAAA,IAAG;AACjD,SAAK,sBAAsB,wBAAwB,IAAI;AACvD,SAAK,WAAW,eAAe,KAAK;AACpC,SAAK,WAAW,eAAe,KAAK;AAEpC,QAAI,SAAS,SAAS,GAAG;AACvB,YAAM,KAAK,YAAY,eAAe,aAAa;AACnD,UAAI,cAAc;AAClB,WAAK,aAAa,KAAK,sBAAsB,aAAa,WAAW;AAAA,IACvE,OAAO;AACL,YAAM,SAAS,CAAC;AAAA,IAClB;AAAA,EACF,OAAO;AACL,UAAM,KAAK,qBAAqB;AAAA,EAClC;AAEA,MAAI,KAAK,QAAQ,gBAAgB;AAC/B,QAAI,MAAM,KAAK,YAAY,UAAU,QAAQ;AAC7C,QAAI,aAAa;AACjB,WAAO,KAAK,WAAW,KAAK,yBAAyB;AAAA,EACvD,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,KAAK,iBAAiB,SAAS,MAAM;AACnC,SAAO;AACT;AAEA,KAAK,sBAAsB,SAAS,UAAU,UAAU,UAAU,SAAS;AACzE,SAAO,KAAK,qBAAqB,KAAK,YAAY,UAAU,QAAQ,GAAG,UAAU,OAAO,OAAO;AACjG;AAQA,IAAI,QAAQ,CAAC;AAEb,KAAK,WAAW,WAAW;AACzB,MAAI,KAAK,aAAa;AAAE,SAAK,iBAAiB,KAAK,OAAO,gCAAgC;AAAA,EAAG;AAC7F,MAAI,OAAO,KAAK,UAAU;AAC1B,OAAK,KAAK;AACV,MAAI,KAAK,QAAQ,eAAe,KAAK,KAAK,SAAS,QAAQ,KAAK;AAC9D,QAAI,OAAO,KAAK,YAAY,KAAK,OAAO,KAAK,OAAO,KAAK,IAAI,KAAK;AAClE,SAAK,OAAO;AACZ,SAAK,OAAO,KAAK,WAAW,MAAM,YAAY;AAC9C,SAAK,KAAK;AACV,QAAI,cAAc,KAAK;AACvB,SAAK,WAAW,KAAK,WAAW,IAAI;AACpC,QAAI,KAAK,SAAS,SAAS,UACzB;AAAE,WAAK,iBAAiB,KAAK,SAAS,OAAO,sDAAsD;AAAA,IAAG;AACxG,QAAI,aACF;AAAE,WAAK,iBAAiB,KAAK,OAAO,kDAAkD;AAAA,IAAG;AAC3F,QAAI,CAAC,KAAK,mBACR;AAAE,WAAK,iBAAiB,KAAK,OAAO,mEAAmE;AAAA,IAAG;AAC5G,WAAO,KAAK,WAAW,MAAM,cAAc;AAAA,EAC7C;AACA,MAAI,WAAW,KAAK,OAAO,WAAW,KAAK;AAC3C,OAAK,SAAS,KAAK,gBAAgB,KAAK,cAAc,MAAM,OAAO,IAAI,GAAG,UAAU,UAAU,MAAM,KAAK;AACzG,MAAI,KAAK,IAAI,QAAQ,MAAM,GAAG;AAAE,SAAK,YAAY,KAAK,cAAc,QAAQ,QAAQ,KAAK,QAAQ,eAAe,GAAG,KAAK;AAAA,EAAG,OACtH;AAAE,SAAK,YAAY;AAAA,EAAO;AAC/B,SAAO,KAAK,WAAW,MAAM,eAAe;AAC9C;AAIA,KAAK,uBAAuB,SAASH,MAAK;AACxC,MAAI,WAAWA,KAAI;AAEnB,MAAI,OAAO,KAAK,UAAU;AAC1B,MAAI,KAAK,SAAS,QAAQ,iBAAiB;AACzC,QAAI,CAAC,UAAU;AACb,WAAK,iBAAiB,KAAK,OAAO,kDAAkD;AAAA,IACtF;AACA,SAAK,QAAQ;AAAA,MACX,KAAK,KAAK,MAAM,QAAQ,UAAU,IAAI;AAAA,MACtC,QAAQ;AAAA,IACV;AAAA,EACF,OAAO;AACL,SAAK,QAAQ;AAAA,MACX,KAAK,KAAK,MAAM,MAAM,KAAK,OAAO,KAAK,GAAG,EAAE,QAAQ,UAAU,IAAI;AAAA,MAClE,QAAQ,KAAK;AAAA,IACf;AAAA,EACF;AACA,OAAK,KAAK;AACV,OAAK,OAAO,KAAK,SAAS,QAAQ;AAClC,SAAO,KAAK,WAAW,MAAM,iBAAiB;AAChD;AAEA,KAAK,gBAAgB,SAASA,MAAK;AACjC,MAAKA,SAAQ,OAAS,CAAAA,OAAM,CAAC;AAC7B,MAAI,WAAWA,KAAI;AAAU,MAAK,aAAa,OAAS,YAAW;AAEnE,MAAI,OAAO,KAAK,UAAU;AAC1B,OAAK,KAAK;AACV,OAAK,cAAc,CAAC;AACpB,MAAI,SAAS,KAAK,qBAAqB,EAAC,SAAkB,CAAC;AAC3D,OAAK,SAAS,CAAC,MAAM;AACrB,SAAO,CAAC,OAAO,MAAM;AACnB,QAAI,KAAK,SAAS,QAAQ,KAAK;AAAE,WAAK,MAAM,KAAK,KAAK,+BAA+B;AAAA,IAAG;AACxF,SAAK,OAAO,QAAQ,YAAY;AAChC,SAAK,YAAY,KAAK,KAAK,gBAAgB,CAAC;AAC5C,SAAK,OAAO,QAAQ,MAAM;AAC1B,SAAK,OAAO,KAAK,SAAS,KAAK,qBAAqB,EAAC,SAAkB,CAAC,CAAC;AAAA,EAC3E;AACA,OAAK,KAAK;AACV,SAAO,KAAK,WAAW,MAAM,iBAAiB;AAChD;AAEA,KAAK,cAAc,SAAS,MAAM;AAChC,SAAO,CAAC,KAAK,YAAY,KAAK,IAAI,SAAS,gBAAgB,KAAK,IAAI,SAAS,YAC1E,KAAK,SAAS,QAAQ,QAAQ,KAAK,SAAS,QAAQ,OAAO,KAAK,SAAS,QAAQ,UAAU,KAAK,SAAS,QAAQ,YAAY,KAAK,KAAK,WAAY,KAAK,QAAQ,eAAe,KAAK,KAAK,SAAS,QAAQ,SAC3M,CAAC,UAAU,KAAK,KAAK,MAAM,MAAM,KAAK,YAAY,KAAK,KAAK,CAAC;AACjE;AAIA,KAAK,WAAW,SAAS,WAAW,wBAAwB;AAC1D,MAAI,OAAO,KAAK,UAAU,GAAG,QAAQ,MAAM,WAAW,CAAC;AACvD,OAAK,aAAa,CAAC;AACnB,OAAK,KAAK;AACV,SAAO,CAAC,KAAK,IAAI,QAAQ,MAAM,GAAG;AAChC,QAAI,CAAC,OAAO;AACV,WAAK,OAAO,QAAQ,KAAK;AACzB,UAAI,KAAK,QAAQ,eAAe,KAAK,KAAK,mBAAmB,QAAQ,MAAM,GAAG;AAAE;AAAA,MAAM;AAAA,IACxF,OAAO;AAAE,cAAQ;AAAA,IAAO;AAExB,QAAI,OAAO,KAAK,cAAc,WAAW,sBAAsB;AAC/D,QAAI,CAAC,WAAW;AAAE,WAAK,eAAe,MAAM,UAAU,sBAAsB;AAAA,IAAG;AAC/E,SAAK,WAAW,KAAK,IAAI;AAAA,EAC3B;AACA,SAAO,KAAK,WAAW,MAAM,YAAY,kBAAkB,kBAAkB;AAC/E;AAEA,KAAK,gBAAgB,SAAS,WAAW,wBAAwB;AAC/D,MAAI,OAAO,KAAK,UAAU,GAAG,aAAa,SAAS,UAAU;AAC7D,MAAI,KAAK,QAAQ,eAAe,KAAK,KAAK,IAAI,QAAQ,QAAQ,GAAG;AAC/D,QAAI,WAAW;AACb,WAAK,WAAW,KAAK,WAAW,KAAK;AACrC,UAAI,KAAK,SAAS,QAAQ,OAAO;AAC/B,aAAK,iBAAiB,KAAK,OAAO,+CAA+C;AAAA,MACnF;AACA,aAAO,KAAK,WAAW,MAAM,aAAa;AAAA,IAC5C;AAEA,SAAK,WAAW,KAAK,iBAAiB,OAAO,sBAAsB;AAEnE,QAAI,KAAK,SAAS,QAAQ,SAAS,0BAA0B,uBAAuB,gBAAgB,GAAG;AACrG,6BAAuB,gBAAgB,KAAK;AAAA,IAC9C;AAEA,WAAO,KAAK,WAAW,MAAM,eAAe;AAAA,EAC9C;AACA,MAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,QAAI,aAAa,wBAAwB;AACvC,iBAAW,KAAK;AAChB,iBAAW,KAAK;AAAA,IAClB;AACA,QAAI,CAAC,WACH;AAAE,oBAAc,KAAK,IAAI,QAAQ,IAAI;AAAA,IAAG;AAAA,EAC5C;AACA,MAAI,cAAc,KAAK;AACvB,OAAK,kBAAkB,IAAI;AAC3B,MAAI,CAAC,aAAa,CAAC,eAAe,KAAK,QAAQ,eAAe,KAAK,CAAC,eAAe,KAAK,YAAY,IAAI,GAAG;AACzG,cAAU;AACV,kBAAc,KAAK,QAAQ,eAAe,KAAK,KAAK,IAAI,QAAQ,IAAI;AACpE,SAAK,kBAAkB,IAAI;AAAA,EAC7B,OAAO;AACL,cAAU;AAAA,EACZ;AACA,OAAK,mBAAmB,MAAM,WAAW,aAAa,SAAS,UAAU,UAAU,wBAAwB,WAAW;AACtH,SAAO,KAAK,WAAW,MAAM,UAAU;AACzC;AAEA,KAAK,oBAAoB,SAAS,MAAM;AACtC,MAAI,OAAO,KAAK,IAAI;AACpB,OAAK,kBAAkB,IAAI;AAC3B,OAAK,QAAQ,KAAK,YAAY,KAAK;AACnC,OAAK,OAAO;AACZ,MAAI,aAAa,KAAK,SAAS,QAAQ,IAAI;AAC3C,MAAI,KAAK,MAAM,OAAO,WAAW,YAAY;AAC3C,QAAI,QAAQ,KAAK,MAAM;AACvB,QAAI,KAAK,SAAS,OAChB;AAAE,WAAK,iBAAiB,OAAO,8BAA8B;AAAA,IAAG,OAEhE;AAAE,WAAK,iBAAiB,OAAO,sCAAsC;AAAA,IAAG;AAAA,EAC5E,OAAO;AACL,QAAI,KAAK,SAAS,SAAS,KAAK,MAAM,OAAO,CAAC,EAAE,SAAS,eACvD;AAAE,WAAK,iBAAiB,KAAK,MAAM,OAAO,CAAC,EAAE,OAAO,+BAA+B;AAAA,IAAG;AAAA,EAC1F;AACF;AAEA,KAAK,qBAAqB,SAAS,MAAM,WAAW,aAAa,SAAS,UAAU,UAAU,wBAAwB,aAAa;AACjI,OAAK,eAAe,YAAY,KAAK,SAAS,QAAQ,OACpD;AAAE,SAAK,WAAW;AAAA,EAAG;AAEvB,MAAI,KAAK,IAAI,QAAQ,KAAK,GAAG;AAC3B,SAAK,QAAQ,YAAY,KAAK,kBAAkB,KAAK,OAAO,KAAK,QAAQ,IAAI,KAAK,iBAAiB,OAAO,sBAAsB;AAChI,SAAK,OAAO;AAAA,EACd,WAAW,KAAK,QAAQ,eAAe,KAAK,KAAK,SAAS,QAAQ,QAAQ;AACxE,QAAI,WAAW;AAAE,WAAK,WAAW;AAAA,IAAG;AACpC,SAAK,SAAS;AACd,SAAK,QAAQ,KAAK,YAAY,aAAa,OAAO;AAClD,SAAK,OAAO;AAAA,EACd,WAAW,CAAC,aAAa,CAAC,eACf,KAAK,QAAQ,eAAe,KAAK,CAAC,KAAK,YAAY,KAAK,IAAI,SAAS,iBACpE,KAAK,IAAI,SAAS,SAAS,KAAK,IAAI,SAAS,WAC7C,KAAK,SAAS,QAAQ,SAAS,KAAK,SAAS,QAAQ,UAAU,KAAK,SAAS,QAAQ,KAAK;AACpG,QAAI,eAAe,SAAS;AAAE,WAAK,WAAW;AAAA,IAAG;AACjD,SAAK,kBAAkB,IAAI;AAAA,EAC7B,WAAW,KAAK,QAAQ,eAAe,KAAK,CAAC,KAAK,YAAY,KAAK,IAAI,SAAS,cAAc;AAC5F,QAAI,eAAe,SAAS;AAAE,WAAK,WAAW;AAAA,IAAG;AACjD,SAAK,gBAAgB,KAAK,GAAG;AAC7B,QAAI,KAAK,IAAI,SAAS,WAAW,CAAC,KAAK,eACrC;AAAE,WAAK,gBAAgB;AAAA,IAAU;AACnC,QAAI,WAAW;AACb,WAAK,QAAQ,KAAK,kBAAkB,UAAU,UAAU,KAAK,SAAS,KAAK,GAAG,CAAC;AAAA,IACjF,WAAW,KAAK,SAAS,QAAQ,MAAM,wBAAwB;AAC7D,UAAI,uBAAuB,kBAAkB,GAC3C;AAAE,+BAAuB,kBAAkB,KAAK;AAAA,MAAO;AACzD,WAAK,QAAQ,KAAK,kBAAkB,UAAU,UAAU,KAAK,SAAS,KAAK,GAAG,CAAC;AAAA,IACjF,OAAO;AACL,WAAK,QAAQ,KAAK,SAAS,KAAK,GAAG;AAAA,IACrC;AACA,SAAK,OAAO;AACZ,SAAK,YAAY;AAAA,EACnB,OAAO;AAAE,SAAK,WAAW;AAAA,EAAG;AAC9B;AAEA,KAAK,oBAAoB,SAAS,MAAM;AACtC,MAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,QAAI,KAAK,IAAI,QAAQ,QAAQ,GAAG;AAC9B,WAAK,WAAW;AAChB,WAAK,MAAM,KAAK,iBAAiB;AACjC,WAAK,OAAO,QAAQ,QAAQ;AAC5B,aAAO,KAAK;AAAA,IACd,OAAO;AACL,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AACA,SAAO,KAAK,MAAM,KAAK,SAAS,QAAQ,OAAO,KAAK,SAAS,QAAQ,SAAS,KAAK,cAAc,IAAI,KAAK,WAAW,KAAK,QAAQ,kBAAkB,OAAO;AAC7J;AAIA,KAAK,eAAe,SAAS,MAAM;AACjC,OAAK,KAAK;AACV,MAAI,KAAK,QAAQ,eAAe,GAAG;AAAE,SAAK,YAAY,KAAK,aAAa;AAAA,EAAO;AAC/E,MAAI,KAAK,QAAQ,eAAe,GAAG;AAAE,SAAK,QAAQ;AAAA,EAAO;AAC3D;AAIA,KAAK,cAAc,SAAS,aAAa,SAAS,kBAAkB;AAClE,MAAI,OAAO,KAAK,UAAU,GAAG,cAAc,KAAK,UAAU,cAAc,KAAK,UAAU,mBAAmB,KAAK;AAE/G,OAAK,aAAa,IAAI;AACtB,MAAI,KAAK,QAAQ,eAAe,GAC9B;AAAE,SAAK,YAAY;AAAA,EAAa;AAClC,MAAI,KAAK,QAAQ,eAAe,GAC9B;AAAE,SAAK,QAAQ,CAAC,CAAC;AAAA,EAAS;AAE5B,OAAK,WAAW;AAChB,OAAK,WAAW;AAChB,OAAK,gBAAgB;AACrB,OAAK,WAAW,cAAc,SAAS,KAAK,SAAS,IAAI,eAAe,mBAAmB,qBAAqB,EAAE;AAElH,OAAK,OAAO,QAAQ,MAAM;AAC1B,OAAK,SAAS,KAAK,iBAAiB,QAAQ,QAAQ,OAAO,KAAK,QAAQ,eAAe,CAAC;AACxF,OAAK,+BAA+B;AACpC,OAAK,kBAAkB,MAAM,OAAO,MAAM,KAAK;AAE/C,OAAK,WAAW;AAChB,OAAK,WAAW;AAChB,OAAK,gBAAgB;AACrB,SAAO,KAAK,WAAW,MAAM,oBAAoB;AACnD;AAIA,KAAK,uBAAuB,SAAS,MAAM,QAAQ,SAAS,SAAS;AACnE,MAAI,cAAc,KAAK,UAAU,cAAc,KAAK,UAAU,mBAAmB,KAAK;AAEtF,OAAK,WAAW,cAAc,SAAS,KAAK,IAAI,WAAW;AAC3D,OAAK,aAAa,IAAI;AACtB,MAAI,KAAK,QAAQ,eAAe,GAAG;AAAE,SAAK,QAAQ,CAAC,CAAC;AAAA,EAAS;AAE7D,OAAK,WAAW;AAChB,OAAK,WAAW;AAChB,OAAK,gBAAgB;AAErB,OAAK,SAAS,KAAK,iBAAiB,QAAQ,IAAI;AAChD,OAAK,kBAAkB,MAAM,MAAM,OAAO,OAAO;AAEjD,OAAK,WAAW;AAChB,OAAK,WAAW;AAChB,OAAK,gBAAgB;AACrB,SAAO,KAAK,WAAW,MAAM,yBAAyB;AACxD;AAIA,KAAK,oBAAoB,SAAS,MAAM,iBAAiB,UAAU,SAAS;AAC1E,MAAI,eAAe,mBAAmB,KAAK,SAAS,QAAQ;AAC5D,MAAI,YAAY,KAAK,QAAQ,YAAY;AAEzC,MAAI,cAAc;AAChB,SAAK,OAAO,KAAK,iBAAiB,OAAO;AACzC,SAAK,aAAa;AAClB,SAAK,YAAY,MAAM,KAAK;AAAA,EAC9B,OAAO;AACL,QAAI,YAAY,KAAK,QAAQ,eAAe,KAAK,CAAC,KAAK,kBAAkB,KAAK,MAAM;AACpF,QAAI,CAAC,aAAa,WAAW;AAC3B,kBAAY,KAAK,gBAAgB,KAAK,GAAG;AAIzC,UAAI,aAAa,WACf;AAAE,aAAK,iBAAiB,KAAK,OAAO,2EAA2E;AAAA,MAAG;AAAA,IACtH;AAGA,QAAI,YAAY,KAAK;AACrB,SAAK,SAAS,CAAC;AACf,QAAI,WAAW;AAAE,WAAK,SAAS;AAAA,IAAM;AAIrC,SAAK,YAAY,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,mBAAmB,CAAC,YAAY,KAAK,kBAAkB,KAAK,MAAM,CAAC;AAEvH,QAAI,KAAK,UAAU,KAAK,IAAI;AAAE,WAAK,gBAAgB,KAAK,IAAI,YAAY;AAAA,IAAG;AAC3E,SAAK,OAAO,KAAK,WAAW,OAAO,QAAW,aAAa,CAAC,SAAS;AACrE,SAAK,aAAa;AAClB,SAAK,uBAAuB,KAAK,KAAK,IAAI;AAC1C,SAAK,SAAS;AAAA,EAChB;AACA,OAAK,UAAU;AACjB;AAEA,KAAK,oBAAoB,SAAS,QAAQ;AACxC,WAAS,IAAI,GAAG,OAAO,QAAQ,IAAI,KAAK,QAAQ,KAAK,GACnD;AACA,QAAI,QAAQ,KAAK,CAAC;AAElB,QAAI,MAAM,SAAS,cAAc;AAAE,aAAO;AAAA,IAC5C;AAAA,EAAE;AACF,SAAO;AACT;AAKA,KAAK,cAAc,SAAS,MAAM,iBAAiB;AACjD,MAAI,WAAW,uBAAO,OAAO,IAAI;AACjC,WAAS,IAAI,GAAG,OAAO,KAAK,QAAQ,IAAI,KAAK,QAAQ,KAAK,GACxD;AACA,QAAI,QAAQ,KAAK,CAAC;AAElB,SAAK,sBAAsB,OAAO,UAAU,kBAAkB,OAAO,QAAQ;AAAA,EAC/E;AACF;AAQA,KAAK,gBAAgB,SAAS,OAAO,oBAAoB,YAAY,wBAAwB;AAC3F,MAAI,OAAO,CAAC,GAAG,QAAQ;AACvB,SAAO,CAAC,KAAK,IAAI,KAAK,GAAG;AACvB,QAAI,CAAC,OAAO;AACV,WAAK,OAAO,QAAQ,KAAK;AACzB,UAAI,sBAAsB,KAAK,mBAAmB,KAAK,GAAG;AAAE;AAAA,MAAM;AAAA,IACpE,OAAO;AAAE,cAAQ;AAAA,IAAO;AAExB,QAAI,MAAO;AACX,QAAI,cAAc,KAAK,SAAS,QAAQ,OACtC;AAAE,YAAM;AAAA,IAAM,WACP,KAAK,SAAS,QAAQ,UAAU;AACvC,YAAM,KAAK,YAAY,sBAAsB;AAC7C,UAAI,0BAA0B,KAAK,SAAS,QAAQ,SAAS,uBAAuB,gBAAgB,GAClG;AAAE,+BAAuB,gBAAgB,KAAK;AAAA,MAAO;AAAA,IACzD,OAAO;AACL,YAAM,KAAK,iBAAiB,OAAO,sBAAsB;AAAA,IAC3D;AACA,SAAK,KAAK,GAAG;AAAA,EACf;AACA,SAAO;AACT;AAEA,KAAK,kBAAkB,SAASA,MAAK;AACnC,MAAI,QAAQA,KAAI;AAChB,MAAI,MAAMA,KAAI;AACd,MAAI,OAAOA,KAAI;AAEf,MAAI,KAAK,eAAe,SAAS,SAC/B;AAAE,SAAK,iBAAiB,OAAO,qDAAqD;AAAA,EAAG;AACzF,MAAI,KAAK,WAAW,SAAS,SAC3B;AAAE,SAAK,iBAAiB,OAAO,2DAA2D;AAAA,EAAG;AAC/F,MAAI,EAAE,KAAK,iBAAiB,EAAE,QAAQ,cAAc,SAAS,aAC3D;AAAE,SAAK,iBAAiB,OAAO,mDAAmD;AAAA,EAAG;AACvF,MAAI,KAAK,uBAAuB,SAAS,eAAe,SAAS,UAC/D;AAAE,SAAK,MAAM,OAAQ,gBAAgB,OAAO,uCAAwC;AAAA,EAAG;AACzF,MAAI,KAAK,SAAS,KAAK,IAAI,GACzB;AAAE,SAAK,MAAM,OAAQ,yBAAyB,OAAO,GAAI;AAAA,EAAG;AAC9D,MAAI,KAAK,QAAQ,cAAc,KAC7B,KAAK,MAAM,MAAM,OAAO,GAAG,EAAE,QAAQ,IAAI,MAAM,IAAI;AAAE;AAAA,EAAO;AAC9D,MAAI,KAAK,KAAK,SAAS,KAAK,sBAAsB,KAAK;AACvD,MAAI,GAAG,KAAK,IAAI,GAAG;AACjB,QAAI,CAAC,KAAK,WAAW,SAAS,SAC5B;AAAE,WAAK,iBAAiB,OAAO,sDAAsD;AAAA,IAAG;AAC1F,SAAK,iBAAiB,OAAQ,kBAAkB,OAAO,eAAgB;AAAA,EACzE;AACF;AAMA,KAAK,aAAa,SAAS,SAAS;AAClC,MAAI,OAAO,KAAK,eAAe;AAC/B,OAAK,KAAK,CAAC,CAAC,OAAO;AACnB,OAAK,WAAW,MAAM,YAAY;AAClC,MAAI,CAAC,SAAS;AACZ,SAAK,gBAAgB,IAAI;AACzB,QAAI,KAAK,SAAS,WAAW,CAAC,KAAK,eACjC;AAAE,WAAK,gBAAgB,KAAK;AAAA,IAAO;AAAA,EACvC;AACA,SAAO;AACT;AAEA,KAAK,iBAAiB,WAAW;AAC/B,MAAI,OAAO,KAAK,UAAU;AAC1B,MAAI,KAAK,SAAS,QAAQ,MAAM;AAC9B,SAAK,OAAO,KAAK;AAAA,EACnB,WAAW,KAAK,KAAK,SAAS;AAC5B,SAAK,OAAO,KAAK,KAAK;AAMtB,SAAK,KAAK,SAAS,WAAW,KAAK,SAAS,gBACzC,KAAK,eAAe,KAAK,eAAe,KAAK,KAAK,MAAM,WAAW,KAAK,YAAY,MAAM,KAAK;AAChG,WAAK,QAAQ,IAAI;AAAA,IACnB;AACA,SAAK,OAAO,QAAQ;AAAA,EACtB,OAAO;AACL,SAAK,WAAW;AAAA,EAClB;AACA,SAAO;AACT;AAEA,KAAK,oBAAoB,WAAW;AAClC,MAAI,OAAO,KAAK,UAAU;AAC1B,MAAI,KAAK,SAAS,QAAQ,WAAW;AACnC,SAAK,OAAO,KAAK;AAAA,EACnB,OAAO;AACL,SAAK,WAAW;AAAA,EAClB;AACA,OAAK,KAAK;AACV,OAAK,WAAW,MAAM,mBAAmB;AAGzC,MAAI,KAAK,QAAQ,oBAAoB;AACnC,QAAI,KAAK,iBAAiB,WAAW,GAAG;AACtC,WAAK,MAAM,KAAK,OAAQ,qBAAsB,KAAK,OAAQ,0CAA2C;AAAA,IACxG,OAAO;AACL,WAAK,iBAAiB,KAAK,iBAAiB,SAAS,CAAC,EAAE,KAAK,KAAK,IAAI;AAAA,IACxE;AAAA,EACF;AAEA,SAAO;AACT;AAIA,KAAK,aAAa,SAAS,SAAS;AAClC,MAAI,CAAC,KAAK,UAAU;AAAE,SAAK,WAAW,KAAK;AAAA,EAAO;AAElD,MAAI,OAAO,KAAK,UAAU;AAC1B,OAAK,KAAK;AACV,MAAI,KAAK,SAAS,QAAQ,QAAQ,KAAK,mBAAmB,KAAM,KAAK,SAAS,QAAQ,QAAQ,CAAC,KAAK,KAAK,YAAa;AACpH,SAAK,WAAW;AAChB,SAAK,WAAW;AAAA,EAClB,OAAO;AACL,SAAK,WAAW,KAAK,IAAI,QAAQ,IAAI;AACrC,SAAK,WAAW,KAAK,iBAAiB,OAAO;AAAA,EAC/C;AACA,SAAO,KAAK,WAAW,MAAM,iBAAiB;AAChD;AAEA,KAAK,aAAa,SAAS,SAAS;AAClC,MAAI,CAAC,KAAK,UAAU;AAAE,SAAK,WAAW,KAAK;AAAA,EAAO;AAElD,MAAI,OAAO,KAAK,UAAU;AAC1B,OAAK,KAAK;AACV,OAAK,WAAW,KAAK,gBAAgB,MAAM,MAAM,OAAO,OAAO;AAC/D,SAAO,KAAK,WAAW,MAAM,iBAAiB;AAChD;AAEA,IAAI,OAAO,OAAO;AAQlB,KAAK,QAAQ,SAAS,KAAK,SAAS;AAClC,MAAI,MAAM,YAAY,KAAK,OAAO,GAAG;AACrC,aAAW,OAAO,IAAI,OAAO,MAAM,IAAI,SAAS;AAChD,MAAI,KAAK,YAAY;AACnB,eAAW,SAAS,KAAK;AAAA,EAC3B;AACA,MAAI,MAAM,IAAI,YAAY,OAAO;AACjC,MAAI,MAAM;AAAK,MAAI,MAAM;AAAK,MAAI,WAAW,KAAK;AAClD,QAAM;AACR;AAEA,KAAK,mBAAmB,KAAK;AAE7B,KAAK,cAAc,WAAW;AAC5B,MAAI,KAAK,QAAQ,WAAW;AAC1B,WAAO,IAAI,SAAS,KAAK,SAAS,KAAK,MAAM,KAAK,SAAS;AAAA,EAC7D;AACF;AAEA,IAAI,OAAO,OAAO;AAElB,IAAI,QAAQ,SAASI,OAAM,OAAO;AAChC,OAAK,QAAQ;AAEb,OAAK,MAAM,CAAC;AAEZ,OAAK,UAAU,CAAC;AAEhB,OAAK,YAAY,CAAC;AACpB;AAIA,KAAK,aAAa,SAAS,OAAO;AAChC,OAAK,WAAW,KAAK,IAAI,MAAM,KAAK,CAAC;AACvC;AAEA,KAAK,YAAY,WAAW;AAC1B,OAAK,WAAW,IAAI;AACtB;AAKA,KAAK,6BAA6B,SAAS,OAAO;AAChD,SAAQ,MAAM,QAAQ,kBAAmB,CAAC,KAAK,YAAa,MAAM,QAAQ;AAC5E;AAEA,KAAK,cAAc,SAAS,MAAM,aAAa,KAAK;AAClD,MAAI,aAAa;AACjB,MAAI,gBAAgB,cAAc;AAChC,QAAI,QAAQ,KAAK,aAAa;AAC9B,iBAAa,MAAM,QAAQ,QAAQ,IAAI,IAAI,MAAM,MAAM,UAAU,QAAQ,IAAI,IAAI,MAAM,MAAM,IAAI,QAAQ,IAAI,IAAI;AACjH,UAAM,QAAQ,KAAK,IAAI;AACvB,QAAI,KAAK,YAAa,MAAM,QAAQ,WAClC;AAAE,aAAO,KAAK,iBAAiB,IAAI;AAAA,IAAG;AAAA,EAC1C,WAAW,gBAAgB,mBAAmB;AAC5C,QAAI,UAAU,KAAK,aAAa;AAChC,YAAQ,QAAQ,KAAK,IAAI;AAAA,EAC3B,WAAW,gBAAgB,eAAe;AACxC,QAAI,UAAU,KAAK,aAAa;AAChC,QAAI,KAAK,qBACP;AAAE,mBAAa,QAAQ,QAAQ,QAAQ,IAAI,IAAI;AAAA,IAAI,OAEnD;AAAE,mBAAa,QAAQ,QAAQ,QAAQ,IAAI,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,IAAI;AAAA,IAAI;AACvF,YAAQ,UAAU,KAAK,IAAI;AAAA,EAC7B,OAAO;AACL,aAAS,IAAI,KAAK,WAAW,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACpD,UAAI,UAAU,KAAK,WAAW,CAAC;AAC/B,UAAI,QAAQ,QAAQ,QAAQ,IAAI,IAAI,MAAM,EAAG,QAAQ,QAAQ,sBAAuB,QAAQ,QAAQ,CAAC,MAAM,SACvG,CAAC,KAAK,2BAA2B,OAAO,KAAK,QAAQ,UAAU,QAAQ,IAAI,IAAI,IAAI;AACrF,qBAAa;AACb;AAAA,MACF;AACA,cAAQ,IAAI,KAAK,IAAI;AACrB,UAAI,KAAK,YAAa,QAAQ,QAAQ,WACpC;AAAE,eAAO,KAAK,iBAAiB,IAAI;AAAA,MAAG;AACxC,UAAI,QAAQ,QAAQ,WAAW;AAAE;AAAA,MAAM;AAAA,IACzC;AAAA,EACF;AACA,MAAI,YAAY;AAAE,SAAK,iBAAiB,KAAM,iBAAiB,OAAO,6BAA8B;AAAA,EAAG;AACzG;AAEA,KAAK,mBAAmB,SAAS,IAAI;AAEnC,MAAI,KAAK,WAAW,CAAC,EAAE,QAAQ,QAAQ,GAAG,IAAI,MAAM,MAChD,KAAK,WAAW,CAAC,EAAE,IAAI,QAAQ,GAAG,IAAI,MAAM,IAAI;AAClD,SAAK,iBAAiB,GAAG,IAAI,IAAI;AAAA,EACnC;AACF;AAEA,KAAK,eAAe,WAAW;AAC7B,SAAO,KAAK,WAAW,KAAK,WAAW,SAAS,CAAC;AACnD;AAEA,KAAK,kBAAkB,WAAW;AAChC,WAAS,IAAI,KAAK,WAAW,SAAS,KAAI,KAAK;AAC7C,QAAI,QAAQ,KAAK,WAAW,CAAC;AAC7B,QAAI,MAAM,SAAS,YAAY,yBAAyB,2BAA2B;AAAE,aAAO;AAAA,IAAM;AAAA,EACpG;AACF;AAGA,KAAK,mBAAmB,WAAW;AACjC,WAAS,IAAI,KAAK,WAAW,SAAS,KAAI,KAAK;AAC7C,QAAI,QAAQ,KAAK,WAAW,CAAC;AAC7B,QAAI,MAAM,SAAS,YAAY,yBAAyB,6BACpD,EAAE,MAAM,QAAQ,cAAc;AAAE,aAAO;AAAA,IAAM;AAAA,EACnD;AACF;AAEA,IAAI,OAAO,SAASC,MAAK,QAAQ,KAAK,KAAK;AACzC,OAAK,OAAO;AACZ,OAAK,QAAQ;AACb,OAAK,MAAM;AACX,MAAI,OAAO,QAAQ,WACjB;AAAE,SAAK,MAAM,IAAI,eAAe,QAAQ,GAAG;AAAA,EAAG;AAChD,MAAI,OAAO,QAAQ,kBACjB;AAAE,SAAK,aAAa,OAAO,QAAQ;AAAA,EAAkB;AACvD,MAAI,OAAO,QAAQ,QACjB;AAAE,SAAK,QAAQ,CAAC,KAAK,CAAC;AAAA,EAAG;AAC7B;AAIA,IAAI,OAAO,OAAO;AAElB,KAAK,YAAY,WAAW;AAC1B,SAAO,IAAI,KAAK,MAAM,KAAK,OAAO,KAAK,QAAQ;AACjD;AAEA,KAAK,cAAc,SAAS,KAAK,KAAK;AACpC,SAAO,IAAI,KAAK,MAAM,KAAK,GAAG;AAChC;AAIA,SAAS,aAAa,MAAM,MAAM,KAAK,KAAK;AAC1C,OAAK,OAAO;AACZ,OAAK,MAAM;AACX,MAAI,KAAK,QAAQ,WACf;AAAE,SAAK,IAAI,MAAM;AAAA,EAAK;AACxB,MAAI,KAAK,QAAQ,QACf;AAAE,SAAK,MAAM,CAAC,IAAI;AAAA,EAAK;AACzB,SAAO;AACT;AAEA,KAAK,aAAa,SAAS,MAAM,MAAM;AACrC,SAAO,aAAa,KAAK,MAAM,MAAM,MAAM,KAAK,YAAY,KAAK,aAAa;AAChF;AAIA,KAAK,eAAe,SAAS,MAAM,MAAM,KAAK,KAAK;AACjD,SAAO,aAAa,KAAK,MAAM,MAAM,MAAM,KAAK,GAAG;AACrD;AAEA,KAAK,WAAW,SAAS,MAAM;AAC7B,MAAI,UAAU,IAAI,KAAK,MAAM,KAAK,OAAO,KAAK,QAAQ;AACtD,WAAS,QAAQ,MAAM;AAAE,YAAQ,IAAI,IAAI,KAAK,IAAI;AAAA,EAAG;AACrD,SAAO;AACT;AAGA,IAAI,6BAA6B;AAOjC,IAAI,wBAAwB;AAC5B,IAAI,yBAAyB,wBAAwB;AACrD,IAAI,yBAAyB;AAC7B,IAAI,yBAAyB,yBAAyB;AACtD,IAAI,yBAAyB;AAC7B,IAAI,yBAAyB;AAE7B,IAAI,0BAA0B;AAAA,EAC5B,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACN;AAGA,IAAI,kCAAkC;AAEtC,IAAI,mCAAmC;AAAA,EACrC,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACN;AAGA,IAAI,+BAA+B;AAGnC,IAAI,oBAAoB;AACxB,IAAI,qBAAqB,oBAAoB;AAC7C,IAAI,qBAAqB,qBAAqB;AAC9C,IAAI,qBAAqB,qBAAqB;AAC9C,IAAI,qBAAqB,qBAAqB;AAC9C,IAAI,qBAAqB,qBAAqB,MAAM;AAEpD,IAAI,sBAAsB;AAAA,EACxB,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACN;AAEA,IAAI,OAAO,CAAC;AACZ,SAAS,iBAAiB,aAAa;AACrC,MAAI,IAAI,KAAK,WAAW,IAAI;AAAA,IAC1B,QAAQ,YAAY,wBAAwB,WAAW,IAAI,MAAM,4BAA4B;AAAA,IAC7F,iBAAiB,YAAY,iCAAiC,WAAW,CAAC;AAAA,IAC1E,WAAW;AAAA,MACT,kBAAkB,YAAY,4BAA4B;AAAA,MAC1D,QAAQ,YAAY,oBAAoB,WAAW,CAAC;AAAA,IACtD;AAAA,EACF;AACA,IAAE,UAAU,oBAAoB,EAAE,UAAU;AAE5C,IAAE,UAAU,KAAK,EAAE,UAAU;AAC7B,IAAE,UAAU,KAAK,EAAE,UAAU;AAC7B,IAAE,UAAU,MAAM,EAAE,UAAU;AAChC;AAEA,KAAS,IAAI,GAAG,OAAO,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACnE,gBAAc,KAAK,CAAC;AAExB,mBAAiB,WAAW;AAC9B;AAHM;AADG;AAAO;AAMhB,IAAI,OAAO,OAAO;AAIlB,IAAI,WAAW,SAASC,UAAS,QAAQ,MAAM;AAE7C,OAAK,SAAS;AAEd,OAAK,OAAO,QAAQ;AACtB;AAEA,SAAS,UAAU,gBAAgB,SAAS,cAAe,KAAK;AAG9D,WAAS,OAAO,MAAM,MAAM,OAAO,KAAK,QAAQ;AAC9C,aAAS,QAAQ,KAAK,OAAO,QAAQ,MAAM,QAAQ;AACjD,UAAI,KAAK,SAAS,MAAM,QAAQ,SAAS,OAAO;AAAE,eAAO;AAAA,MAAK;AAAA,IAChE;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,UAAU,UAAU,SAAS,UAAW;AAC/C,SAAO,IAAI,SAAS,KAAK,QAAQ,KAAK,IAAI;AAC5C;AAEA,IAAI,wBAAwB,SAASC,uBAAsB,QAAQ;AACjE,OAAK,SAAS;AACd,OAAK,aAAa,SAAS,OAAO,QAAQ,eAAe,IAAI,OAAO,OAAO,OAAO,QAAQ,eAAe,IAAI,MAAM,OAAO,OAAO,QAAQ,eAAe,KAAK,MAAM,OAAO,OAAO,QAAQ,eAAe,KAAK,MAAM;AACnN,OAAK,oBAAoB,KAAK,OAAO,QAAQ,eAAe,KAAK,KAAK,OAAO,QAAQ,WAAW;AAChG,OAAK,SAAS;AACd,OAAK,QAAQ;AACb,OAAK,QAAQ;AACb,OAAK,UAAU;AACf,OAAK,UAAU;AACf,OAAK,UAAU;AACf,OAAK,MAAM;AACX,OAAK,eAAe;AACpB,OAAK,kBAAkB;AACvB,OAAK,8BAA8B;AACnC,OAAK,qBAAqB;AAC1B,OAAK,mBAAmB;AACxB,OAAK,aAAa,uBAAO,OAAO,IAAI;AACpC,OAAK,qBAAqB,CAAC;AAC3B,OAAK,WAAW;AAClB;AAEA,sBAAsB,UAAU,QAAQ,SAAS,MAAO,OAAO,SAAS,OAAO;AAC7E,MAAI,cAAc,MAAM,QAAQ,GAAG,MAAM;AACzC,MAAI,UAAU,MAAM,QAAQ,GAAG,MAAM;AACrC,OAAK,QAAQ,QAAQ;AACrB,OAAK,SAAS,UAAU;AACxB,OAAK,QAAQ;AACb,MAAI,eAAe,KAAK,OAAO,QAAQ,eAAe,IAAI;AACxD,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,EACjB,OAAO;AACL,SAAK,UAAU,WAAW,KAAK,OAAO,QAAQ,eAAe;AAC7D,SAAK,UAAU;AACf,SAAK,UAAU,WAAW,KAAK,OAAO,QAAQ,eAAe;AAAA,EAC/D;AACF;AAEA,sBAAsB,UAAU,QAAQ,SAAS,MAAO,SAAS;AAC/D,OAAK,OAAO,iBAAiB,KAAK,OAAQ,kCAAmC,KAAK,SAAU,QAAQ,OAAQ;AAC9G;AAIA,sBAAsB,UAAU,KAAK,SAAS,GAAI,GAAG,QAAQ;AACzD,MAAK,WAAW,OAAS,UAAS;AAEpC,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,EAAE;AACV,MAAI,KAAK,GAAG;AACV,WAAO;AAAA,EACT;AACA,MAAI,IAAI,EAAE,WAAW,CAAC;AACtB,MAAI,EAAE,UAAU,KAAK,YAAY,KAAK,SAAU,KAAK,SAAU,IAAI,KAAK,GAAG;AACzE,WAAO;AAAA,EACT;AACA,MAAI,OAAO,EAAE,WAAW,IAAI,CAAC;AAC7B,SAAO,QAAQ,SAAU,QAAQ,SAAU,KAAK,MAAM,OAAO,WAAY;AAC3E;AAEA,sBAAsB,UAAU,YAAY,SAAS,UAAW,GAAG,QAAQ;AACvE,MAAK,WAAW,OAAS,UAAS;AAEpC,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,EAAE;AACV,MAAI,KAAK,GAAG;AACV,WAAO;AAAA,EACT;AACA,MAAI,IAAI,EAAE,WAAW,CAAC,GAAG;AACzB,MAAI,EAAE,UAAU,KAAK,YAAY,KAAK,SAAU,KAAK,SAAU,IAAI,KAAK,MACnE,OAAO,EAAE,WAAW,IAAI,CAAC,KAAK,SAAU,OAAO,OAAQ;AAC1D,WAAO,IAAI;AAAA,EACb;AACA,SAAO,IAAI;AACb;AAEA,sBAAsB,UAAU,UAAU,SAAS,QAAS,QAAQ;AAChE,MAAK,WAAW,OAAS,UAAS;AAEpC,SAAO,KAAK,GAAG,KAAK,KAAK,MAAM;AACjC;AAEA,sBAAsB,UAAU,YAAY,SAAS,UAAW,QAAQ;AACpE,MAAK,WAAW,OAAS,UAAS;AAEpC,SAAO,KAAK,GAAG,KAAK,UAAU,KAAK,KAAK,MAAM,GAAG,MAAM;AACzD;AAEA,sBAAsB,UAAU,UAAU,SAAS,QAAS,QAAQ;AAChE,MAAK,WAAW,OAAS,UAAS;AAEpC,OAAK,MAAM,KAAK,UAAU,KAAK,KAAK,MAAM;AAC5C;AAEA,sBAAsB,UAAU,MAAM,SAAS,IAAK,IAAI,QAAQ;AAC5D,MAAK,WAAW,OAAS,UAAS;AAEpC,MAAI,KAAK,QAAQ,MAAM,MAAM,IAAI;AAC/B,SAAK,QAAQ,MAAM;AACnB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,sBAAsB,UAAU,WAAW,SAAS,SAAU,KAAK,QAAQ;AACvE,MAAK,WAAW,OAAS,UAAS;AAEpC,MAAI,MAAM,KAAK;AACf,WAAS,IAAI,GAAG,OAAO,KAAK,IAAI,KAAK,QAAQ,KAAK,GAAG;AACnD,QAAI,KAAK,KAAK,CAAC;AAEb,QAAIC,WAAU,KAAK,GAAG,KAAK,MAAM;AACnC,QAAIA,aAAY,MAAMA,aAAY,IAAI;AACpC,aAAO;AAAA,IACT;AACA,UAAM,KAAK,UAAU,KAAK,MAAM;AAAA,EAClC;AACA,OAAK,MAAM;AACX,SAAO;AACT;AAQA,KAAK,sBAAsB,SAAS,OAAO;AACzC,MAAI,aAAa,MAAM;AACvB,MAAI,QAAQ,MAAM;AAElB,MAAI,IAAI;AACR,MAAI,IAAI;AAER,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,OAAO,MAAM,OAAO,CAAC;AACzB,QAAI,WAAW,QAAQ,IAAI,MAAM,IAAI;AACnC,WAAK,MAAM,MAAM,OAAO,iCAAiC;AAAA,IAC3D;AACA,QAAI,MAAM,QAAQ,MAAM,IAAI,CAAC,IAAI,IAAI;AACnC,WAAK,MAAM,MAAM,OAAO,mCAAmC;AAAA,IAC7D;AACA,QAAI,SAAS,KAAK;AAAE,UAAI;AAAA,IAAM;AAC9B,QAAI,SAAS,KAAK;AAAE,UAAI;AAAA,IAAM;AAAA,EAChC;AACA,MAAI,KAAK,QAAQ,eAAe,MAAM,KAAK,GAAG;AAC5C,SAAK,MAAM,MAAM,OAAO,iCAAiC;AAAA,EAC3D;AACF;AAEA,SAAS,QAAQ,KAAK;AACpB,WAAS,KAAK,KAAK;AAAE,WAAO;AAAA,EAAK;AACjC,SAAO;AACT;AAQA,KAAK,wBAAwB,SAAS,OAAO;AAC3C,OAAK,eAAe,KAAK;AAOzB,MAAI,CAAC,MAAM,WAAW,KAAK,QAAQ,eAAe,KAAK,QAAQ,MAAM,UAAU,GAAG;AAChF,UAAM,UAAU;AAChB,SAAK,eAAe,KAAK;AAAA,EAC3B;AACF;AAGA,KAAK,iBAAiB,SAAS,OAAO;AACpC,QAAM,MAAM;AACZ,QAAM,eAAe;AACrB,QAAM,kBAAkB;AACxB,QAAM,8BAA8B;AACpC,QAAM,qBAAqB;AAC3B,QAAM,mBAAmB;AACzB,QAAM,aAAa,uBAAO,OAAO,IAAI;AACrC,QAAM,mBAAmB,SAAS;AAClC,QAAM,WAAW;AAEjB,OAAK,mBAAmB,KAAK;AAE7B,MAAI,MAAM,QAAQ,MAAM,OAAO,QAAQ;AAErC,QAAI,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY,GAAG;AAC3B,YAAM,MAAM,eAAe;AAAA,IAC7B;AACA,QAAI,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY,KAAK,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY,GAAG;AACtD,YAAM,MAAM,0BAA0B;AAAA,IACxC;AAAA,EACF;AACA,MAAI,MAAM,mBAAmB,MAAM,oBAAoB;AACrD,UAAM,MAAM,gBAAgB;AAAA,EAC9B;AACA,WAAS,IAAI,GAAG,OAAO,MAAM,oBAAoB,IAAI,KAAK,QAAQ,KAAK,GAAG;AACxE,QAAI,OAAO,KAAK,CAAC;AAEjB,QAAI,CAAC,MAAM,WAAW,IAAI,GAAG;AAC3B,YAAM,MAAM,kCAAkC;AAAA,IAChD;AAAA,EACF;AACF;AAGA,KAAK,qBAAqB,SAAS,OAAO;AACxC,MAAI,mBAAmB,KAAK,QAAQ,eAAe;AACnD,MAAI,kBAAkB;AAAE,UAAM,WAAW,IAAI,SAAS,MAAM,UAAU,IAAI;AAAA,EAAG;AAC7E,OAAK,mBAAmB,KAAK;AAC7B,SAAO,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AAC9B,QAAI,kBAAkB;AAAE,YAAM,WAAW,MAAM,SAAS,QAAQ;AAAA,IAAG;AACnE,SAAK,mBAAmB,KAAK;AAAA,EAC/B;AACA,MAAI,kBAAkB;AAAE,UAAM,WAAW,MAAM,SAAS;AAAA,EAAQ;AAGhE,MAAI,KAAK,qBAAqB,OAAO,IAAI,GAAG;AAC1C,UAAM,MAAM,mBAAmB;AAAA,EACjC;AACA,MAAI,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AAC3B,UAAM,MAAM,0BAA0B;AAAA,EACxC;AACF;AAGA,KAAK,qBAAqB,SAAS,OAAO;AACxC,SAAO,MAAM,MAAM,MAAM,OAAO,UAAU,KAAK,eAAe,KAAK,GAAG;AAAA,EAAC;AACzE;AAGA,KAAK,iBAAiB,SAAS,OAAO;AACpC,MAAI,KAAK,oBAAoB,KAAK,GAAG;AAInC,QAAI,MAAM,+BAA+B,KAAK,qBAAqB,KAAK,GAAG;AAEzE,UAAI,MAAM,SAAS;AACjB,cAAM,MAAM,oBAAoB;AAAA,MAClC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,UAAU,KAAK,eAAe,KAAK,IAAI,KAAK,uBAAuB,KAAK,GAAG;AACnF,SAAK,qBAAqB,KAAK;AAC/B,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAGA,KAAK,sBAAsB,SAAS,OAAO;AACzC,MAAI,QAAQ,MAAM;AAClB,QAAM,8BAA8B;AAGpC,MAAI,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,KAAK,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AACtD,WAAO;AAAA,EACT;AAGA,MAAI,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AAC3B,QAAI,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY,KAAK,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY,GAAG;AACtD,aAAO;AAAA,IACT;AACA,UAAM,MAAM;AAAA,EACd;AAGA,MAAI,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,KAAK,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AACtD,QAAI,aAAa;AACjB,QAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,mBAAa,MAAM;AAAA,QAAI;AAAA;AAAA,MAAY;AAAA,IACrC;AACA,QAAI,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY,KAAK,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY,GAAG;AACtD,WAAK,mBAAmB,KAAK;AAC7B,UAAI,CAAC,MAAM;AAAA,QAAI;AAAA;AAAA,MAAY,GAAG;AAC5B,cAAM,MAAM,oBAAoB;AAAA,MAClC;AACA,YAAM,8BAA8B,CAAC;AACrC,aAAO;AAAA,IACT;AAAA,EACF;AAEA,QAAM,MAAM;AACZ,SAAO;AACT;AAGA,KAAK,uBAAuB,SAAS,OAAO,SAAS;AACnD,MAAK,YAAY,OAAS,WAAU;AAEpC,MAAI,KAAK,2BAA2B,OAAO,OAAO,GAAG;AACnD,UAAM;AAAA,MAAI;AAAA;AAAA,IAAY;AACtB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAGA,KAAK,6BAA6B,SAAS,OAAO,SAAS;AACzD,SACE,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,KACtB,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,KACtB,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,KACtB,KAAK,2BAA2B,OAAO,OAAO;AAElD;AACA,KAAK,6BAA6B,SAAS,OAAO,SAAS;AACzD,MAAI,QAAQ,MAAM;AAClB,MAAI,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AAC3B,QAAI,MAAM,GAAG,MAAM;AACnB,QAAI,KAAK,wBAAwB,KAAK,GAAG;AACvC,YAAM,MAAM;AACZ,UAAI,MAAM;AAAA,QAAI;AAAA;AAAA,MAAY,KAAK,KAAK,wBAAwB,KAAK,GAAG;AAClE,cAAM,MAAM;AAAA,MACd;AACA,UAAI,MAAM;AAAA,QAAI;AAAA;AAAA,MAAY,GAAG;AAE3B,YAAI,QAAQ,MAAM,MAAM,OAAO,CAAC,SAAS;AACvC,gBAAM,MAAM,uCAAuC;AAAA,QACrD;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,MAAM,WAAW,CAAC,SAAS;AAC7B,YAAM,MAAM,uBAAuB;AAAA,IACrC;AACA,UAAM,MAAM;AAAA,EACd;AACA,SAAO;AACT;AAGA,KAAK,iBAAiB,SAAS,OAAO;AACpC,SACE,KAAK,4BAA4B,KAAK,KACtC,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,KACtB,KAAK,mCAAmC,KAAK,KAC7C,KAAK,yBAAyB,KAAK,KACnC,KAAK,2BAA2B,KAAK,KACrC,KAAK,yBAAyB,KAAK;AAEvC;AACA,KAAK,qCAAqC,SAAS,OAAO;AACxD,MAAI,QAAQ,MAAM;AAClB,MAAI,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AAC3B,QAAI,KAAK,qBAAqB,KAAK,GAAG;AACpC,aAAO;AAAA,IACT;AACA,UAAM,MAAM;AAAA,EACd;AACA,SAAO;AACT;AACA,KAAK,6BAA6B,SAAS,OAAO;AAChD,MAAI,QAAQ,MAAM;AAClB,MAAI,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AAC3B,QAAI,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY,GAAG;AAC3B,UAAI,KAAK,QAAQ,eAAe,IAAI;AAClC,YAAI,eAAe,KAAK,oBAAoB,KAAK;AACjD,YAAI,YAAY,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY;AACtC,YAAI,gBAAgB,WAAW;AAC7B,mBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,gBAAI,WAAW,aAAa,OAAO,CAAC;AACpC,gBAAI,aAAa,QAAQ,UAAU,IAAI,CAAC,IAAI,IAAI;AAC9C,oBAAM,MAAM,wCAAwC;AAAA,YACtD;AAAA,UACF;AACA,cAAI,WAAW;AACb,gBAAI,kBAAkB,KAAK,oBAAoB,KAAK;AACpD,gBAAI,CAAC,gBAAgB,CAAC,mBAAmB,MAAM,QAAQ,MAAM,IAAc;AACzE,oBAAM,MAAM,sCAAsC;AAAA,YACpD;AACA,qBAAS,MAAM,GAAG,MAAM,gBAAgB,QAAQ,OAAO;AACrD,kBAAI,aAAa,gBAAgB,OAAO,GAAG;AAC3C,kBACE,gBAAgB,QAAQ,YAAY,MAAM,CAAC,IAAI,MAC/C,aAAa,QAAQ,UAAU,IAAI,IACnC;AACA,sBAAM,MAAM,wCAAwC;AAAA,cACtD;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,MAAM;AAAA,QAAI;AAAA;AAAA,MAAY,GAAG;AAC3B,aAAK,mBAAmB,KAAK;AAC7B,YAAI,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,GAAG;AAC3B,iBAAO;AAAA,QACT;AACA,cAAM,MAAM,oBAAoB;AAAA,MAClC;AAAA,IACF;AACA,UAAM,MAAM;AAAA,EACd;AACA,SAAO;AACT;AACA,KAAK,2BAA2B,SAAS,OAAO;AAC9C,MAAI,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AAC3B,QAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,WAAK,sBAAsB,KAAK;AAAA,IAClC,WAAW,MAAM,QAAQ,MAAM,IAAc;AAC3C,YAAM,MAAM,eAAe;AAAA,IAC7B;AACA,SAAK,mBAAmB,KAAK;AAC7B,QAAI,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY,GAAG;AAC3B,YAAM,sBAAsB;AAC5B,aAAO;AAAA,IACT;AACA,UAAM,MAAM,oBAAoB;AAAA,EAClC;AACA,SAAO;AACT;AAIA,KAAK,sBAAsB,SAAS,OAAO;AACzC,MAAI,YAAY;AAChB,MAAI,KAAK;AACT,UAAQ,KAAK,MAAM,QAAQ,OAAO,MAAM,4BAA4B,EAAE,GAAG;AACvE,iBAAa,kBAAkB,EAAE;AACjC,UAAM,QAAQ;AAAA,EAChB;AACA,SAAO;AACT;AAGA,SAAS,4BAA4B,IAAI;AACvC,SAAO,OAAO,OAAgB,OAAO,OAAgB,OAAO;AAC9D;AAGA,KAAK,yBAAyB,SAAS,OAAO;AAC5C,SACE,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,KACtB,KAAK,mCAAmC,KAAK,KAC7C,KAAK,yBAAyB,KAAK,KACnC,KAAK,2BAA2B,KAAK,KACrC,KAAK,yBAAyB,KAAK,KACnC,KAAK,kCAAkC,KAAK,KAC5C,KAAK,mCAAmC,KAAK;AAEjD;AAGA,KAAK,oCAAoC,SAAS,OAAO;AACvD,MAAI,KAAK,2BAA2B,OAAO,IAAI,GAAG;AAChD,UAAM,MAAM,mBAAmB;AAAA,EACjC;AACA,SAAO;AACT;AAGA,KAAK,4BAA4B,SAAS,OAAO;AAC/C,MAAI,KAAK,MAAM,QAAQ;AACvB,MAAI,kBAAkB,EAAE,GAAG;AACzB,UAAM,eAAe;AACrB,UAAM,QAAQ;AACd,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,IAAI;AAC7B,SACE,OAAO,MACP,MAAM,MAAgB,MAAM,MAC5B,OAAO,MACP,OAAO,MACP,MAAM,MAAgB,MAAM,MAC5B,MAAM,OAAgB,MAAM;AAEhC;AAIA,KAAK,8BAA8B,SAAS,OAAO;AACjD,MAAI,QAAQ,MAAM;AAClB,MAAI,KAAK;AACT,UAAQ,KAAK,MAAM,QAAQ,OAAO,MAAM,CAAC,kBAAkB,EAAE,GAAG;AAC9D,UAAM,QAAQ;AAAA,EAChB;AACA,SAAO,MAAM,QAAQ;AACvB;AAGA,KAAK,qCAAqC,SAAS,OAAO;AACxD,MAAI,KAAK,MAAM,QAAQ;AACvB,MACE,OAAO,MACP,OAAO,MACP,EAAE,MAAM,MAAgB,MAAM,OAC9B,OAAO,MACP,OAAO,MACP,OAAO,MACP,OAAO,MACP,OAAO,KACP;AACA,UAAM,QAAQ;AACd,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAKA,KAAK,wBAAwB,SAAS,OAAO;AAC3C,MAAI,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AAC3B,QAAI,CAAC,KAAK,oBAAoB,KAAK,GAAG;AAAE,YAAM,MAAM,eAAe;AAAA,IAAG;AACtE,QAAI,mBAAmB,KAAK,QAAQ,eAAe;AACnD,QAAI,QAAQ,MAAM,WAAW,MAAM,eAAe;AAClD,QAAI,OAAO;AACT,UAAI,kBAAkB;AACpB,iBAAS,IAAI,GAAG,OAAO,OAAO,IAAI,KAAK,QAAQ,KAAK,GAAG;AACrD,cAAI,QAAQ,KAAK,CAAC;AAElB,cAAI,CAAC,MAAM,cAAc,MAAM,QAAQ,GACrC;AAAE,kBAAM,MAAM,8BAA8B;AAAA,UAAG;AAAA,QACnD;AAAA,MACF,OAAO;AACL,cAAM,MAAM,8BAA8B;AAAA,MAC5C;AAAA,IACF;AACA,QAAI,kBAAkB;AACpB,OAAC,UAAU,MAAM,WAAW,MAAM,eAAe,IAAI,CAAC,IAAI,KAAK,MAAM,QAAQ;AAAA,IAC/E,OAAO;AACL,YAAM,WAAW,MAAM,eAAe,IAAI;AAAA,IAC5C;AAAA,EACF;AACF;AAKA,KAAK,sBAAsB,SAAS,OAAO;AACzC,QAAM,kBAAkB;AACxB,MAAI,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AAC3B,QAAI,KAAK,+BAA+B,KAAK,KAAK,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY,GAAG;AACzE,aAAO;AAAA,IACT;AACA,UAAM,MAAM,4BAA4B;AAAA,EAC1C;AACA,SAAO;AACT;AAMA,KAAK,iCAAiC,SAAS,OAAO;AACpD,QAAM,kBAAkB;AACxB,MAAI,KAAK,gCAAgC,KAAK,GAAG;AAC/C,UAAM,mBAAmB,kBAAkB,MAAM,YAAY;AAC7D,WAAO,KAAK,+BAA+B,KAAK,GAAG;AACjD,YAAM,mBAAmB,kBAAkB,MAAM,YAAY;AAAA,IAC/D;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAOA,KAAK,kCAAkC,SAAS,OAAO;AACrD,MAAI,QAAQ,MAAM;AAClB,MAAI,SAAS,KAAK,QAAQ,eAAe;AACzC,MAAI,KAAK,MAAM,QAAQ,MAAM;AAC7B,QAAM,QAAQ,MAAM;AAEpB,MAAI,OAAO,MAAgB,KAAK,sCAAsC,OAAO,MAAM,GAAG;AACpF,SAAK,MAAM;AAAA,EACb;AACA,MAAI,wBAAwB,EAAE,GAAG;AAC/B,UAAM,eAAe;AACrB,WAAO;AAAA,EACT;AAEA,QAAM,MAAM;AACZ,SAAO;AACT;AACA,SAAS,wBAAwB,IAAI;AACnC,SAAO,kBAAkB,IAAI,IAAI,KAAK,OAAO,MAAgB,OAAO;AACtE;AASA,KAAK,iCAAiC,SAAS,OAAO;AACpD,MAAI,QAAQ,MAAM;AAClB,MAAI,SAAS,KAAK,QAAQ,eAAe;AACzC,MAAI,KAAK,MAAM,QAAQ,MAAM;AAC7B,QAAM,QAAQ,MAAM;AAEpB,MAAI,OAAO,MAAgB,KAAK,sCAAsC,OAAO,MAAM,GAAG;AACpF,SAAK,MAAM;AAAA,EACb;AACA,MAAI,uBAAuB,EAAE,GAAG;AAC9B,UAAM,eAAe;AACrB,WAAO;AAAA,EACT;AAEA,QAAM,MAAM;AACZ,SAAO;AACT;AACA,SAAS,uBAAuB,IAAI;AAClC,SAAO,iBAAiB,IAAI,IAAI,KAAK,OAAO,MAAgB,OAAO,MAAgB,OAAO,QAAuB,OAAO;AAC1H;AAGA,KAAK,uBAAuB,SAAS,OAAO;AAC1C,MACE,KAAK,wBAAwB,KAAK,KAClC,KAAK,+BAA+B,KAAK,KACzC,KAAK,0BAA0B,KAAK,KACnC,MAAM,WAAW,KAAK,qBAAqB,KAAK,GACjD;AACA,WAAO;AAAA,EACT;AACA,MAAI,MAAM,SAAS;AAEjB,QAAI,MAAM,QAAQ,MAAM,IAAc;AACpC,YAAM,MAAM,wBAAwB;AAAA,IACtC;AACA,UAAM,MAAM,gBAAgB;AAAA,EAC9B;AACA,SAAO;AACT;AACA,KAAK,0BAA0B,SAAS,OAAO;AAC7C,MAAI,QAAQ,MAAM;AAClB,MAAI,KAAK,wBAAwB,KAAK,GAAG;AACvC,QAAI,IAAI,MAAM;AACd,QAAI,MAAM,SAAS;AAEjB,UAAI,IAAI,MAAM,kBAAkB;AAC9B,cAAM,mBAAmB;AAAA,MAC3B;AACA,aAAO;AAAA,IACT;AACA,QAAI,KAAK,MAAM,oBAAoB;AACjC,aAAO;AAAA,IACT;AACA,UAAM,MAAM;AAAA,EACd;AACA,SAAO;AACT;AACA,KAAK,uBAAuB,SAAS,OAAO;AAC1C,MAAI,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AAC3B,QAAI,KAAK,oBAAoB,KAAK,GAAG;AACnC,YAAM,mBAAmB,KAAK,MAAM,eAAe;AACnD,aAAO;AAAA,IACT;AACA,UAAM,MAAM,yBAAyB;AAAA,EACvC;AACA,SAAO;AACT;AAGA,KAAK,4BAA4B,SAAS,OAAO;AAC/C,SACE,KAAK,wBAAwB,KAAK,KAClC,KAAK,yBAAyB,KAAK,KACnC,KAAK,eAAe,KAAK,KACzB,KAAK,4BAA4B,KAAK,KACtC,KAAK,sCAAsC,OAAO,KAAK,KACtD,CAAC,MAAM,WAAW,KAAK,oCAAoC,KAAK,KACjE,KAAK,yBAAyB,KAAK;AAEvC;AACA,KAAK,2BAA2B,SAAS,OAAO;AAC9C,MAAI,QAAQ,MAAM;AAClB,MAAI,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AAC3B,QAAI,KAAK,wBAAwB,KAAK,GAAG;AACvC,aAAO;AAAA,IACT;AACA,UAAM,MAAM;AAAA,EACd;AACA,SAAO;AACT;AACA,KAAK,iBAAiB,SAAS,OAAO;AACpC,MAAI,MAAM,QAAQ,MAAM,MAAgB,CAAC,eAAe,MAAM,UAAU,CAAC,GAAG;AAC1E,UAAM,eAAe;AACrB,UAAM,QAAQ;AACd,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAGA,KAAK,0BAA0B,SAAS,OAAO;AAC7C,MAAI,KAAK,MAAM,QAAQ;AACvB,MAAI,OAAO,KAAc;AACvB,UAAM,eAAe;AACrB,UAAM,QAAQ;AACd,WAAO;AAAA,EACT;AACA,MAAI,OAAO,KAAc;AACvB,UAAM,eAAe;AACrB,UAAM,QAAQ;AACd,WAAO;AAAA,EACT;AACA,MAAI,OAAO,KAAc;AACvB,UAAM,eAAe;AACrB,UAAM,QAAQ;AACd,WAAO;AAAA,EACT;AACA,MAAI,OAAO,KAAc;AACvB,UAAM,eAAe;AACrB,UAAM,QAAQ;AACd,WAAO;AAAA,EACT;AACA,MAAI,OAAO,KAAc;AACvB,UAAM,eAAe;AACrB,UAAM,QAAQ;AACd,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAGA,KAAK,0BAA0B,SAAS,OAAO;AAC7C,MAAI,KAAK,MAAM,QAAQ;AACvB,MAAI,gBAAgB,EAAE,GAAG;AACvB,UAAM,eAAe,KAAK;AAC1B,UAAM,QAAQ;AACd,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,gBAAgB,IAAI;AAC3B,SACG,MAAM,MAAgB,MAAM,MAC5B,MAAM,MAAgB,MAAM;AAEjC;AAGA,KAAK,wCAAwC,SAAS,OAAO,QAAQ;AACnE,MAAK,WAAW,OAAS,UAAS;AAElC,MAAI,QAAQ,MAAM;AAClB,MAAI,UAAU,UAAU,MAAM;AAE9B,MAAI,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AAC3B,QAAI,KAAK,yBAAyB,OAAO,CAAC,GAAG;AAC3C,UAAI,OAAO,MAAM;AACjB,UAAI,WAAW,QAAQ,SAAU,QAAQ,OAAQ;AAC/C,YAAI,mBAAmB,MAAM;AAC7B,YAAI,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,KAAK,MAAM;AAAA,UAAI;AAAA;AAAA,QAAY,KAAK,KAAK,yBAAyB,OAAO,CAAC,GAAG;AACjG,cAAI,QAAQ,MAAM;AAClB,cAAI,SAAS,SAAU,SAAS,OAAQ;AACtC,kBAAM,gBAAgB,OAAO,SAAU,QAAS,QAAQ,SAAU;AAClE,mBAAO;AAAA,UACT;AAAA,QACF;AACA,cAAM,MAAM;AACZ,cAAM,eAAe;AAAA,MACvB;AACA,aAAO;AAAA,IACT;AACA,QACE,WACA,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY,KACtB,KAAK,oBAAoB,KAAK,KAC9B,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY,KACtB,eAAe,MAAM,YAAY,GACjC;AACA,aAAO;AAAA,IACT;AACA,QAAI,SAAS;AACX,YAAM,MAAM,wBAAwB;AAAA,IACtC;AACA,UAAM,MAAM;AAAA,EACd;AAEA,SAAO;AACT;AACA,SAAS,eAAe,IAAI;AAC1B,SAAO,MAAM,KAAK,MAAM;AAC1B;AAGA,KAAK,2BAA2B,SAAS,OAAO;AAC9C,MAAI,MAAM,SAAS;AACjB,QAAI,KAAK,0BAA0B,KAAK,GAAG;AACzC,aAAO;AAAA,IACT;AACA,QAAI,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY,GAAG;AAC3B,YAAM,eAAe;AACrB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,MAAM,QAAQ;AACvB,MAAI,OAAO,OAAiB,CAAC,MAAM,WAAW,OAAO,MAAe;AAClE,UAAM,eAAe;AACrB,UAAM,QAAQ;AACd,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAGA,KAAK,0BAA0B,SAAS,OAAO;AAC7C,QAAM,eAAe;AACrB,MAAI,KAAK,MAAM,QAAQ;AACvB,MAAI,MAAM,MAAgB,MAAM,IAAc;AAC5C,OAAG;AACD,YAAM,eAAe,KAAK,MAAM,gBAAgB,KAAK;AACrD,YAAM,QAAQ;AAAA,IAChB,UAAU,KAAK,MAAM,QAAQ,MAAM,MAAgB,MAAM;AACzD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAIA,IAAI,cAAc;AAClB,IAAI,YAAY;AAChB,IAAI,gBAAgB;AAGpB,KAAK,iCAAiC,SAAS,OAAO;AACpD,MAAI,KAAK,MAAM,QAAQ;AAEvB,MAAI,uBAAuB,EAAE,GAAG;AAC9B,UAAM,eAAe;AACrB,UAAM,QAAQ;AACd,WAAO;AAAA,EACT;AAEA,MAAI,SAAS;AACb,MACE,MAAM,WACN,KAAK,QAAQ,eAAe,OAC1B,SAAS,OAAO,OAAiB,OAAO,MAC1C;AACA,UAAM,eAAe;AACrB,UAAM,QAAQ;AACd,QAAI;AACJ,QACE,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY,MACrB,SAAS,KAAK,yCAAyC,KAAK,MAC7D,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY,GACtB;AACA,UAAI,UAAU,WAAW,eAAe;AAAE,cAAM,MAAM,uBAAuB;AAAA,MAAG;AAChF,aAAO;AAAA,IACT;AACA,UAAM,MAAM,uBAAuB;AAAA,EACrC;AAEA,SAAO;AACT;AAEA,SAAS,uBAAuB,IAAI;AAClC,SACE,OAAO,OACP,OAAO,MACP,OAAO,OACP,OAAO,MACP,OAAO,OACP,OAAO;AAEX;AAKA,KAAK,2CAA2C,SAAS,OAAO;AAC9D,MAAI,QAAQ,MAAM;AAGlB,MAAI,KAAK,8BAA8B,KAAK,KAAK,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AACxE,QAAI,OAAO,MAAM;AACjB,QAAI,KAAK,+BAA+B,KAAK,GAAG;AAC9C,UAAI,QAAQ,MAAM;AAClB,WAAK,2CAA2C,OAAO,MAAM,KAAK;AAClE,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,MAAM;AAGZ,MAAI,KAAK,yCAAyC,KAAK,GAAG;AACxD,QAAI,cAAc,MAAM;AACxB,WAAO,KAAK,0CAA0C,OAAO,WAAW;AAAA,EAC1E;AACA,SAAO;AACT;AAEA,KAAK,6CAA6C,SAAS,OAAO,MAAM,OAAO;AAC7E,MAAI,CAAC,OAAO,MAAM,kBAAkB,WAAW,IAAI,GACjD;AAAE,UAAM,MAAM,uBAAuB;AAAA,EAAG;AAC1C,MAAI,CAAC,MAAM,kBAAkB,UAAU,IAAI,EAAE,KAAK,KAAK,GACrD;AAAE,UAAM,MAAM,wBAAwB;AAAA,EAAG;AAC7C;AAEA,KAAK,4CAA4C,SAAS,OAAO,aAAa;AAC5E,MAAI,MAAM,kBAAkB,OAAO,KAAK,WAAW,GAAG;AAAE,WAAO;AAAA,EAAU;AACzE,MAAI,MAAM,WAAW,MAAM,kBAAkB,gBAAgB,KAAK,WAAW,GAAG;AAAE,WAAO;AAAA,EAAc;AACvG,QAAM,MAAM,uBAAuB;AACrC;AAIA,KAAK,gCAAgC,SAAS,OAAO;AACnD,MAAI,KAAK;AACT,QAAM,kBAAkB;AACxB,SAAO,+BAA+B,KAAK,MAAM,QAAQ,CAAC,GAAG;AAC3D,UAAM,mBAAmB,kBAAkB,EAAE;AAC7C,UAAM,QAAQ;AAAA,EAChB;AACA,SAAO,MAAM,oBAAoB;AACnC;AAEA,SAAS,+BAA+B,IAAI;AAC1C,SAAO,gBAAgB,EAAE,KAAK,OAAO;AACvC;AAIA,KAAK,iCAAiC,SAAS,OAAO;AACpD,MAAI,KAAK;AACT,QAAM,kBAAkB;AACxB,SAAO,gCAAgC,KAAK,MAAM,QAAQ,CAAC,GAAG;AAC5D,UAAM,mBAAmB,kBAAkB,EAAE;AAC7C,UAAM,QAAQ;AAAA,EAChB;AACA,SAAO,MAAM,oBAAoB;AACnC;AACA,SAAS,gCAAgC,IAAI;AAC3C,SAAO,+BAA+B,EAAE,KAAK,eAAe,EAAE;AAChE;AAIA,KAAK,2CAA2C,SAAS,OAAO;AAC9D,SAAO,KAAK,+BAA+B,KAAK;AAClD;AAGA,KAAK,2BAA2B,SAAS,OAAO;AAC9C,MAAI,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AAC3B,QAAI,SAAS,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY;AACnC,QAAI,SAAS,KAAK,qBAAqB,KAAK;AAC5C,QAAI,CAAC,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY,GACzB;AAAE,YAAM,MAAM,8BAA8B;AAAA,IAAG;AACjD,QAAI,UAAU,WAAW,eACvB;AAAE,YAAM,MAAM,6CAA6C;AAAA,IAAG;AAChE,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAIA,KAAK,uBAAuB,SAAS,OAAO;AAC1C,MAAI,MAAM,QAAQ,MAAM,IAAc;AAAE,WAAO;AAAA,EAAU;AACzD,MAAI,MAAM,SAAS;AAAE,WAAO,KAAK,0BAA0B,KAAK;AAAA,EAAE;AAClE,OAAK,2BAA2B,KAAK;AACrC,SAAO;AACT;AAIA,KAAK,6BAA6B,SAAS,OAAO;AAChD,SAAO,KAAK,oBAAoB,KAAK,GAAG;AACtC,QAAI,OAAO,MAAM;AACjB,QAAI,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY,KAAK,KAAK,oBAAoB,KAAK,GAAG;AAC9D,UAAI,QAAQ,MAAM;AAClB,UAAI,MAAM,YAAY,SAAS,MAAM,UAAU,KAAK;AAClD,cAAM,MAAM,yBAAyB;AAAA,MACvC;AACA,UAAI,SAAS,MAAM,UAAU,MAAM,OAAO,OAAO;AAC/C,cAAM,MAAM,uCAAuC;AAAA,MACrD;AAAA,IACF;AAAA,EACF;AACF;AAIA,KAAK,sBAAsB,SAAS,OAAO;AACzC,MAAI,QAAQ,MAAM;AAElB,MAAI,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AAC3B,QAAI,KAAK,sBAAsB,KAAK,GAAG;AACrC,aAAO;AAAA,IACT;AACA,QAAI,MAAM,SAAS;AAEjB,UAAI,OAAO,MAAM,QAAQ;AACzB,UAAI,SAAS,MAAgB,aAAa,IAAI,GAAG;AAC/C,cAAM,MAAM,sBAAsB;AAAA,MACpC;AACA,YAAM,MAAM,gBAAgB;AAAA,IAC9B;AACA,UAAM,MAAM;AAAA,EACd;AAEA,MAAI,KAAK,MAAM,QAAQ;AACvB,MAAI,OAAO,IAAc;AACvB,UAAM,eAAe;AACrB,UAAM,QAAQ;AACd,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAGA,KAAK,wBAAwB,SAAS,OAAO;AAC3C,MAAI,QAAQ,MAAM;AAElB,MAAI,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AAC3B,UAAM,eAAe;AACrB,WAAO;AAAA,EACT;AAEA,MAAI,MAAM,WAAW,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AAC5C,UAAM,eAAe;AACrB,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,MAAM,WAAW,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AAC7C,QAAI,KAAK,6BAA6B,KAAK,GAAG;AAC5C,aAAO;AAAA,IACT;AACA,UAAM,MAAM;AAAA,EACd;AAEA,SACE,KAAK,+BAA+B,KAAK,KACzC,KAAK,0BAA0B,KAAK;AAExC;AAMA,KAAK,4BAA4B,SAAS,OAAO;AAC/C,MAAI,SAAS,WAAW;AACxB,MAAI,KAAK,wBAAwB,KAAK,EAAG;AAAA,WAAW,YAAY,KAAK,0BAA0B,KAAK,GAAG;AACrG,QAAI,cAAc,eAAe;AAAE,eAAS;AAAA,IAAe;AAE3D,QAAI,QAAQ,MAAM;AAClB,WAAO,MAAM;AAAA,MAAS,CAAC,IAAM,EAAI;AAAA;AAAA,IAAU,GAAG;AAC5C,UACE,MAAM,QAAQ,MAAM,OACnB,YAAY,KAAK,0BAA0B,KAAK,IACjD;AACA,YAAI,cAAc,eAAe;AAAE,mBAAS;AAAA,QAAW;AACvD;AAAA,MACF;AACA,YAAM,MAAM,sCAAsC;AAAA,IACpD;AACA,QAAI,UAAU,MAAM,KAAK;AAAE,aAAO;AAAA,IAAO;AAEzC,WAAO,MAAM;AAAA,MAAS,CAAC,IAAM,EAAI;AAAA;AAAA,IAAU,GAAG;AAC5C,UAAI,KAAK,0BAA0B,KAAK,GAAG;AAAE;AAAA,MAAS;AACtD,YAAM,MAAM,sCAAsC;AAAA,IACpD;AACA,QAAI,UAAU,MAAM,KAAK;AAAE,aAAO;AAAA,IAAO;AAAA,EAC3C,OAAO;AACL,UAAM,MAAM,sCAAsC;AAAA,EACpD;AAEA,aAAS;AACP,QAAI,KAAK,wBAAwB,KAAK,GAAG;AAAE;AAAA,IAAS;AACpD,gBAAY,KAAK,0BAA0B,KAAK;AAChD,QAAI,CAAC,WAAW;AAAE,aAAO;AAAA,IAAO;AAChC,QAAI,cAAc,eAAe;AAAE,eAAS;AAAA,IAAe;AAAA,EAC7D;AACF;AAGA,KAAK,0BAA0B,SAAS,OAAO;AAC7C,MAAI,QAAQ,MAAM;AAClB,MAAI,KAAK,4BAA4B,KAAK,GAAG;AAC3C,QAAI,OAAO,MAAM;AACjB,QAAI,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY,KAAK,KAAK,4BAA4B,KAAK,GAAG;AACtE,UAAI,QAAQ,MAAM;AAClB,UAAI,SAAS,MAAM,UAAU,MAAM,OAAO,OAAO;AAC/C,cAAM,MAAM,uCAAuC;AAAA,MACrD;AACA,aAAO;AAAA,IACT;AACA,UAAM,MAAM;AAAA,EACd;AACA,SAAO;AACT;AAGA,KAAK,4BAA4B,SAAS,OAAO;AAC/C,MAAI,KAAK,4BAA4B,KAAK,GAAG;AAAE,WAAO;AAAA,EAAU;AAChE,SAAO,KAAK,iCAAiC,KAAK,KAAK,KAAK,sBAAsB,KAAK;AACzF;AAGA,KAAK,wBAAwB,SAAS,OAAO;AAC3C,MAAI,QAAQ,MAAM;AAClB,MAAI,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AAC3B,QAAI,SAAS,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY;AACnC,QAAI,SAAS,KAAK,qBAAqB,KAAK;AAC5C,QAAI,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY,GAAG;AAC3B,UAAI,UAAU,WAAW,eAAe;AACtC,cAAM,MAAM,6CAA6C;AAAA,MAC3D;AACA,aAAO;AAAA,IACT;AACA,UAAM,MAAM;AAAA,EACd;AACA,MAAI,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AAC3B,QAAI,WAAW,KAAK,+BAA+B,KAAK;AACxD,QAAI,UAAU;AACZ,aAAO;AAAA,IACT;AACA,UAAM,MAAM;AAAA,EACd;AACA,SAAO;AACT;AAGA,KAAK,mCAAmC,SAAS,OAAO;AACtD,MAAI,QAAQ,MAAM;AAClB,MAAI,MAAM;AAAA,IAAS,CAAC,IAAM,GAAI;AAAA;AAAA,EAAU,GAAG;AACzC,QAAI,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY,GAAG;AAC3B,UAAI,SAAS,KAAK,sCAAsC,KAAK;AAC7D,UAAI,MAAM;AAAA,QAAI;AAAA;AAAA,MAAY,GAAG;AAC3B,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AAEL,YAAM,MAAM,gBAAgB;AAAA,IAC9B;AACA,UAAM,MAAM;AAAA,EACd;AACA,SAAO;AACT;AAGA,KAAK,wCAAwC,SAAS,OAAO;AAC3D,MAAI,SAAS,KAAK,mBAAmB,KAAK;AAC1C,SAAO,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AAC9B,QAAI,KAAK,mBAAmB,KAAK,MAAM,eAAe;AAAE,eAAS;AAAA,IAAe;AAAA,EAClF;AACA,SAAO;AACT;AAIA,KAAK,qBAAqB,SAAS,OAAO;AACxC,MAAI,QAAQ;AACZ,SAAO,KAAK,4BAA4B,KAAK,GAAG;AAAE;AAAA,EAAS;AAC3D,SAAO,UAAU,IAAI,YAAY;AACnC;AAGA,KAAK,8BAA8B,SAAS,OAAO;AACjD,MAAI,QAAQ,MAAM;AAClB,MAAI,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AAC3B,QACE,KAAK,0BAA0B,KAAK,KACpC,KAAK,qCAAqC,KAAK,GAC/C;AACA,aAAO;AAAA,IACT;AACA,QAAI,MAAM;AAAA,MAAI;AAAA;AAAA,IAAY,GAAG;AAC3B,YAAM,eAAe;AACrB,aAAO;AAAA,IACT;AACA,UAAM,MAAM;AACZ,WAAO;AAAA,EACT;AACA,MAAI,KAAK,MAAM,QAAQ;AACvB,MAAI,KAAK,KAAK,OAAO,MAAM,UAAU,KAAK,4CAA4C,EAAE,GAAG;AAAE,WAAO;AAAA,EAAM;AAC1G,MAAI,0BAA0B,EAAE,GAAG;AAAE,WAAO;AAAA,EAAM;AAClD,QAAM,QAAQ;AACd,QAAM,eAAe;AACrB,SAAO;AACT;AAGA,SAAS,4CAA4C,IAAI;AACvD,SACE,OAAO,MACP,MAAM,MAAgB,MAAM,MAC5B,MAAM,MAAgB,MAAM,MAC5B,OAAO,MACP,MAAM,MAAgB,MAAM,MAC5B,OAAO,MACP,OAAO,MACP,OAAO;AAEX;AAGA,SAAS,0BAA0B,IAAI;AACrC,SACE,OAAO,MACP,OAAO,MACP,OAAO,MACP,OAAO,MACP,MAAM,MAAgB,MAAM,MAC5B,MAAM,OAAgB,MAAM;AAEhC;AAGA,KAAK,uCAAuC,SAAS,OAAO;AAC1D,MAAI,KAAK,MAAM,QAAQ;AACvB,MAAI,6BAA6B,EAAE,GAAG;AACpC,UAAM,eAAe;AACrB,UAAM,QAAQ;AACd,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAGA,SAAS,6BAA6B,IAAI;AACxC,SACE,OAAO,MACP,OAAO,MACP,OAAO,MACP,OAAO,MACP,OAAO,MACP,OAAO,MACP,MAAM,MAAgB,MAAM,MAC5B,OAAO,MACP,OAAO,MACP,OAAO;AAEX;AAGA,KAAK,+BAA+B,SAAS,OAAO;AAClD,MAAI,KAAK,MAAM,QAAQ;AACvB,MAAI,eAAe,EAAE,KAAK,OAAO,IAAc;AAC7C,UAAM,eAAe,KAAK;AAC1B,UAAM,QAAQ;AACd,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAGA,KAAK,8BAA8B,SAAS,OAAO;AACjD,MAAI,QAAQ,MAAM;AAClB,MAAI,MAAM;AAAA,IAAI;AAAA;AAAA,EAAY,GAAG;AAC3B,QAAI,KAAK,yBAAyB,OAAO,CAAC,GAAG;AAC3C,aAAO;AAAA,IACT;AACA,QAAI,MAAM,SAAS;AACjB,YAAM,MAAM,gBAAgB;AAAA,IAC9B;AACA,UAAM,MAAM;AAAA,EACd;AACA,SAAO;AACT;AAGA,KAAK,0BAA0B,SAAS,OAAO;AAC7C,MAAI,QAAQ,MAAM;AAClB,MAAI,KAAK;AACT,QAAM,eAAe;AACrB,SAAO,eAAe,KAAK,MAAM,QAAQ,CAAC,GAAG;AAC3C,UAAM,eAAe,KAAK,MAAM,gBAAgB,KAAK;AACrD,UAAM,QAAQ;AAAA,EAChB;AACA,SAAO,MAAM,QAAQ;AACvB;AACA,SAAS,eAAe,IAAI;AAC1B,SAAO,MAAM,MAAgB,MAAM;AACrC;AAGA,KAAK,sBAAsB,SAAS,OAAO;AACzC,MAAI,QAAQ,MAAM;AAClB,MAAI,KAAK;AACT,QAAM,eAAe;AACrB,SAAO,WAAW,KAAK,MAAM,QAAQ,CAAC,GAAG;AACvC,UAAM,eAAe,KAAK,MAAM,eAAe,SAAS,EAAE;AAC1D,UAAM,QAAQ;AAAA,EAChB;AACA,SAAO,MAAM,QAAQ;AACvB;AACA,SAAS,WAAW,IAAI;AACtB,SACG,MAAM,MAAgB,MAAM,MAC5B,MAAM,MAAgB,MAAM,MAC5B,MAAM,MAAgB,MAAM;AAEjC;AACA,SAAS,SAAS,IAAI;AACpB,MAAI,MAAM,MAAgB,MAAM,IAAc;AAC5C,WAAO,MAAM,KAAK;AAAA,EACpB;AACA,MAAI,MAAM,MAAgB,MAAM,KAAc;AAC5C,WAAO,MAAM,KAAK;AAAA,EACpB;AACA,SAAO,KAAK;AACd;AAIA,KAAK,sCAAsC,SAAS,OAAO;AACzD,MAAI,KAAK,qBAAqB,KAAK,GAAG;AACpC,QAAI,KAAK,MAAM;AACf,QAAI,KAAK,qBAAqB,KAAK,GAAG;AACpC,UAAI,KAAK,MAAM;AACf,UAAI,MAAM,KAAK,KAAK,qBAAqB,KAAK,GAAG;AAC/C,cAAM,eAAe,KAAK,KAAK,KAAK,IAAI,MAAM;AAAA,MAChD,OAAO;AACL,cAAM,eAAe,KAAK,IAAI;AAAA,MAChC;AAAA,IACF,OAAO;AACL,YAAM,eAAe;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAGA,KAAK,uBAAuB,SAAS,OAAO;AAC1C,MAAI,KAAK,MAAM,QAAQ;AACvB,MAAI,aAAa,EAAE,GAAG;AACpB,UAAM,eAAe,KAAK;AAC1B,UAAM,QAAQ;AACd,WAAO;AAAA,EACT;AACA,QAAM,eAAe;AACrB,SAAO;AACT;AACA,SAAS,aAAa,IAAI;AACxB,SAAO,MAAM,MAAgB,MAAM;AACrC;AAKA,KAAK,2BAA2B,SAAS,OAAO,QAAQ;AACtD,MAAI,QAAQ,MAAM;AAClB,QAAM,eAAe;AACrB,WAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,QAAI,KAAK,MAAM,QAAQ;AACvB,QAAI,CAAC,WAAW,EAAE,GAAG;AACnB,YAAM,MAAM;AACZ,aAAO;AAAA,IACT;AACA,UAAM,eAAe,KAAK,MAAM,eAAe,SAAS,EAAE;AAC1D,UAAM,QAAQ;AAAA,EAChB;AACA,SAAO;AACT;AAMA,IAAI,QAAQ,SAASC,OAAM,GAAG;AAC5B,OAAK,OAAO,EAAE;AACd,OAAK,QAAQ,EAAE;AACf,OAAK,QAAQ,EAAE;AACf,OAAK,MAAM,EAAE;AACb,MAAI,EAAE,QAAQ,WACZ;AAAE,SAAK,MAAM,IAAI,eAAe,GAAG,EAAE,UAAU,EAAE,MAAM;AAAA,EAAG;AAC5D,MAAI,EAAE,QAAQ,QACZ;AAAE,SAAK,QAAQ,CAAC,EAAE,OAAO,EAAE,GAAG;AAAA,EAAG;AACrC;AAIA,IAAI,KAAK,OAAO;AAIhB,GAAG,OAAO,SAAS,+BAA+B;AAChD,MAAI,CAAC,iCAAiC,KAAK,KAAK,WAAW,KAAK,aAC9D;AAAE,SAAK,iBAAiB,KAAK,OAAO,gCAAgC,KAAK,KAAK,OAAO;AAAA,EAAG;AAC1F,MAAI,KAAK,QAAQ,SACf;AAAE,SAAK,QAAQ,QAAQ,IAAI,MAAM,IAAI,CAAC;AAAA,EAAG;AAE3C,OAAK,aAAa,KAAK;AACvB,OAAK,eAAe,KAAK;AACzB,OAAK,gBAAgB,KAAK;AAC1B,OAAK,kBAAkB,KAAK;AAC5B,OAAK,UAAU;AACjB;AAEA,GAAG,WAAW,WAAW;AACvB,OAAK,KAAK;AACV,SAAO,IAAI,MAAM,IAAI;AACvB;AAGA,IAAI,OAAO,WAAW,aACpB;AAAE,KAAG,OAAO,QAAQ,IAAI,WAAW;AACjC,QAAI,WAAW;AAEf,WAAO;AAAA,MACL,MAAM,WAAY;AAChB,YAAI,QAAQ,SAAS,SAAS;AAC9B,eAAO;AAAA,UACL,MAAM,MAAM,SAAS,QAAQ;AAAA,UAC7B,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAG;AAQL,GAAG,YAAY,WAAW;AACxB,MAAI,aAAa,KAAK,WAAW;AACjC,MAAI,CAAC,cAAc,CAAC,WAAW,eAAe;AAAE,SAAK,UAAU;AAAA,EAAG;AAElE,OAAK,QAAQ,KAAK;AAClB,MAAI,KAAK,QAAQ,WAAW;AAAE,SAAK,WAAW,KAAK,YAAY;AAAA,EAAG;AAClE,MAAI,KAAK,OAAO,KAAK,MAAM,QAAQ;AAAE,WAAO,KAAK,YAAY,QAAQ,GAAG;AAAA,EAAE;AAE1E,MAAI,WAAW,UAAU;AAAE,WAAO,WAAW,SAAS,IAAI;AAAA,EAAE,OACvD;AAAE,SAAK,UAAU,KAAK,kBAAkB,CAAC;AAAA,EAAG;AACnD;AAEA,GAAG,YAAY,SAAS,MAAM;AAG5B,MAAI,kBAAkB,MAAM,KAAK,QAAQ,eAAe,CAAC,KAAK,SAAS,IACrE;AAAE,WAAO,KAAK,SAAS;AAAA,EAAE;AAE3B,SAAO,KAAK,iBAAiB,IAAI;AACnC;AAEA,GAAG,oBAAoB,WAAW;AAChC,MAAI,OAAO,KAAK,MAAM,WAAW,KAAK,GAAG;AACzC,MAAI,QAAQ,SAAU,QAAQ,OAAQ;AAAE,WAAO;AAAA,EAAK;AACpD,MAAI,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC7C,SAAO,QAAQ,SAAU,QAAQ,QAAS,QAAQ,QAAQ,MAAM,OAAO;AACzE;AAEA,GAAG,mBAAmB,WAAW;AAC/B,MAAI,WAAW,KAAK,QAAQ,aAAa,KAAK,YAAY;AAC1D,MAAI,QAAQ,KAAK,KAAK,MAAM,KAAK,MAAM,QAAQ,MAAM,KAAK,OAAO,CAAC;AAClE,MAAI,QAAQ,IAAI;AAAE,SAAK,MAAM,KAAK,MAAM,GAAG,sBAAsB;AAAA,EAAG;AACpE,OAAK,MAAM,MAAM;AACjB,MAAI,KAAK,QAAQ,WAAW;AAC1B,aAAS,YAAa,QAAS,MAAM,QAAQ,YAAY,cAAc,KAAK,OAAO,KAAK,KAAK,GAAG,KAAK,MAAK;AACxG,QAAE,KAAK;AACP,YAAM,KAAK,YAAY;AAAA,IACzB;AAAA,EACF;AACA,MAAI,KAAK,QAAQ,WACf;AAAE,SAAK,QAAQ;AAAA,MAAU;AAAA,MAAM,KAAK,MAAM,MAAM,QAAQ,GAAG,GAAG;AAAA,MAAG;AAAA,MAAO,KAAK;AAAA,MACtD;AAAA,MAAU,KAAK,YAAY;AAAA,IAAC;AAAA,EAAG;AAC1D;AAEA,GAAG,kBAAkB,SAAS,WAAW;AACvC,MAAI,QAAQ,KAAK;AACjB,MAAI,WAAW,KAAK,QAAQ,aAAa,KAAK,YAAY;AAC1D,MAAI,KAAK,KAAK,MAAM,WAAW,KAAK,OAAO,SAAS;AACpD,SAAO,KAAK,MAAM,KAAK,MAAM,UAAU,CAAC,UAAU,EAAE,GAAG;AACrD,SAAK,KAAK,MAAM,WAAW,EAAE,KAAK,GAAG;AAAA,EACvC;AACA,MAAI,KAAK,QAAQ,WACf;AAAE,SAAK,QAAQ;AAAA,MAAU;AAAA,MAAO,KAAK,MAAM,MAAM,QAAQ,WAAW,KAAK,GAAG;AAAA,MAAG;AAAA,MAAO,KAAK;AAAA,MACpE;AAAA,MAAU,KAAK,YAAY;AAAA,IAAC;AAAA,EAAG;AAC1D;AAKA,GAAG,YAAY,WAAW;AACxB,OAAM,QAAO,KAAK,MAAM,KAAK,MAAM,QAAQ;AACzC,QAAI,KAAK,KAAK,MAAM,WAAW,KAAK,GAAG;AACvC,YAAQ,IAAI;AAAA,MACZ,KAAK;AAAA,MAAI,KAAK;AACZ,UAAE,KAAK;AACP;AAAA,MACF,KAAK;AACH,YAAI,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,IAAI;AAC9C,YAAE,KAAK;AAAA,QACT;AAAA,MACF,KAAK;AAAA,MAAI,KAAK;AAAA,MAAM,KAAK;AACvB,UAAE,KAAK;AACP,YAAI,KAAK,QAAQ,WAAW;AAC1B,YAAE,KAAK;AACP,eAAK,YAAY,KAAK;AAAA,QACxB;AACA;AAAA,MACF,KAAK;AACH,gBAAQ,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC,GAAG;AAAA,UAC7C,KAAK;AACH,iBAAK,iBAAiB;AACtB;AAAA,UACF,KAAK;AACH,iBAAK,gBAAgB,CAAC;AACtB;AAAA,UACF;AACE,kBAAM;AAAA,QACR;AACA;AAAA,MACF;AACE,YAAI,KAAK,KAAK,KAAK,MAAM,MAAM,QAAQ,mBAAmB,KAAK,OAAO,aAAa,EAAE,CAAC,GAAG;AACvF,YAAE,KAAK;AAAA,QACT,OAAO;AACL,gBAAM;AAAA,QACR;AAAA,IACF;AAAA,EACF;AACF;AAOA,GAAG,cAAc,SAAS,MAAM,KAAK;AACnC,OAAK,MAAM,KAAK;AAChB,MAAI,KAAK,QAAQ,WAAW;AAAE,SAAK,SAAS,KAAK,YAAY;AAAA,EAAG;AAChE,MAAI,WAAW,KAAK;AACpB,OAAK,OAAO;AACZ,OAAK,QAAQ;AAEb,OAAK,cAAc,QAAQ;AAC7B;AAWA,GAAG,gBAAgB,WAAW;AAC5B,MAAI,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC7C,MAAI,QAAQ,MAAM,QAAQ,IAAI;AAAE,WAAO,KAAK,WAAW,IAAI;AAAA,EAAE;AAC7D,MAAI,QAAQ,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC9C,MAAI,KAAK,QAAQ,eAAe,KAAK,SAAS,MAAM,UAAU,IAAI;AAChE,SAAK,OAAO;AACZ,WAAO,KAAK,YAAY,QAAQ,QAAQ;AAAA,EAC1C,OAAO;AACL,MAAE,KAAK;AACP,WAAO,KAAK,YAAY,QAAQ,GAAG;AAAA,EACrC;AACF;AAEA,GAAG,kBAAkB,WAAW;AAC9B,MAAI,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC7C,MAAI,KAAK,aAAa;AAAE,MAAE,KAAK;AAAK,WAAO,KAAK,WAAW;AAAA,EAAE;AAC7D,MAAI,SAAS,IAAI;AAAE,WAAO,KAAK,SAAS,QAAQ,QAAQ,CAAC;AAAA,EAAE;AAC3D,SAAO,KAAK,SAAS,QAAQ,OAAO,CAAC;AACvC;AAEA,GAAG,4BAA4B,SAAS,MAAM;AAC5C,MAAI,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC7C,MAAI,OAAO;AACX,MAAI,YAAY,SAAS,KAAK,QAAQ,OAAO,QAAQ;AAGrD,MAAI,KAAK,QAAQ,eAAe,KAAK,SAAS,MAAM,SAAS,IAAI;AAC/D,MAAE;AACF,gBAAY,QAAQ;AACpB,WAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAAA,EAC3C;AAEA,MAAI,SAAS,IAAI;AAAE,WAAO,KAAK,SAAS,QAAQ,QAAQ,OAAO,CAAC;AAAA,EAAE;AAClE,SAAO,KAAK,SAAS,WAAW,IAAI;AACtC;AAEA,GAAG,qBAAqB,SAAS,MAAM;AACrC,MAAI,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC7C,MAAI,SAAS,MAAM;AACjB,QAAI,KAAK,QAAQ,eAAe,IAAI;AAClC,UAAI,QAAQ,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC9C,UAAI,UAAU,IAAI;AAAE,eAAO,KAAK,SAAS,QAAQ,QAAQ,CAAC;AAAA,MAAE;AAAA,IAC9D;AACA,WAAO,KAAK,SAAS,SAAS,MAAM,QAAQ,YAAY,QAAQ,YAAY,CAAC;AAAA,EAC/E;AACA,MAAI,SAAS,IAAI;AAAE,WAAO,KAAK,SAAS,QAAQ,QAAQ,CAAC;AAAA,EAAE;AAC3D,SAAO,KAAK,SAAS,SAAS,MAAM,QAAQ,YAAY,QAAQ,YAAY,CAAC;AAC/E;AAEA,GAAG,kBAAkB,WAAW;AAC9B,MAAI,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC7C,MAAI,SAAS,IAAI;AAAE,WAAO,KAAK,SAAS,QAAQ,QAAQ,CAAC;AAAA,EAAE;AAC3D,SAAO,KAAK,SAAS,QAAQ,YAAY,CAAC;AAC5C;AAEA,GAAG,qBAAqB,SAAS,MAAM;AACrC,MAAI,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC7C,MAAI,SAAS,MAAM;AACjB,QAAI,SAAS,MAAM,CAAC,KAAK,YAAY,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,OACxE,KAAK,eAAe,KAAK,UAAU,KAAK,KAAK,MAAM,MAAM,KAAK,YAAY,KAAK,GAAG,CAAC,IAAI;AAE1F,WAAK,gBAAgB,CAAC;AACtB,WAAK,UAAU;AACf,aAAO,KAAK,UAAU;AAAA,IACxB;AACA,WAAO,KAAK,SAAS,QAAQ,QAAQ,CAAC;AAAA,EACxC;AACA,MAAI,SAAS,IAAI;AAAE,WAAO,KAAK,SAAS,QAAQ,QAAQ,CAAC;AAAA,EAAE;AAC3D,SAAO,KAAK,SAAS,QAAQ,SAAS,CAAC;AACzC;AAEA,GAAG,kBAAkB,SAAS,MAAM;AAClC,MAAI,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC7C,MAAI,OAAO;AACX,MAAI,SAAS,MAAM;AACjB,WAAO,SAAS,MAAM,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,KAAK,IAAI;AACvE,QAAI,KAAK,MAAM,WAAW,KAAK,MAAM,IAAI,MAAM,IAAI;AAAE,aAAO,KAAK,SAAS,QAAQ,QAAQ,OAAO,CAAC;AAAA,IAAE;AACpG,WAAO,KAAK,SAAS,QAAQ,UAAU,IAAI;AAAA,EAC7C;AACA,MAAI,SAAS,MAAM,SAAS,MAAM,CAAC,KAAK,YAAY,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,MACxF,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,IAAI;AAE9C,SAAK,gBAAgB,CAAC;AACtB,SAAK,UAAU;AACf,WAAO,KAAK,UAAU;AAAA,EACxB;AACA,MAAI,SAAS,IAAI;AAAE,WAAO;AAAA,EAAG;AAC7B,SAAO,KAAK,SAAS,QAAQ,YAAY,IAAI;AAC/C;AAEA,GAAG,oBAAoB,SAAS,MAAM;AACpC,MAAI,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC7C,MAAI,SAAS,IAAI;AAAE,WAAO,KAAK,SAAS,QAAQ,UAAU,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC;AAAA,EAAE;AAC9G,MAAI,SAAS,MAAM,SAAS,MAAM,KAAK,QAAQ,eAAe,GAAG;AAC/D,SAAK,OAAO;AACZ,WAAO,KAAK,YAAY,QAAQ,KAAK;AAAA,EACvC;AACA,SAAO,KAAK,SAAS,SAAS,KAAK,QAAQ,KAAK,QAAQ,QAAQ,CAAC;AACnE;AAEA,GAAG,qBAAqB,WAAW;AACjC,MAAI,cAAc,KAAK,QAAQ;AAC/B,MAAI,eAAe,IAAI;AACrB,QAAI,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC7C,QAAI,SAAS,IAAI;AACf,UAAI,QAAQ,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC9C,UAAI,QAAQ,MAAM,QAAQ,IAAI;AAAE,eAAO,KAAK,SAAS,QAAQ,aAAa,CAAC;AAAA,MAAE;AAAA,IAC/E;AACA,QAAI,SAAS,IAAI;AACf,UAAI,eAAe,IAAI;AACrB,YAAI,UAAU,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAChD,YAAI,YAAY,IAAI;AAAE,iBAAO,KAAK,SAAS,QAAQ,QAAQ,CAAC;AAAA,QAAE;AAAA,MAChE;AACA,aAAO,KAAK,SAAS,QAAQ,UAAU,CAAC;AAAA,IAC1C;AAAA,EACF;AACA,SAAO,KAAK,SAAS,QAAQ,UAAU,CAAC;AAC1C;AAEA,GAAG,uBAAuB,WAAW;AACnC,MAAI,cAAc,KAAK,QAAQ;AAC/B,MAAI,OAAO;AACX,MAAI,eAAe,IAAI;AACrB,MAAE,KAAK;AACP,WAAO,KAAK,kBAAkB;AAC9B,QAAI,kBAAkB,MAAM,IAAI,KAAK,SAAS,IAAc;AAC1D,aAAO,KAAK,YAAY,QAAQ,WAAW,KAAK,UAAU,CAAC;AAAA,IAC7D;AAAA,EACF;AAEA,OAAK,MAAM,KAAK,KAAK,2BAA2B,kBAAkB,IAAI,IAAI,GAAG;AAC/E;AAEA,GAAG,mBAAmB,SAAS,MAAM;AACnC,UAAQ,MAAM;AAAA;AAAA;AAAA,IAGd,KAAK;AACH,aAAO,KAAK,cAAc;AAAA;AAAA,IAG5B,KAAK;AAAI,QAAE,KAAK;AAAK,aAAO,KAAK,YAAY,QAAQ,MAAM;AAAA,IAC3D,KAAK;AAAI,QAAE,KAAK;AAAK,aAAO,KAAK,YAAY,QAAQ,MAAM;AAAA,IAC3D,KAAK;AAAI,QAAE,KAAK;AAAK,aAAO,KAAK,YAAY,QAAQ,IAAI;AAAA,IACzD,KAAK;AAAI,QAAE,KAAK;AAAK,aAAO,KAAK,YAAY,QAAQ,KAAK;AAAA,IAC1D,KAAK;AAAI,QAAE,KAAK;AAAK,aAAO,KAAK,YAAY,QAAQ,QAAQ;AAAA,IAC7D,KAAK;AAAI,QAAE,KAAK;AAAK,aAAO,KAAK,YAAY,QAAQ,QAAQ;AAAA,IAC7D,KAAK;AAAK,QAAE,KAAK;AAAK,aAAO,KAAK,YAAY,QAAQ,MAAM;AAAA,IAC5D,KAAK;AAAK,QAAE,KAAK;AAAK,aAAO,KAAK,YAAY,QAAQ,MAAM;AAAA,IAC5D,KAAK;AAAI,QAAE,KAAK;AAAK,aAAO,KAAK,YAAY,QAAQ,KAAK;AAAA,IAE1D,KAAK;AACH,UAAI,KAAK,QAAQ,cAAc,GAAG;AAAE;AAAA,MAAM;AAC1C,QAAE,KAAK;AACP,aAAO,KAAK,YAAY,QAAQ,SAAS;AAAA,IAE3C,KAAK;AACH,UAAI,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC;AAC7C,UAAI,SAAS,OAAO,SAAS,IAAI;AAAE,eAAO,KAAK,gBAAgB,EAAE;AAAA,MAAE;AACnE,UAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,YAAI,SAAS,OAAO,SAAS,IAAI;AAAE,iBAAO,KAAK,gBAAgB,CAAC;AAAA,QAAE;AAClE,YAAI,SAAS,MAAM,SAAS,IAAI;AAAE,iBAAO,KAAK,gBAAgB,CAAC;AAAA,QAAE;AAAA,MACnE;AAAA;AAAA;AAAA,IAIF,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAAA,IAAI,KAAK;AAC3E,aAAO,KAAK,WAAW,KAAK;AAAA;AAAA,IAG9B,KAAK;AAAA,IAAI,KAAK;AACZ,aAAO,KAAK,WAAW,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,IAM7B,KAAK;AACH,aAAO,KAAK,gBAAgB;AAAA,IAE9B,KAAK;AAAA,IAAI,KAAK;AACZ,aAAO,KAAK,0BAA0B,IAAI;AAAA,IAE5C,KAAK;AAAA,IAAK,KAAK;AACb,aAAO,KAAK,mBAAmB,IAAI;AAAA,IAErC,KAAK;AACH,aAAO,KAAK,gBAAgB;AAAA,IAE9B,KAAK;AAAA,IAAI,KAAK;AACZ,aAAO,KAAK,mBAAmB,IAAI;AAAA,IAErC,KAAK;AAAA,IAAI,KAAK;AACZ,aAAO,KAAK,gBAAgB,IAAI;AAAA,IAElC,KAAK;AAAA,IAAI,KAAK;AACZ,aAAO,KAAK,kBAAkB,IAAI;AAAA,IAEpC,KAAK;AACH,aAAO,KAAK,mBAAmB;AAAA,IAEjC,KAAK;AACH,aAAO,KAAK,SAAS,QAAQ,QAAQ,CAAC;AAAA,IAExC,KAAK;AACH,aAAO,KAAK,qBAAqB;AAAA,EACnC;AAEA,OAAK,MAAM,KAAK,KAAK,2BAA2B,kBAAkB,IAAI,IAAI,GAAG;AAC/E;AAEA,GAAG,WAAW,SAAS,MAAM,MAAM;AACjC,MAAI,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,KAAK,MAAM,IAAI;AACpD,OAAK,OAAO;AACZ,SAAO,KAAK,YAAY,MAAM,GAAG;AACnC;AAEA,GAAG,aAAa,WAAW;AACzB,MAAI,SAAS,SAAS,QAAQ,KAAK;AACnC,aAAS;AACP,QAAI,KAAK,OAAO,KAAK,MAAM,QAAQ;AAAE,WAAK,MAAM,OAAO,iCAAiC;AAAA,IAAG;AAC3F,QAAI,KAAK,KAAK,MAAM,OAAO,KAAK,GAAG;AACnC,QAAI,UAAU,KAAK,EAAE,GAAG;AAAE,WAAK,MAAM,OAAO,iCAAiC;AAAA,IAAG;AAChF,QAAI,CAAC,SAAS;AACZ,UAAI,OAAO,KAAK;AAAE,kBAAU;AAAA,MAAM,WACzB,OAAO,OAAO,SAAS;AAAE,kBAAU;AAAA,MAAO,WAC1C,OAAO,OAAO,CAAC,SAAS;AAAE;AAAA,MAAM;AACzC,gBAAU,OAAO;AAAA,IACnB,OAAO;AAAE,gBAAU;AAAA,IAAO;AAC1B,MAAE,KAAK;AAAA,EACT;AACA,MAAI,UAAU,KAAK,MAAM,MAAM,OAAO,KAAK,GAAG;AAC9C,IAAE,KAAK;AACP,MAAI,aAAa,KAAK;AACtB,MAAI,QAAQ,KAAK,UAAU;AAC3B,MAAI,KAAK,aAAa;AAAE,SAAK,WAAW,UAAU;AAAA,EAAG;AAGrD,MAAI,QAAQ,KAAK,gBAAgB,KAAK,cAAc,IAAI,sBAAsB,IAAI;AAClF,QAAM,MAAM,OAAO,SAAS,KAAK;AACjC,OAAK,oBAAoB,KAAK;AAC9B,OAAK,sBAAsB,KAAK;AAGhC,MAAI,QAAQ;AACZ,MAAI;AACF,YAAQ,IAAI,OAAO,SAAS,KAAK;AAAA,EACnC,SAAS,GAAG;AAAA,EAGZ;AAEA,SAAO,KAAK,YAAY,QAAQ,QAAQ,EAAC,SAAkB,OAAc,MAAY,CAAC;AACxF;AAMA,GAAG,UAAU,SAAS,OAAO,KAAK,gCAAgC;AAEhE,MAAI,kBAAkB,KAAK,QAAQ,eAAe,MAAM,QAAQ;AAKhE,MAAI,8BAA8B,kCAAkC,KAAK,MAAM,WAAW,KAAK,GAAG,MAAM;AAExG,MAAI,QAAQ,KAAK,KAAK,QAAQ,GAAG,WAAW;AAC5C,WAAS,IAAI,GAAG,IAAI,OAAO,OAAO,WAAW,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,KAAK,KAAK;AACxE,QAAI,OAAO,KAAK,MAAM,WAAW,KAAK,GAAG,GAAG,MAAO;AAEnD,QAAI,mBAAmB,SAAS,IAAI;AAClC,UAAI,6BAA6B;AAAE,aAAK,iBAAiB,KAAK,KAAK,mEAAmE;AAAA,MAAG;AACzI,UAAI,aAAa,IAAI;AAAE,aAAK,iBAAiB,KAAK,KAAK,kDAAkD;AAAA,MAAG;AAC5G,UAAI,MAAM,GAAG;AAAE,aAAK,iBAAiB,KAAK,KAAK,yDAAyD;AAAA,MAAG;AAC3G,iBAAW;AACX;AAAA,IACF;AAEA,QAAI,QAAQ,IAAI;AAAE,YAAM,OAAO,KAAK;AAAA,IAAI,WAC/B,QAAQ,IAAI;AAAE,YAAM,OAAO,KAAK;AAAA,IAAI,WACpC,QAAQ,MAAM,QAAQ,IAAI;AAAE,YAAM,OAAO;AAAA,IAAI,OACjD;AAAE,YAAM;AAAA,IAAU;AACvB,QAAI,OAAO,OAAO;AAAE;AAAA,IAAM;AAC1B,eAAW;AACX,YAAQ,QAAQ,QAAQ;AAAA,EAC1B;AAEA,MAAI,mBAAmB,aAAa,IAAI;AAAE,SAAK,iBAAiB,KAAK,MAAM,GAAG,wDAAwD;AAAA,EAAG;AACzI,MAAI,KAAK,QAAQ,SAAS,OAAO,QAAQ,KAAK,MAAM,UAAU,KAAK;AAAE,WAAO;AAAA,EAAK;AAEjF,SAAO;AACT;AAEA,SAAS,eAAe,KAAK,6BAA6B;AACxD,MAAI,6BAA6B;AAC/B,WAAO,SAAS,KAAK,CAAC;AAAA,EACxB;AAGA,SAAO,WAAW,IAAI,QAAQ,MAAM,EAAE,CAAC;AACzC;AAEA,SAAS,eAAe,KAAK;AAC3B,MAAI,OAAO,WAAW,YAAY;AAChC,WAAO;AAAA,EACT;AAGA,SAAO,OAAO,IAAI,QAAQ,MAAM,EAAE,CAAC;AACrC;AAEA,GAAG,kBAAkB,SAAS,OAAO;AACnC,MAAI,QAAQ,KAAK;AACjB,OAAK,OAAO;AACZ,MAAI,MAAM,KAAK,QAAQ,KAAK;AAC5B,MAAI,OAAO,MAAM;AAAE,SAAK,MAAM,KAAK,QAAQ,GAAG,8BAA8B,KAAK;AAAA,EAAG;AACpF,MAAI,KAAK,QAAQ,eAAe,MAAM,KAAK,MAAM,WAAW,KAAK,GAAG,MAAM,KAAK;AAC7E,UAAM,eAAe,KAAK,MAAM,MAAM,OAAO,KAAK,GAAG,CAAC;AACtD,MAAE,KAAK;AAAA,EACT,WAAW,kBAAkB,KAAK,kBAAkB,CAAC,GAAG;AAAE,SAAK,MAAM,KAAK,KAAK,kCAAkC;AAAA,EAAG;AACpH,SAAO,KAAK,YAAY,QAAQ,KAAK,GAAG;AAC1C;AAIA,GAAG,aAAa,SAAS,eAAe;AACtC,MAAI,QAAQ,KAAK;AACjB,MAAI,CAAC,iBAAiB,KAAK,QAAQ,IAAI,QAAW,IAAI,MAAM,MAAM;AAAE,SAAK,MAAM,OAAO,gBAAgB;AAAA,EAAG;AACzG,MAAI,QAAQ,KAAK,MAAM,SAAS,KAAK,KAAK,MAAM,WAAW,KAAK,MAAM;AACtE,MAAI,SAAS,KAAK,QAAQ;AAAE,SAAK,MAAM,OAAO,gBAAgB;AAAA,EAAG;AACjE,MAAI,OAAO,KAAK,MAAM,WAAW,KAAK,GAAG;AACzC,MAAI,CAAC,SAAS,CAAC,iBAAiB,KAAK,QAAQ,eAAe,MAAM,SAAS,KAAK;AAC9E,QAAI,QAAQ,eAAe,KAAK,MAAM,MAAM,OAAO,KAAK,GAAG,CAAC;AAC5D,MAAE,KAAK;AACP,QAAI,kBAAkB,KAAK,kBAAkB,CAAC,GAAG;AAAE,WAAK,MAAM,KAAK,KAAK,kCAAkC;AAAA,IAAG;AAC7G,WAAO,KAAK,YAAY,QAAQ,KAAK,KAAK;AAAA,EAC5C;AACA,MAAI,SAAS,OAAO,KAAK,KAAK,MAAM,MAAM,OAAO,KAAK,GAAG,CAAC,GAAG;AAAE,YAAQ;AAAA,EAAO;AAC9E,MAAI,SAAS,MAAM,CAAC,OAAO;AACzB,MAAE,KAAK;AACP,SAAK,QAAQ,EAAE;AACf,WAAO,KAAK,MAAM,WAAW,KAAK,GAAG;AAAA,EACvC;AACA,OAAK,SAAS,MAAM,SAAS,QAAQ,CAAC,OAAO;AAC3C,WAAO,KAAK,MAAM,WAAW,EAAE,KAAK,GAAG;AACvC,QAAI,SAAS,MAAM,SAAS,IAAI;AAAE,QAAE,KAAK;AAAA,IAAK;AAC9C,QAAI,KAAK,QAAQ,EAAE,MAAM,MAAM;AAAE,WAAK,MAAM,OAAO,gBAAgB;AAAA,IAAG;AAAA,EACxE;AACA,MAAI,kBAAkB,KAAK,kBAAkB,CAAC,GAAG;AAAE,SAAK,MAAM,KAAK,KAAK,kCAAkC;AAAA,EAAG;AAE7G,MAAI,MAAM,eAAe,KAAK,MAAM,MAAM,OAAO,KAAK,GAAG,GAAG,KAAK;AACjE,SAAO,KAAK,YAAY,QAAQ,KAAK,GAAG;AAC1C;AAIA,GAAG,gBAAgB,WAAW;AAC5B,MAAI,KAAK,KAAK,MAAM,WAAW,KAAK,GAAG,GAAG;AAE1C,MAAI,OAAO,KAAK;AACd,QAAI,KAAK,QAAQ,cAAc,GAAG;AAAE,WAAK,WAAW;AAAA,IAAG;AACvD,QAAI,UAAU,EAAE,KAAK;AACrB,WAAO,KAAK,YAAY,KAAK,MAAM,QAAQ,KAAK,KAAK,GAAG,IAAI,KAAK,GAAG;AACpE,MAAE,KAAK;AACP,QAAI,OAAO,SAAU;AAAE,WAAK,mBAAmB,SAAS,0BAA0B;AAAA,IAAG;AAAA,EACvF,OAAO;AACL,WAAO,KAAK,YAAY,CAAC;AAAA,EAC3B;AACA,SAAO;AACT;AAEA,GAAG,aAAa,SAAS,OAAO;AAC9B,MAAI,MAAM,IAAI,aAAa,EAAE,KAAK;AAClC,aAAS;AACP,QAAI,KAAK,OAAO,KAAK,MAAM,QAAQ;AAAE,WAAK,MAAM,KAAK,OAAO,8BAA8B;AAAA,IAAG;AAC7F,QAAI,KAAK,KAAK,MAAM,WAAW,KAAK,GAAG;AACvC,QAAI,OAAO,OAAO;AAAE;AAAA,IAAM;AAC1B,QAAI,OAAO,IAAI;AACb,aAAO,KAAK,MAAM,MAAM,YAAY,KAAK,GAAG;AAC5C,aAAO,KAAK,gBAAgB,KAAK;AACjC,mBAAa,KAAK;AAAA,IACpB,WAAW,OAAO,QAAU,OAAO,MAAQ;AACzC,UAAI,KAAK,QAAQ,cAAc,IAAI;AAAE,aAAK,MAAM,KAAK,OAAO,8BAA8B;AAAA,MAAG;AAC7F,QAAE,KAAK;AACP,UAAI,KAAK,QAAQ,WAAW;AAC1B,aAAK;AACL,aAAK,YAAY,KAAK;AAAA,MACxB;AAAA,IACF,OAAO;AACL,UAAI,UAAU,EAAE,GAAG;AAAE,aAAK,MAAM,KAAK,OAAO,8BAA8B;AAAA,MAAG;AAC7E,QAAE,KAAK;AAAA,IACT;AAAA,EACF;AACA,SAAO,KAAK,MAAM,MAAM,YAAY,KAAK,KAAK;AAC9C,SAAO,KAAK,YAAY,QAAQ,QAAQ,GAAG;AAC7C;AAIA,IAAI,gCAAgC,CAAC;AAErC,GAAG,uBAAuB,WAAW;AACnC,OAAK,oBAAoB;AACzB,MAAI;AACF,SAAK,cAAc;AAAA,EACrB,SAAS,KAAK;AACZ,QAAI,QAAQ,+BAA+B;AACzC,WAAK,yBAAyB;AAAA,IAChC,OAAO;AACL,YAAM;AAAA,IACR;AAAA,EACF;AAEA,OAAK,oBAAoB;AAC3B;AAEA,GAAG,qBAAqB,SAAS,UAAU,SAAS;AAClD,MAAI,KAAK,qBAAqB,KAAK,QAAQ,eAAe,GAAG;AAC3D,UAAM;AAAA,EACR,OAAO;AACL,SAAK,MAAM,UAAU,OAAO;AAAA,EAC9B;AACF;AAEA,GAAG,gBAAgB,WAAW;AAC5B,MAAI,MAAM,IAAI,aAAa,KAAK;AAChC,aAAS;AACP,QAAI,KAAK,OAAO,KAAK,MAAM,QAAQ;AAAE,WAAK,MAAM,KAAK,OAAO,uBAAuB;AAAA,IAAG;AACtF,QAAI,KAAK,KAAK,MAAM,WAAW,KAAK,GAAG;AACvC,QAAI,OAAO,MAAM,OAAO,MAAM,KAAK,MAAM,WAAW,KAAK,MAAM,CAAC,MAAM,KAAK;AACzE,UAAI,KAAK,QAAQ,KAAK,UAAU,KAAK,SAAS,QAAQ,YAAY,KAAK,SAAS,QAAQ,kBAAkB;AACxG,YAAI,OAAO,IAAI;AACb,eAAK,OAAO;AACZ,iBAAO,KAAK,YAAY,QAAQ,YAAY;AAAA,QAC9C,OAAO;AACL,YAAE,KAAK;AACP,iBAAO,KAAK,YAAY,QAAQ,SAAS;AAAA,QAC3C;AAAA,MACF;AACA,aAAO,KAAK,MAAM,MAAM,YAAY,KAAK,GAAG;AAC5C,aAAO,KAAK,YAAY,QAAQ,UAAU,GAAG;AAAA,IAC/C;AACA,QAAI,OAAO,IAAI;AACb,aAAO,KAAK,MAAM,MAAM,YAAY,KAAK,GAAG;AAC5C,aAAO,KAAK,gBAAgB,IAAI;AAChC,mBAAa,KAAK;AAAA,IACpB,WAAW,UAAU,EAAE,GAAG;AACxB,aAAO,KAAK,MAAM,MAAM,YAAY,KAAK,GAAG;AAC5C,QAAE,KAAK;AACP,cAAQ,IAAI;AAAA,QACZ,KAAK;AACH,cAAI,KAAK,MAAM,WAAW,KAAK,GAAG,MAAM,IAAI;AAAE,cAAE,KAAK;AAAA,UAAK;AAAA,QAC5D,KAAK;AACH,iBAAO;AACP;AAAA,QACF;AACE,iBAAO,OAAO,aAAa,EAAE;AAC7B;AAAA,MACF;AACA,UAAI,KAAK,QAAQ,WAAW;AAC1B,UAAE,KAAK;AACP,aAAK,YAAY,KAAK;AAAA,MACxB;AACA,mBAAa,KAAK;AAAA,IACpB,OAAO;AACL,QAAE,KAAK;AAAA,IACT;AAAA,EACF;AACF;AAGA,GAAG,2BAA2B,WAAW;AACvC,SAAO,KAAK,MAAM,KAAK,MAAM,QAAQ,KAAK,OAAO;AAC/C,YAAQ,KAAK,MAAM,KAAK,GAAG,GAAG;AAAA,MAC9B,KAAK;AACH,UAAE,KAAK;AACP;AAAA,MAEF,KAAK;AACH,YAAI,KAAK,MAAM,KAAK,MAAM,CAAC,MAAM,KAAK;AAAE;AAAA,QAAM;AAAA;AAAA,MAEhD,KAAK;AACH,eAAO,KAAK,YAAY,QAAQ,iBAAiB,KAAK,MAAM,MAAM,KAAK,OAAO,KAAK,GAAG,CAAC;AAAA,MAEzF,KAAK;AACH,YAAI,KAAK,MAAM,KAAK,MAAM,CAAC,MAAM,MAAM;AAAE,YAAE,KAAK;AAAA,QAAK;AAAA;AAAA,MAEvD,KAAK;AAAA,MAAM,KAAK;AAAA,MAAU,KAAK;AAC7B,UAAE,KAAK;AACP,aAAK,YAAY,KAAK,MAAM;AAC5B;AAAA,IACF;AAAA,EACF;AACA,OAAK,MAAM,KAAK,OAAO,uBAAuB;AAChD;AAIA,GAAG,kBAAkB,SAAS,YAAY;AACxC,MAAI,KAAK,KAAK,MAAM,WAAW,EAAE,KAAK,GAAG;AACzC,IAAE,KAAK;AACP,UAAQ,IAAI;AAAA,IACZ,KAAK;AAAK,aAAO;AAAA;AAAA,IACjB,KAAK;AAAK,aAAO;AAAA;AAAA,IACjB,KAAK;AAAK,aAAO,OAAO,aAAa,KAAK,YAAY,CAAC,CAAC;AAAA;AAAA,IACxD,KAAK;AAAK,aAAO,kBAAkB,KAAK,cAAc,CAAC;AAAA;AAAA,IACvD,KAAK;AAAK,aAAO;AAAA;AAAA,IACjB,KAAK;AAAI,aAAO;AAAA;AAAA,IAChB,KAAK;AAAK,aAAO;AAAA;AAAA,IACjB,KAAK;AAAK,aAAO;AAAA;AAAA,IACjB,KAAK;AAAI,UAAI,KAAK,MAAM,WAAW,KAAK,GAAG,MAAM,IAAI;AAAE,UAAE,KAAK;AAAA,MAAK;AAAA;AAAA,IACnE,KAAK;AACH,UAAI,KAAK,QAAQ,WAAW;AAAE,aAAK,YAAY,KAAK;AAAK,UAAE,KAAK;AAAA,MAAS;AACzE,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,UAAI,KAAK,QAAQ;AACf,aAAK;AAAA,UACH,KAAK,MAAM;AAAA,UACX;AAAA,QACF;AAAA,MACF;AACA,UAAI,YAAY;AACd,YAAI,UAAU,KAAK,MAAM;AAEzB,aAAK;AAAA,UACH;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACE,UAAI,MAAM,MAAM,MAAM,IAAI;AACxB,YAAI,WAAW,KAAK,MAAM,OAAO,KAAK,MAAM,GAAG,CAAC,EAAE,MAAM,SAAS,EAAE,CAAC;AACpE,YAAI,QAAQ,SAAS,UAAU,CAAC;AAChC,YAAI,QAAQ,KAAK;AACf,qBAAW,SAAS,MAAM,GAAG,EAAE;AAC/B,kBAAQ,SAAS,UAAU,CAAC;AAAA,QAC9B;AACA,aAAK,OAAO,SAAS,SAAS;AAC9B,aAAK,KAAK,MAAM,WAAW,KAAK,GAAG;AACnC,aAAK,aAAa,OAAO,OAAO,MAAM,OAAO,QAAQ,KAAK,UAAU,aAAa;AAC/E,eAAK;AAAA,YACH,KAAK,MAAM,IAAI,SAAS;AAAA,YACxB,aACI,qCACA;AAAA,UACN;AAAA,QACF;AACA,eAAO,OAAO,aAAa,KAAK;AAAA,MAClC;AACA,UAAI,UAAU,EAAE,GAAG;AAGjB,YAAI,KAAK,QAAQ,WAAW;AAAE,eAAK,YAAY,KAAK;AAAK,YAAE,KAAK;AAAA,QAAS;AACzE,eAAO;AAAA,MACT;AACA,aAAO,OAAO,aAAa,EAAE;AAAA,EAC/B;AACF;AAIA,GAAG,cAAc,SAAS,KAAK;AAC7B,MAAI,UAAU,KAAK;AACnB,MAAI,IAAI,KAAK,QAAQ,IAAI,GAAG;AAC5B,MAAI,MAAM,MAAM;AAAE,SAAK,mBAAmB,SAAS,+BAA+B;AAAA,EAAG;AACrF,SAAO;AACT;AAQA,GAAG,YAAY,WAAW;AACxB,OAAK,cAAc;AACnB,MAAI,OAAO,IAAI,QAAQ,MAAM,aAAa,KAAK;AAC/C,MAAI,SAAS,KAAK,QAAQ,eAAe;AACzC,SAAO,KAAK,MAAM,KAAK,MAAM,QAAQ;AACnC,QAAI,KAAK,KAAK,kBAAkB;AAChC,QAAI,iBAAiB,IAAI,MAAM,GAAG;AAChC,WAAK,OAAO,MAAM,QAAS,IAAI;AAAA,IACjC,WAAW,OAAO,IAAI;AACpB,WAAK,cAAc;AACnB,cAAQ,KAAK,MAAM,MAAM,YAAY,KAAK,GAAG;AAC7C,UAAI,WAAW,KAAK;AACpB,UAAI,KAAK,MAAM,WAAW,EAAE,KAAK,GAAG,MAAM,KACxC;AAAE,aAAK,mBAAmB,KAAK,KAAK,2CAA2C;AAAA,MAAG;AACpF,QAAE,KAAK;AACP,UAAI,MAAM,KAAK,cAAc;AAC7B,UAAI,EAAE,QAAQ,oBAAoB,kBAAkB,KAAK,MAAM,GAC7D;AAAE,aAAK,mBAAmB,UAAU,wBAAwB;AAAA,MAAG;AACjE,cAAQ,kBAAkB,GAAG;AAC7B,mBAAa,KAAK;AAAA,IACpB,OAAO;AACL;AAAA,IACF;AACA,YAAQ;AAAA,EACV;AACA,SAAO,OAAO,KAAK,MAAM,MAAM,YAAY,KAAK,GAAG;AACrD;AAKA,GAAG,WAAW,WAAW;AACvB,MAAI,OAAO,KAAK,UAAU;AAC1B,MAAI,OAAO,QAAQ;AACnB,MAAI,KAAK,SAAS,KAAK,IAAI,GAAG;AAC5B,WAAO,SAAS,IAAI;AAAA,EACtB;AACA,SAAO,KAAK,YAAY,MAAM,IAAI;AACpC;AAiBA,IAAI,UAAU;AAEd,OAAO,QAAQ;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAU;AAAA,EACV,cAAc;AAAA,EACd;AAAA,EACA,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAQA,SAASR,OAAM,OAAO,SAAS;AAC7B,SAAO,OAAO,MAAM,OAAO,OAAO;AACpC;AAMA,SAASS,mBAAkB,OAAO,KAAK,SAAS;AAC9C,SAAO,OAAO,kBAAkB,OAAO,KAAK,OAAO;AACrD;AAKA,SAASC,WAAU,OAAO,SAAS;AACjC,SAAO,OAAO,UAAU,OAAO,OAAO;AACxC;", "names": ["TokenType", "Position", "SourceLocation", "offset", "<PERSON><PERSON><PERSON>", "ref", "parse", "DestructuringErrors", "TokContext", "<PERSON><PERSON>", "Node", "BranchID", "RegExpValidationState", "current", "Token", "parseExpressionAt", "tokenizer"]}