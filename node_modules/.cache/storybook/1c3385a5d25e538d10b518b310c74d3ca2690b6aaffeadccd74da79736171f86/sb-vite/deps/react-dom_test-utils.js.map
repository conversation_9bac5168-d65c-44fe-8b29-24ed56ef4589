{"version": 3, "sources": ["../../../../../react-dom/cjs/react-dom-test-utils.development.js", "../../../../../react-dom/test-utils.js"], "sourcesContent": ["/**\n * @license React\n * react-dom-test-utils.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    var React = require(\"react\"),\n      didWarnAboutUsingAct = !1;\n    exports.act = function (callback) {\n      !1 === didWarnAboutUsingAct &&\n        ((didWarnAboutUsingAct = !0),\n        console.error(\n          \"`ReactDOMTestUtils.act` is deprecated in favor of `React.act`. Import `act` from `react` instead of `react-dom/test-utils`. See https://react.dev/warnings/react-dom-test-utils for more info.\"\n        ));\n      return React.act(callback);\n    };\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-dom-test-utils.production.js');\n} else {\n  module.exports = require('./cjs/react-dom-test-utils.development.js');\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AAWA,KACG,WAAY;AACX,UAAI,QAAQ,iBACV,uBAAuB;AACzB,cAAQ,MAAM,SAAU,UAAU;AAChC,kBAAO,yBACH,uBAAuB,MACzB,QAAQ;AAAA,UACN;AAAA,QACF;AACF,eAAO,MAAM,IAAI,QAAQ;AAAA,MAC3B;AAAA,IACF,GAAG;AAAA;AAAA;;;ACvBL;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;", "names": []}