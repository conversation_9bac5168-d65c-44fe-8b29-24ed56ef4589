import {
  require_baseIsEqual
} from "./chunk-7YG6QBUQ.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// node_modules/lodash/isEqual.js
var require_isEqual = __commonJS({
  "node_modules/lodash/isEqual.js"(exports, module) {
    var baseIsEqual = require_baseIsEqual();
    function isEqual(value, other) {
      return baseIsEqual(value, other);
    }
    module.exports = isEqual;
  }
});

export {
  require_isEqual
};
//# sourceMappingURL=chunk-GBX5BES6.js.map
