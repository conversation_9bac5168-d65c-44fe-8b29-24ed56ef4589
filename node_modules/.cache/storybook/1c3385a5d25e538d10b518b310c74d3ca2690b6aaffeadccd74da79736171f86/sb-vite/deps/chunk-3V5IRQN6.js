import {
  require_baseMerge,
  require_createAssigner
} from "./chunk-LMGR3DYQ.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// node_modules/lodash/merge.js
var require_merge = __commonJS({
  "node_modules/lodash/merge.js"(exports, module) {
    var baseMerge = require_baseMerge();
    var createAssigner = require_createAssigner();
    var merge = createAssigner(function(object, source, srcIndex) {
      baseMerge(object, source, srcIndex);
    });
    module.exports = merge;
  }
});

export {
  require_merge
};
//# sourceMappingURL=chunk-3V5IRQN6.js.map
