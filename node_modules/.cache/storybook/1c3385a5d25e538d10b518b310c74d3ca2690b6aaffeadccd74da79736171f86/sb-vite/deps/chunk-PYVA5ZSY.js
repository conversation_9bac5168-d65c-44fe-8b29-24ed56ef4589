import {
  require_baseFor
} from "./chunk-JVJYDJ5R.js";
import {
  require_keys
} from "./chunk-IWFWZYSF.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// node_modules/lodash/_baseForOwn.js
var require_baseForOwn = __commonJS({
  "node_modules/lodash/_baseForOwn.js"(exports, module) {
    var baseFor = require_baseFor();
    var keys = require_keys();
    function baseForOwn(object, iteratee) {
      return object && baseFor(object, iteratee, keys);
    }
    module.exports = baseForOwn;
  }
});

export {
  require_baseForOwn
};
//# sourceMappingURL=chunk-PYVA5ZSY.js.map
