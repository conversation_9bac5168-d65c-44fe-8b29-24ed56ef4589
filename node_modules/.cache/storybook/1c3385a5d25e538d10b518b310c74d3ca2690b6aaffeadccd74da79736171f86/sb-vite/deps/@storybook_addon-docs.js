import {
  <PERSON>s<PERSON><PERSON>er
} from "./chunk-Q4X5UN5Z.js";
import "./chunk-FEC7IGZM.js";
import "./chunk-EL7LIPAV.js";
import "./chunk-YHKAGQD2.js";
import {
  __export
} from "./chunk-MBPMIFF4.js";
import "./chunk-PGJ4O6RQ.js";
import "./chunk-N3ZF2DWA.js";
import "./chunk-JLBFQ2EK.js";
import "./chunk-PYKVDTIO.js";
import "./chunk-7ZSBYTHS.js";
import "./chunk-62AE6YUO.js";
import "./chunk-62Y44TTL.js";
import "./chunk-TTAPWIZ2.js";
import "./chunk-LHOSQLWN.js";
import "./chunk-AZZHOME7.js";
import "./chunk-XZMMCN3X.js";
import {
  require_preview_api
} from "./chunk-ZLWVYHQP.js";
import {
  __toESM
} from "./chunk-7D4SUZUM.js";

// node_modules/@storybook/addon-docs/dist/index.mjs
var import_preview_api = __toESM(require_preview_api(), 1);
var preview_exports = {};
__export(preview_exports, { parameters: () => parameters });
var excludeTags = Object.entries(globalThis.TAGS_OPTIONS ?? {}).reduce((acc, entry) => {
  let [tag, option] = entry;
  return option.excludeFromDocsStories && (acc[tag] = true), acc;
}, {});
var parameters = { docs: { renderer: async () => {
  let { DocsRenderer: DocsRenderer2 } = await import("./DocsRenderer-3PZUHFFL-33VHI6N2.js");
  return new DocsRenderer2();
}, stories: { filter: (story) => (story.tags || []).filter((tag) => excludeTags[tag]).length === 0 && !story.parameters.docs?.disable } } };
var index_default = () => (0, import_preview_api.definePreview)(preview_exports);
export {
  DocsRenderer,
  index_default as default
};
//# sourceMappingURL=@storybook_addon-docs.js.map
