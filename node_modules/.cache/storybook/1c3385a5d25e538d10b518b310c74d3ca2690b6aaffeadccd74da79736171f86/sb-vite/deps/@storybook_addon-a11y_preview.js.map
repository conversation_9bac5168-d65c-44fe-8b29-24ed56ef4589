{"version": 3, "sources": ["../../../../../@storybook/addon-a11y/dist/preview.mjs"], "sourcesContent": ["import { expect } from 'storybook/test';\nimport { ElementA11yParameterError } from 'storybook/internal/preview-errors';\nimport { global } from '@storybook/global';\nimport { addons, waitForAnimations } from 'storybook/preview-api';\n\nvar ADDON_ID=\"storybook/a11y\",PANEL_ID=`${ADDON_ID}/panel`;var RESULT=`${ADDON_ID}/result`,REQUEST=`${ADDON_ID}/request`,RUNNING=`${ADDON_ID}/running`,ERROR=`${ADDON_ID}/error`,MANUAL=`${ADDON_ID}/manual`,SELECT=`${ADDON_ID}/select`;var EVENTS={RESULT,REQUEST,RUNNING,ERROR,MANUAL,SELECT};var{document}=global,withLinkPaths=(results,storyId)=>{let pathname=document.location.pathname.replace(/iframe\\.html$/,\"\"),enhancedResults={...results};return [\"incomplete\",\"passes\",\"violations\"].forEach(key=>{Array.isArray(results[key])&&(enhancedResults[key]=results[key].map(result=>({...result,nodes:result.nodes.map((node,index)=>{let id=`${key}.${result.id}.${index+1}`,linkPath=`${pathname}?path=/story/${storyId}&addonPanel=${PANEL_ID}&a11ySelection=${id}`;return {id,...node,linkPath}})})));}),enhancedResults};var{document:document2}=global,channel=addons.getChannel(),DEFAULT_PARAMETERS={config:{},options:{}},DISABLED_RULES=[\"region\"],queue=[],isRunning=!1,runNext=async()=>{if(queue.length===0){isRunning=!1;return}isRunning=!0;let next=queue.shift();next&&await next(),runNext();},run=async(input=DEFAULT_PARAMETERS,storyId)=>{let axe=(await import('axe-core'))?.default||globalThis.axe,{config={},options={}}=input;if(input.element)throw new ElementA11yParameterError;let context={include:document2?.body,exclude:[\".sb-wrapper\",\"#storybook-docs\",\"#storybook-highlights-root\"]};if(input.context){let hasInclude=typeof input.context==\"object\"&&\"include\"in input.context&&input.context.include!==void 0,hasExclude=typeof input.context==\"object\"&&\"exclude\"in input.context&&input.context.exclude!==void 0;hasInclude?context.include=input.context.include:!hasInclude&&!hasExclude&&(context.include=input.context),hasExclude&&(context.exclude=context.exclude.concat(input.context.exclude));}axe.reset();let configWithDefault={...config,rules:[...DISABLED_RULES.map(id=>({id,enabled:!1})),...config?.rules??[]]};return axe.configure(configWithDefault),new Promise((resolve,reject)=>{let highlightsRoot=document2?.getElementById(\"storybook-highlights-root\");highlightsRoot&&(highlightsRoot.style.display=\"none\");let task=async()=>{try{let result=await axe.run(context,options),resultWithLinks=withLinkPaths(result,storyId);resolve(resultWithLinks);}catch(error){reject(error);}};queue.push(task),isRunning||runNext(),highlightsRoot&&(highlightsRoot.style.display=\"\");})};channel.on(EVENTS.MANUAL,async(storyId,input=DEFAULT_PARAMETERS)=>{try{await waitForAnimations();let result=await run(input,storyId),resultJson=JSON.parse(JSON.stringify(result));channel.emit(EVENTS.RESULT,resultJson,storyId);}catch(error){channel.emit(EVENTS.ERROR,error);}});function getIsVitestStandaloneRun(){try{return import.meta.env.VITEST_STORYBOOK===\"false\"}catch{return !1}}var vitestMatchersExtended=!1,afterEach=async({id:storyId,reporting,parameters:parameters2,globals,viewMode})=>{let a11yParameter=parameters2.a11y,a11yGlobals=globals.a11y,shouldRunEnvironmentIndependent=a11yParameter?.disable!==!0&&a11yParameter?.test!==\"off\"&&a11yGlobals?.manual!==!0,getMode=()=>{switch(a11yParameter?.test){case\"todo\":return \"warning\";case\"error\":default:return \"failed\"}};if(shouldRunEnvironmentIndependent&&viewMode===\"story\")try{let result=await run(a11yParameter,storyId);if(result){let hasViolations=(result?.violations.length??0)>0;if(reporting.addReport({type:\"a11y\",version:1,result,status:hasViolations?getMode():\"passed\"}),getIsVitestStandaloneRun()&&hasViolations&&getMode()===\"failed\"){if(!vitestMatchersExtended){let{toHaveNoViolations}=await import('./matchers-7Z3WT2CE.mjs');expect.extend({toHaveNoViolations}),vitestMatchersExtended=!0;}expect(result).toHaveNoViolations();}}}catch(e){if(reporting.addReport({type:\"a11y\",version:1,result:{error:e},status:\"failed\"}),getIsVitestStandaloneRun())throw e}},initialGlobals={a11y:{manual:!1}},parameters={a11y:{test:\"todo\"}};\n\nexport { afterEach, initialGlobals, parameters };\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,kBAAuB;AACvB,4BAA0C;AAC1C,oBAAuB;AACvB,yBAA0C;AAE1C,IAAI,WAAS;AAAb,IAA8B,WAAS,GAAG,QAAQ;AAAS,IAAI,SAAO,GAAG,QAAQ;AAAtB,IAAgC,UAAQ,GAAG,QAAQ;AAAnD,IAA8D,UAAQ,GAAG,QAAQ;AAAjF,IAA4F,QAAM,GAAG,QAAQ;AAA7G,IAAsH,SAAO,GAAG,QAAQ;AAAxI,IAAkJ,SAAO,GAAG,QAAQ;AAAU,IAAI,SAAO,EAAC,QAAO,SAAQ,SAAQ,OAAM,QAAO,OAAM;AAAE,IAAG,EAAC,SAAQ,IAAE;AAAd,IAAqB,gBAAc,CAAC,SAAQ,YAAU;AAAC,MAAI,WAAS,SAAS,SAAS,SAAS,QAAQ,iBAAgB,EAAE,GAAE,kBAAgB,EAAC,GAAG,QAAO;AAAE,SAAO,CAAC,cAAa,UAAS,YAAY,EAAE,QAAQ,SAAK;AAAC,UAAM,QAAQ,QAAQ,GAAG,CAAC,MAAI,gBAAgB,GAAG,IAAE,QAAQ,GAAG,EAAE,IAAI,aAAS,EAAC,GAAG,QAAO,OAAM,OAAO,MAAM,IAAI,CAAC,MAAK,UAAQ;AAAC,UAAI,KAAG,GAAG,GAAG,IAAI,OAAO,EAAE,IAAI,QAAM,CAAC,IAAG,WAAS,GAAG,QAAQ,gBAAgB,OAAO,eAAe,QAAQ,kBAAkB,EAAE;AAAG,aAAO,EAAC,IAAG,GAAG,MAAK,SAAQ;AAAA,IAAC,CAAC,EAAC,EAAE;AAAA,EAAG,CAAC,GAAE;AAAe;AAAE,IAAG,EAAC,UAAS,UAAS,IAAE;AAAxB,IAA+B,UAAQ,0BAAO,WAAW;AAAzD,IAA2D,qBAAmB,EAAC,QAAO,CAAC,GAAE,SAAQ,CAAC,EAAC;AAAnG,IAAqG,iBAAe,CAAC,QAAQ;AAA7H,IAA+H,QAAM,CAAC;AAAtI,IAAwI,YAAU;AAAlJ,IAAqJ,UAAQ,YAAS;AAAC,MAAG,MAAM,WAAS,GAAE;AAAC,gBAAU;AAAG;AAAA,EAAM;AAAC,cAAU;AAAG,MAAI,OAAK,MAAM,MAAM;AAAE,UAAM,MAAM,KAAK,GAAE,QAAQ;AAAE;AAAjR,IAAmR,MAAI,OAAM,QAAM,oBAAmB,YAAU;AAAC,MAAI,OAAK,MAAM,OAAO,eAAU,IAAI,WAAS,WAAW,KAAI,EAAC,SAAO,CAAC,GAAE,UAAQ,CAAC,EAAC,IAAE;AAAM,MAAG,MAAM,QAAQ,OAAM,IAAI;AAA0B,MAAI,UAAQ,EAAC,SAAQ,WAAW,MAAK,SAAQ,CAAC,eAAc,mBAAkB,4BAA4B,EAAC;AAAE,MAAG,MAAM,SAAQ;AAAC,QAAI,aAAW,OAAO,MAAM,WAAS,YAAU,aAAY,MAAM,WAAS,MAAM,QAAQ,YAAU,QAAO,aAAW,OAAO,MAAM,WAAS,YAAU,aAAY,MAAM,WAAS,MAAM,QAAQ,YAAU;AAAO,iBAAW,QAAQ,UAAQ,MAAM,QAAQ,UAAQ,CAAC,cAAY,CAAC,eAAa,QAAQ,UAAQ,MAAM,UAAS,eAAa,QAAQ,UAAQ,QAAQ,QAAQ,OAAO,MAAM,QAAQ,OAAO;AAAA,EAAG;AAAC,MAAI,MAAM;AAAE,MAAI,oBAAkB,EAAC,GAAG,QAAO,OAAM,CAAC,GAAG,eAAe,IAAI,SAAK,EAAC,IAAG,SAAQ,MAAE,EAAE,GAAE,GAAG,QAAQ,SAAO,CAAC,CAAC,EAAC;AAAE,SAAO,IAAI,UAAU,iBAAiB,GAAE,IAAI,QAAQ,CAAC,SAAQ,WAAS;AAAC,QAAI,iBAAe,WAAW,eAAe,2BAA2B;AAAE,uBAAiB,eAAe,MAAM,UAAQ;AAAQ,QAAI,OAAK,YAAS;AAAC,UAAG;AAAC,YAAI,SAAO,MAAM,IAAI,IAAI,SAAQ,OAAO,GAAE,kBAAgB,cAAc,QAAO,OAAO;AAAE,gBAAQ,eAAe;AAAA,MAAE,SAAO,OAAM;AAAC,eAAO,KAAK;AAAA,MAAE;AAAA,IAAC;AAAE,UAAM,KAAK,IAAI,GAAE,aAAW,QAAQ,GAAE,mBAAiB,eAAe,MAAM,UAAQ;AAAA,EAAI,CAAC;AAAC;AAAE,QAAQ,GAAG,OAAO,QAAO,OAAM,SAAQ,QAAM,uBAAqB;AAAC,MAAG;AAAC,cAAM,sCAAkB;AAAE,QAAI,SAAO,MAAM,IAAI,OAAM,OAAO,GAAE,aAAW,KAAK,MAAM,KAAK,UAAU,MAAM,CAAC;AAAE,YAAQ,KAAK,OAAO,QAAO,YAAW,OAAO;AAAA,EAAE,SAAO,OAAM;AAAC,YAAQ,KAAK,OAAO,OAAM,KAAK;AAAA,EAAE;AAAC,CAAC;AAAE,SAAS,2BAA0B;AAAC,MAAG;AAAC,WAAO,YAAY,IAAI,qBAAmB;AAAA,EAAO,QAAM;AAAC,WAAO;AAAA,EAAE;AAAC;AAAC,IAAI,yBAAuB;AAA3B,IAA8B,YAAU,OAAM,EAAC,IAAG,SAAQ,WAAU,YAAW,aAAY,SAAQ,SAAQ,MAAI;AAAC,MAAI,gBAAc,YAAY,MAAK,cAAY,QAAQ,MAAK,kCAAgC,eAAe,YAAU,QAAI,eAAe,SAAO,SAAO,aAAa,WAAS,MAAG,UAAQ,MAAI;AAAC,YAAO,eAAe,MAAK;AAAA,MAAC,KAAI;AAAO,eAAO;AAAA,MAAU,KAAI;AAAA,MAAQ;AAAQ,eAAO;AAAA,IAAQ;AAAA,EAAC;AAAE,MAAG,mCAAiC,aAAW,QAAQ,KAAG;AAAC,QAAI,SAAO,MAAM,IAAI,eAAc,OAAO;AAAE,QAAG,QAAO;AAAC,UAAI,iBAAe,QAAQ,WAAW,UAAQ,KAAG;AAAE,UAAG,UAAU,UAAU,EAAC,MAAK,QAAO,SAAQ,GAAE,QAAO,QAAO,gBAAc,QAAQ,IAAE,SAAQ,CAAC,GAAE,yBAAyB,KAAG,iBAAe,QAAQ,MAAI,UAAS;AAAC,YAAG,CAAC,wBAAuB;AAAC,cAAG,EAAC,mBAAkB,IAAE,MAAM,OAAO,iCAAyB;AAAE,6BAAO,OAAO,EAAC,mBAAkB,CAAC,GAAE,yBAAuB;AAAA,QAAG;AAAC,gCAAO,MAAM,EAAE,mBAAmB;AAAA,MAAE;AAAA,IAAC;AAAA,EAAC,SAAO,GAAE;AAAC,QAAG,UAAU,UAAU,EAAC,MAAK,QAAO,SAAQ,GAAE,QAAO,EAAC,OAAM,EAAC,GAAE,QAAO,SAAQ,CAAC,GAAE,yBAAyB,EAAE,OAAM;AAAA,EAAC;AAAC;AAA9gC,IAAghC,iBAAe,EAAC,MAAK,EAAC,QAAO,MAAE,EAAC;AAAhjC,IAAkjC,aAAW,EAAC,MAAK,EAAC,MAAK,OAAM,EAAC;", "names": []}