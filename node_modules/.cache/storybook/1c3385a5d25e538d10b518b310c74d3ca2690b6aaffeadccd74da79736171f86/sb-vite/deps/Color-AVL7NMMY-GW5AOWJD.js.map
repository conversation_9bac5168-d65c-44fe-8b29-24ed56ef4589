{"version": 3, "sources": ["../../../../../@storybook/addon-docs/dist/Color-AVL7NMMY.mjs"], "sourcesContent": ["import { debounce, getControlId } from './chunk-SPFYY5GD.mjs';\nimport { __commonJS, __toESM } from './chunk-QUZPS4B6.mjs';\nimport e, { useRef, useMemo, useEffect, useCallback, useState, useLayoutEffect } from 'react';\nimport { WithTooltip, TooltipNote, Form } from 'storybook/internal/components';\nimport { MarkupIcon } from '@storybook/icons';\nimport { styled } from 'storybook/theming';\n\nvar require_color_name=__commonJS({\"../../node_modules/color-name/index.js\"(exports,module){module.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]};}});var require_conversions=__commonJS({\"../../node_modules/color-convert/conversions.js\"(exports,module){var cssKeywords=require_color_name(),reverseKeywords={};for(let key of Object.keys(cssKeywords))reverseKeywords[cssKeywords[key]]=key;var convert2={rgb:{channels:3,labels:\"rgb\"},hsl:{channels:3,labels:\"hsl\"},hsv:{channels:3,labels:\"hsv\"},hwb:{channels:3,labels:\"hwb\"},cmyk:{channels:4,labels:\"cmyk\"},xyz:{channels:3,labels:\"xyz\"},lab:{channels:3,labels:\"lab\"},lch:{channels:3,labels:\"lch\"},hex:{channels:1,labels:[\"hex\"]},keyword:{channels:1,labels:[\"keyword\"]},ansi16:{channels:1,labels:[\"ansi16\"]},ansi256:{channels:1,labels:[\"ansi256\"]},hcg:{channels:3,labels:[\"h\",\"c\",\"g\"]},apple:{channels:3,labels:[\"r16\",\"g16\",\"b16\"]},gray:{channels:1,labels:[\"gray\"]}};module.exports=convert2;for(let model of Object.keys(convert2)){if(!(\"channels\"in convert2[model]))throw new Error(\"missing channels property: \"+model);if(!(\"labels\"in convert2[model]))throw new Error(\"missing channel labels property: \"+model);if(convert2[model].labels.length!==convert2[model].channels)throw new Error(\"channel and label counts mismatch: \"+model);let{channels,labels}=convert2[model];delete convert2[model].channels,delete convert2[model].labels,Object.defineProperty(convert2[model],\"channels\",{value:channels}),Object.defineProperty(convert2[model],\"labels\",{value:labels});}convert2.rgb.hsl=function(rgb){let r2=rgb[0]/255,g2=rgb[1]/255,b2=rgb[2]/255,min=Math.min(r2,g2,b2),max=Math.max(r2,g2,b2),delta=max-min,h2,s2;max===min?h2=0:r2===max?h2=(g2-b2)/delta:g2===max?h2=2+(b2-r2)/delta:b2===max&&(h2=4+(r2-g2)/delta),h2=Math.min(h2*60,360),h2<0&&(h2+=360);let l2=(min+max)/2;return max===min?s2=0:l2<=.5?s2=delta/(max+min):s2=delta/(2-max-min),[h2,s2*100,l2*100]};convert2.rgb.hsv=function(rgb){let rdif,gdif,bdif,h2,s2,r2=rgb[0]/255,g2=rgb[1]/255,b2=rgb[2]/255,v2=Math.max(r2,g2,b2),diff=v2-Math.min(r2,g2,b2),diffc=function(c2){return (v2-c2)/6/diff+1/2};return diff===0?(h2=0,s2=0):(s2=diff/v2,rdif=diffc(r2),gdif=diffc(g2),bdif=diffc(b2),r2===v2?h2=bdif-gdif:g2===v2?h2=1/3+rdif-bdif:b2===v2&&(h2=2/3+gdif-rdif),h2<0?h2+=1:h2>1&&(h2-=1)),[h2*360,s2*100,v2*100]};convert2.rgb.hwb=function(rgb){let r2=rgb[0],g2=rgb[1],b2=rgb[2],h2=convert2.rgb.hsl(rgb)[0],w2=1/255*Math.min(r2,Math.min(g2,b2));return b2=1-1/255*Math.max(r2,Math.max(g2,b2)),[h2,w2*100,b2*100]};convert2.rgb.cmyk=function(rgb){let r2=rgb[0]/255,g2=rgb[1]/255,b2=rgb[2]/255,k2=Math.min(1-r2,1-g2,1-b2),c2=(1-r2-k2)/(1-k2)||0,m2=(1-g2-k2)/(1-k2)||0,y2=(1-b2-k2)/(1-k2)||0;return [c2*100,m2*100,y2*100,k2*100]};function comparativeDistance(x2,y2){return (x2[0]-y2[0])**2+(x2[1]-y2[1])**2+(x2[2]-y2[2])**2}convert2.rgb.keyword=function(rgb){let reversed=reverseKeywords[rgb];if(reversed)return reversed;let currentClosestDistance=1/0,currentClosestKeyword;for(let keyword of Object.keys(cssKeywords)){let value=cssKeywords[keyword],distance=comparativeDistance(rgb,value);distance<currentClosestDistance&&(currentClosestDistance=distance,currentClosestKeyword=keyword);}return currentClosestKeyword};convert2.keyword.rgb=function(keyword){return cssKeywords[keyword]};convert2.rgb.xyz=function(rgb){let r2=rgb[0]/255,g2=rgb[1]/255,b2=rgb[2]/255;r2=r2>.04045?((r2+.055)/1.055)**2.4:r2/12.92,g2=g2>.04045?((g2+.055)/1.055)**2.4:g2/12.92,b2=b2>.04045?((b2+.055)/1.055)**2.4:b2/12.92;let x2=r2*.4124+g2*.3576+b2*.1805,y2=r2*.2126+g2*.7152+b2*.0722,z2=r2*.0193+g2*.1192+b2*.9505;return [x2*100,y2*100,z2*100]};convert2.rgb.lab=function(rgb){let xyz=convert2.rgb.xyz(rgb),x2=xyz[0],y2=xyz[1],z2=xyz[2];x2/=95.047,y2/=100,z2/=108.883,x2=x2>.008856?x2**(1/3):7.787*x2+16/116,y2=y2>.008856?y2**(1/3):7.787*y2+16/116,z2=z2>.008856?z2**(1/3):7.787*z2+16/116;let l2=116*y2-16,a2=500*(x2-y2),b2=200*(y2-z2);return [l2,a2,b2]};convert2.hsl.rgb=function(hsl){let h2=hsl[0]/360,s2=hsl[1]/100,l2=hsl[2]/100,t2,t3,val;if(s2===0)return val=l2*255,[val,val,val];l2<.5?t2=l2*(1+s2):t2=l2+s2-l2*s2;let t1=2*l2-t2,rgb=[0,0,0];for(let i2=0;i2<3;i2++)t3=h2+1/3*-(i2-1),t3<0&&t3++,t3>1&&t3--,6*t3<1?val=t1+(t2-t1)*6*t3:2*t3<1?val=t2:3*t3<2?val=t1+(t2-t1)*(2/3-t3)*6:val=t1,rgb[i2]=val*255;return rgb};convert2.hsl.hsv=function(hsl){let h2=hsl[0],s2=hsl[1]/100,l2=hsl[2]/100,smin=s2,lmin=Math.max(l2,.01);l2*=2,s2*=l2<=1?l2:2-l2,smin*=lmin<=1?lmin:2-lmin;let v2=(l2+s2)/2,sv=l2===0?2*smin/(lmin+smin):2*s2/(l2+s2);return [h2,sv*100,v2*100]};convert2.hsv.rgb=function(hsv){let h2=hsv[0]/60,s2=hsv[1]/100,v2=hsv[2]/100,hi=Math.floor(h2)%6,f2=h2-Math.floor(h2),p2=255*v2*(1-s2),q2=255*v2*(1-s2*f2),t2=255*v2*(1-s2*(1-f2));switch(v2*=255,hi){case 0:return [v2,t2,p2];case 1:return [q2,v2,p2];case 2:return [p2,v2,t2];case 3:return [p2,q2,v2];case 4:return [t2,p2,v2];case 5:return [v2,p2,q2]}};convert2.hsv.hsl=function(hsv){let h2=hsv[0],s2=hsv[1]/100,v2=hsv[2]/100,vmin=Math.max(v2,.01),sl,l2;l2=(2-s2)*v2;let lmin=(2-s2)*vmin;return sl=s2*vmin,sl/=lmin<=1?lmin:2-lmin,sl=sl||0,l2/=2,[h2,sl*100,l2*100]};convert2.hwb.rgb=function(hwb){let h2=hwb[0]/360,wh=hwb[1]/100,bl=hwb[2]/100,ratio=wh+bl,f2;ratio>1&&(wh/=ratio,bl/=ratio);let i2=Math.floor(6*h2),v2=1-bl;f2=6*h2-i2,(i2&1)!==0&&(f2=1-f2);let n2=wh+f2*(v2-wh),r2,g2,b2;switch(i2){default:case 6:case 0:r2=v2,g2=n2,b2=wh;break;case 1:r2=n2,g2=v2,b2=wh;break;case 2:r2=wh,g2=v2,b2=n2;break;case 3:r2=wh,g2=n2,b2=v2;break;case 4:r2=n2,g2=wh,b2=v2;break;case 5:r2=v2,g2=wh,b2=n2;break}return [r2*255,g2*255,b2*255]};convert2.cmyk.rgb=function(cmyk){let c2=cmyk[0]/100,m2=cmyk[1]/100,y2=cmyk[2]/100,k2=cmyk[3]/100,r2=1-Math.min(1,c2*(1-k2)+k2),g2=1-Math.min(1,m2*(1-k2)+k2),b2=1-Math.min(1,y2*(1-k2)+k2);return [r2*255,g2*255,b2*255]};convert2.xyz.rgb=function(xyz){let x2=xyz[0]/100,y2=xyz[1]/100,z2=xyz[2]/100,r2,g2,b2;return r2=x2*3.2406+y2*-1.5372+z2*-.4986,g2=x2*-.9689+y2*1.8758+z2*.0415,b2=x2*.0557+y2*-.204+z2*1.057,r2=r2>.0031308?1.055*r2**(1/2.4)-.055:r2*12.92,g2=g2>.0031308?1.055*g2**(1/2.4)-.055:g2*12.92,b2=b2>.0031308?1.055*b2**(1/2.4)-.055:b2*12.92,r2=Math.min(Math.max(0,r2),1),g2=Math.min(Math.max(0,g2),1),b2=Math.min(Math.max(0,b2),1),[r2*255,g2*255,b2*255]};convert2.xyz.lab=function(xyz){let x2=xyz[0],y2=xyz[1],z2=xyz[2];x2/=95.047,y2/=100,z2/=108.883,x2=x2>.008856?x2**(1/3):7.787*x2+16/116,y2=y2>.008856?y2**(1/3):7.787*y2+16/116,z2=z2>.008856?z2**(1/3):7.787*z2+16/116;let l2=116*y2-16,a2=500*(x2-y2),b2=200*(y2-z2);return [l2,a2,b2]};convert2.lab.xyz=function(lab){let l2=lab[0],a2=lab[1],b2=lab[2],x2,y2,z2;y2=(l2+16)/116,x2=a2/500+y2,z2=y2-b2/200;let y22=y2**3,x22=x2**3,z22=z2**3;return y2=y22>.008856?y22:(y2-16/116)/7.787,x2=x22>.008856?x22:(x2-16/116)/7.787,z2=z22>.008856?z22:(z2-16/116)/7.787,x2*=95.047,y2*=100,z2*=108.883,[x2,y2,z2]};convert2.lab.lch=function(lab){let l2=lab[0],a2=lab[1],b2=lab[2],h2;h2=Math.atan2(b2,a2)*360/2/Math.PI,h2<0&&(h2+=360);let c2=Math.sqrt(a2*a2+b2*b2);return [l2,c2,h2]};convert2.lch.lab=function(lch){let l2=lch[0],c2=lch[1],hr=lch[2]/360*2*Math.PI,a2=c2*Math.cos(hr),b2=c2*Math.sin(hr);return [l2,a2,b2]};convert2.rgb.ansi16=function(args,saturation=null){let[r2,g2,b2]=args,value=saturation===null?convert2.rgb.hsv(args)[2]:saturation;if(value=Math.round(value/50),value===0)return 30;let ansi=30+(Math.round(b2/255)<<2|Math.round(g2/255)<<1|Math.round(r2/255));return value===2&&(ansi+=60),ansi};convert2.hsv.ansi16=function(args){return convert2.rgb.ansi16(convert2.hsv.rgb(args),args[2])};convert2.rgb.ansi256=function(args){let r2=args[0],g2=args[1],b2=args[2];return r2===g2&&g2===b2?r2<8?16:r2>248?231:Math.round((r2-8)/247*24)+232:16+36*Math.round(r2/255*5)+6*Math.round(g2/255*5)+Math.round(b2/255*5)};convert2.ansi16.rgb=function(args){let color=args%10;if(color===0||color===7)return args>50&&(color+=3.5),color=color/10.5*255,[color,color,color];let mult=(~~(args>50)+1)*.5,r2=(color&1)*mult*255,g2=(color>>1&1)*mult*255,b2=(color>>2&1)*mult*255;return [r2,g2,b2]};convert2.ansi256.rgb=function(args){if(args>=232){let c2=(args-232)*10+8;return [c2,c2,c2]}args-=16;let rem,r2=Math.floor(args/36)/5*255,g2=Math.floor((rem=args%36)/6)/5*255,b2=rem%6/5*255;return [r2,g2,b2]};convert2.rgb.hex=function(args){let string=(((Math.round(args[0])&255)<<16)+((Math.round(args[1])&255)<<8)+(Math.round(args[2])&255)).toString(16).toUpperCase();return \"000000\".substring(string.length)+string};convert2.hex.rgb=function(args){let match=args.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!match)return [0,0,0];let colorString=match[0];match[0].length===3&&(colorString=colorString.split(\"\").map(char=>char+char).join(\"\"));let integer=parseInt(colorString,16),r2=integer>>16&255,g2=integer>>8&255,b2=integer&255;return [r2,g2,b2]};convert2.rgb.hcg=function(rgb){let r2=rgb[0]/255,g2=rgb[1]/255,b2=rgb[2]/255,max=Math.max(Math.max(r2,g2),b2),min=Math.min(Math.min(r2,g2),b2),chroma=max-min,grayscale,hue;return chroma<1?grayscale=min/(1-chroma):grayscale=0,chroma<=0?hue=0:max===r2?hue=(g2-b2)/chroma%6:max===g2?hue=2+(b2-r2)/chroma:hue=4+(r2-g2)/chroma,hue/=6,hue%=1,[hue*360,chroma*100,grayscale*100]};convert2.hsl.hcg=function(hsl){let s2=hsl[1]/100,l2=hsl[2]/100,c2=l2<.5?2*s2*l2:2*s2*(1-l2),f2=0;return c2<1&&(f2=(l2-.5*c2)/(1-c2)),[hsl[0],c2*100,f2*100]};convert2.hsv.hcg=function(hsv){let s2=hsv[1]/100,v2=hsv[2]/100,c2=s2*v2,f2=0;return c2<1&&(f2=(v2-c2)/(1-c2)),[hsv[0],c2*100,f2*100]};convert2.hcg.rgb=function(hcg){let h2=hcg[0]/360,c2=hcg[1]/100,g2=hcg[2]/100;if(c2===0)return [g2*255,g2*255,g2*255];let pure=[0,0,0],hi=h2%1*6,v2=hi%1,w2=1-v2,mg=0;switch(Math.floor(hi)){case 0:pure[0]=1,pure[1]=v2,pure[2]=0;break;case 1:pure[0]=w2,pure[1]=1,pure[2]=0;break;case 2:pure[0]=0,pure[1]=1,pure[2]=v2;break;case 3:pure[0]=0,pure[1]=w2,pure[2]=1;break;case 4:pure[0]=v2,pure[1]=0,pure[2]=1;break;default:pure[0]=1,pure[1]=0,pure[2]=w2;}return mg=(1-c2)*g2,[(c2*pure[0]+mg)*255,(c2*pure[1]+mg)*255,(c2*pure[2]+mg)*255]};convert2.hcg.hsv=function(hcg){let c2=hcg[1]/100,g2=hcg[2]/100,v2=c2+g2*(1-c2),f2=0;return v2>0&&(f2=c2/v2),[hcg[0],f2*100,v2*100]};convert2.hcg.hsl=function(hcg){let c2=hcg[1]/100,l2=hcg[2]/100*(1-c2)+.5*c2,s2=0;return l2>0&&l2<.5?s2=c2/(2*l2):l2>=.5&&l2<1&&(s2=c2/(2*(1-l2))),[hcg[0],s2*100,l2*100]};convert2.hcg.hwb=function(hcg){let c2=hcg[1]/100,g2=hcg[2]/100,v2=c2+g2*(1-c2);return [hcg[0],(v2-c2)*100,(1-v2)*100]};convert2.hwb.hcg=function(hwb){let w2=hwb[1]/100,v2=1-hwb[2]/100,c2=v2-w2,g2=0;return c2<1&&(g2=(v2-c2)/(1-c2)),[hwb[0],c2*100,g2*100]};convert2.apple.rgb=function(apple){return [apple[0]/65535*255,apple[1]/65535*255,apple[2]/65535*255]};convert2.rgb.apple=function(rgb){return [rgb[0]/255*65535,rgb[1]/255*65535,rgb[2]/255*65535]};convert2.gray.rgb=function(args){return [args[0]/100*255,args[0]/100*255,args[0]/100*255]};convert2.gray.hsl=function(args){return [0,0,args[0]]};convert2.gray.hsv=convert2.gray.hsl;convert2.gray.hwb=function(gray){return [0,100,gray[0]]};convert2.gray.cmyk=function(gray){return [0,0,0,gray[0]]};convert2.gray.lab=function(gray){return [gray[0],0,0]};convert2.gray.hex=function(gray){let val=Math.round(gray[0]/100*255)&255,string=((val<<16)+(val<<8)+val).toString(16).toUpperCase();return \"000000\".substring(string.length)+string};convert2.rgb.gray=function(rgb){return [(rgb[0]+rgb[1]+rgb[2])/3/255*100]};}});var require_route=__commonJS({\"../../node_modules/color-convert/route.js\"(exports,module){var conversions=require_conversions();function buildGraph(){let graph={},models=Object.keys(conversions);for(let len=models.length,i2=0;i2<len;i2++)graph[models[i2]]={distance:-1,parent:null};return graph}function deriveBFS(fromModel){let graph=buildGraph(),queue=[fromModel];for(graph[fromModel].distance=0;queue.length;){let current=queue.pop(),adjacents=Object.keys(conversions[current]);for(let len=adjacents.length,i2=0;i2<len;i2++){let adjacent=adjacents[i2],node=graph[adjacent];node.distance===-1&&(node.distance=graph[current].distance+1,node.parent=current,queue.unshift(adjacent));}}return graph}function link(from,to){return function(args){return to(from(args))}}function wrapConversion(toModel,graph){let path=[graph[toModel].parent,toModel],fn=conversions[graph[toModel].parent][toModel],cur=graph[toModel].parent;for(;graph[cur].parent;)path.unshift(graph[cur].parent),fn=link(conversions[graph[cur].parent][cur],fn),cur=graph[cur].parent;return fn.conversion=path,fn}module.exports=function(fromModel){let graph=deriveBFS(fromModel),conversion={},models=Object.keys(graph);for(let len=models.length,i2=0;i2<len;i2++){let toModel=models[i2];graph[toModel].parent!==null&&(conversion[toModel]=wrapConversion(toModel,graph));}return conversion};}});var require_color_convert=__commonJS({\"../../node_modules/color-convert/index.js\"(exports,module){var conversions=require_conversions(),route=require_route(),convert2={},models=Object.keys(conversions);function wrapRaw(fn){let wrappedFn=function(...args){let arg0=args[0];return arg0==null?arg0:(arg0.length>1&&(args=arg0),fn(args))};return \"conversion\"in fn&&(wrappedFn.conversion=fn.conversion),wrappedFn}function wrapRounded(fn){let wrappedFn=function(...args){let arg0=args[0];if(arg0==null)return arg0;arg0.length>1&&(args=arg0);let result=fn(args);if(typeof result==\"object\")for(let len=result.length,i2=0;i2<len;i2++)result[i2]=Math.round(result[i2]);return result};return \"conversion\"in fn&&(wrappedFn.conversion=fn.conversion),wrappedFn}models.forEach(fromModel=>{convert2[fromModel]={},Object.defineProperty(convert2[fromModel],\"channels\",{value:conversions[fromModel].channels}),Object.defineProperty(convert2[fromModel],\"labels\",{value:conversions[fromModel].labels});let routes=route(fromModel);Object.keys(routes).forEach(toModel=>{let fn=routes[toModel];convert2[fromModel][toModel]=wrapRounded(fn),convert2[fromModel][toModel].raw=wrapRaw(fn);});});module.exports=convert2;}});var import_color_convert=__toESM(require_color_convert());function u(){return (u=Object.assign||function(e2){for(var r2=1;r2<arguments.length;r2++){var t2=arguments[r2];for(var n2 in t2)Object.prototype.hasOwnProperty.call(t2,n2)&&(e2[n2]=t2[n2]);}return e2}).apply(this,arguments)}function c(e2,r2){if(e2==null)return {};var t2,n2,o2={},a2=Object.keys(e2);for(n2=0;n2<a2.length;n2++)r2.indexOf(t2=a2[n2])>=0||(o2[t2]=e2[t2]);return o2}function i(e2){var t2=useRef(e2),n2=useRef(function(e3){t2.current&&t2.current(e3);});return t2.current=e2,n2.current}var s=function(e2,r2,t2){return r2===void 0&&(r2=0),t2===void 0&&(t2=1),e2>t2?t2:e2<r2?r2:e2},f=function(e2){return \"touches\"in e2},v=function(e2){return e2&&e2.ownerDocument.defaultView||self},d=function(e2,r2,t2){var n2=e2.getBoundingClientRect(),o2=f(r2)?function(e3,r3){for(var t3=0;t3<e3.length;t3++)if(e3[t3].identifier===r3)return e3[t3];return e3[0]}(r2.touches,t2):r2;return {left:s((o2.pageX-(n2.left+v(e2).pageXOffset))/n2.width),top:s((o2.pageY-(n2.top+v(e2).pageYOffset))/n2.height)}},h=function(e2){!f(e2)&&e2.preventDefault();},m=e.memo(function(o2){var a2=o2.onMove,l2=o2.onKey,s2=c(o2,[\"onMove\",\"onKey\"]),m2=useRef(null),g2=i(a2),p2=i(l2),b2=useRef(null),_2=useRef(!1),x2=useMemo(function(){var e2=function(e3){h(e3),(f(e3)?e3.touches.length>0:e3.buttons>0)&&m2.current?g2(d(m2.current,e3,b2.current)):t2(!1);},r2=function(){return t2(!1)};function t2(t3){var n2=_2.current,o3=v(m2.current),a3=t3?o3.addEventListener:o3.removeEventListener;a3(n2?\"touchmove\":\"mousemove\",e2),a3(n2?\"touchend\":\"mouseup\",r2);}return [function(e3){var r3=e3.nativeEvent,n2=m2.current;if(n2&&(h(r3),!function(e4,r4){return r4&&!f(e4)}(r3,_2.current)&&n2)){if(f(r3)){_2.current=!0;var o3=r3.changedTouches||[];o3.length&&(b2.current=o3[0].identifier);}n2.focus(),g2(d(n2,r3,b2.current)),t2(!0);}},function(e3){var r3=e3.which||e3.keyCode;r3<37||r3>40||(e3.preventDefault(),p2({left:r3===39?.05:r3===37?-.05:0,top:r3===40?.05:r3===38?-.05:0}));},t2]},[p2,g2]),C2=x2[0],E2=x2[1],H2=x2[2];return useEffect(function(){return H2},[H2]),e.createElement(\"div\",u({},s2,{onTouchStart:C2,onMouseDown:C2,className:\"react-colorful__interactive\",ref:m2,onKeyDown:E2,tabIndex:0,role:\"slider\"}))}),g=function(e2){return e2.filter(Boolean).join(\" \")},p=function(r2){var t2=r2.color,n2=r2.left,o2=r2.top,a2=o2===void 0?.5:o2,l2=g([\"react-colorful__pointer\",r2.className]);return e.createElement(\"div\",{className:l2,style:{top:100*a2+\"%\",left:100*n2+\"%\"}},e.createElement(\"div\",{className:\"react-colorful__pointer-fill\",style:{backgroundColor:t2}}))},b=function(e2,r2,t2){return r2===void 0&&(r2=0),t2===void 0&&(t2=Math.pow(10,r2)),Math.round(t2*e2)/t2},_={grad:.9,turn:360,rad:360/(2*Math.PI)},x=function(e2){return L(C(e2))},C=function(e2){return e2[0]===\"#\"&&(e2=e2.substring(1)),e2.length<6?{r:parseInt(e2[0]+e2[0],16),g:parseInt(e2[1]+e2[1],16),b:parseInt(e2[2]+e2[2],16),a:e2.length===4?b(parseInt(e2[3]+e2[3],16)/255,2):1}:{r:parseInt(e2.substring(0,2),16),g:parseInt(e2.substring(2,4),16),b:parseInt(e2.substring(4,6),16),a:e2.length===8?b(parseInt(e2.substring(6,8),16)/255,2):1}},E=function(e2,r2){return r2===void 0&&(r2=\"deg\"),Number(e2)*(_[r2]||1)},H=function(e2){var r2=/hsla?\\(?\\s*(-?\\d*\\.?\\d+)(deg|rad|grad|turn)?[,\\s]+(-?\\d*\\.?\\d+)%?[,\\s]+(-?\\d*\\.?\\d+)%?,?\\s*[/\\s]*(-?\\d*\\.?\\d+)?(%)?\\s*\\)?/i.exec(e2);return r2?N({h:E(r2[1],r2[2]),s:Number(r2[3]),l:Number(r2[4]),a:r2[5]===void 0?1:Number(r2[5])/(r2[6]?100:1)}):{h:0,s:0,v:0,a:1}};var N=function(e2){var r2=e2.s,t2=e2.l;return {h:e2.h,s:(r2*=(t2<50?t2:100-t2)/100)>0?2*r2/(t2+r2)*100:0,v:t2+r2,a:e2.a}},w=function(e2){return K(I(e2))},y=function(e2){var r2=e2.s,t2=e2.v,n2=e2.a,o2=(200-r2)*t2/100;return {h:b(e2.h),s:b(o2>0&&o2<200?r2*t2/100/(o2<=100?o2:200-o2)*100:0),l:b(o2/2),a:b(n2,2)}},q=function(e2){var r2=y(e2);return \"hsl(\"+r2.h+\", \"+r2.s+\"%, \"+r2.l+\"%)\"},k=function(e2){var r2=y(e2);return \"hsla(\"+r2.h+\", \"+r2.s+\"%, \"+r2.l+\"%, \"+r2.a+\")\"},I=function(e2){var r2=e2.h,t2=e2.s,n2=e2.v,o2=e2.a;r2=r2/360*6,t2/=100,n2/=100;var a2=Math.floor(r2),l2=n2*(1-t2),u2=n2*(1-(r2-a2)*t2),c2=n2*(1-(1-r2+a2)*t2),i2=a2%6;return {r:b(255*[n2,u2,l2,l2,c2,n2][i2]),g:b(255*[c2,n2,n2,u2,l2,l2][i2]),b:b(255*[l2,l2,c2,n2,n2,u2][i2]),a:b(o2,2)}};var z=function(e2){var r2=/rgba?\\(?\\s*(-?\\d*\\.?\\d+)(%)?[,\\s]+(-?\\d*\\.?\\d+)(%)?[,\\s]+(-?\\d*\\.?\\d+)(%)?,?\\s*[/\\s]*(-?\\d*\\.?\\d+)?(%)?\\s*\\)?/i.exec(e2);return r2?L({r:Number(r2[1])/(r2[2]?100/255:1),g:Number(r2[3])/(r2[4]?100/255:1),b:Number(r2[5])/(r2[6]?100/255:1),a:r2[7]===void 0?1:Number(r2[7])/(r2[8]?100:1)}):{h:0,s:0,v:0,a:1}};var D=function(e2){var r2=e2.toString(16);return r2.length<2?\"0\"+r2:r2},K=function(e2){var r2=e2.r,t2=e2.g,n2=e2.b,o2=e2.a,a2=o2<1?D(b(255*o2)):\"\";return \"#\"+D(r2)+D(t2)+D(n2)+a2},L=function(e2){var r2=e2.r,t2=e2.g,n2=e2.b,o2=e2.a,a2=Math.max(r2,t2,n2),l2=a2-Math.min(r2,t2,n2),u2=l2?a2===r2?(t2-n2)/l2:a2===t2?2+(n2-r2)/l2:4+(r2-t2)/l2:0;return {h:b(60*(u2<0?u2+6:u2)),s:b(a2?l2/a2*100:0),v:b(a2/255*100),a:o2}};var S=e.memo(function(r2){var t2=r2.hue,n2=r2.onChange,o2=g([\"react-colorful__hue\",r2.className]);return e.createElement(\"div\",{className:o2},e.createElement(m,{onMove:function(e2){n2({h:360*e2.left});},onKey:function(e2){n2({h:s(t2+360*e2.left,0,360)});},\"aria-label\":\"Hue\",\"aria-valuenow\":b(t2),\"aria-valuemax\":\"360\",\"aria-valuemin\":\"0\"},e.createElement(p,{className:\"react-colorful__hue-pointer\",left:t2/360,color:q({h:t2,s:100,v:100,a:1})})))}),T=e.memo(function(r2){var t2=r2.hsva,n2=r2.onChange,o2={backgroundColor:q({h:t2.h,s:100,v:100,a:1})};return e.createElement(\"div\",{className:\"react-colorful__saturation\",style:o2},e.createElement(m,{onMove:function(e2){n2({s:100*e2.left,v:100-100*e2.top});},onKey:function(e2){n2({s:s(t2.s+100*e2.left,0,100),v:s(t2.v-100*e2.top,0,100)});},\"aria-label\":\"Color\",\"aria-valuetext\":\"Saturation \"+b(t2.s)+\"%, Brightness \"+b(t2.v)+\"%\"},e.createElement(p,{className:\"react-colorful__saturation-pointer\",top:1-t2.v/100,left:t2.s/100,color:q(t2)})))}),F=function(e2,r2){if(e2===r2)return !0;for(var t2 in e2)if(e2[t2]!==r2[t2])return !1;return !0},P=function(e2,r2){return e2.replace(/\\s/g,\"\")===r2.replace(/\\s/g,\"\")},X=function(e2,r2){return e2.toLowerCase()===r2.toLowerCase()||F(C(e2),C(r2))};function Y(e2,t2,l2){var u2=i(l2),c2=useState(function(){return e2.toHsva(t2)}),s2=c2[0],f2=c2[1],v2=useRef({color:t2,hsva:s2});useEffect(function(){if(!e2.equal(t2,v2.current.color)){var r2=e2.toHsva(t2);v2.current={hsva:r2,color:t2},f2(r2);}},[t2,e2]),useEffect(function(){var r2;F(s2,v2.current.hsva)||e2.equal(r2=e2.fromHsva(s2),v2.current.color)||(v2.current={hsva:s2,color:r2},u2(r2));},[s2,e2,u2]);var d2=useCallback(function(e3){f2(function(r2){return Object.assign({},r2,e3)});},[]);return [s2,d2]}var V=typeof window<\"u\"?useLayoutEffect:useEffect,$=function(){return (typeof __webpack_nonce__<\"u\"?__webpack_nonce__:void 0)};var J=new Map,Q=function(e2){V(function(){var r2=e2.current?e2.current.ownerDocument:document;if(r2!==void 0&&!J.has(r2)){var t2=r2.createElement(\"style\");t2.innerHTML=`.react-colorful{position:relative;display:flex;flex-direction:column;width:200px;height:200px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:default}.react-colorful__saturation{position:relative;flex-grow:1;border-color:transparent;border-bottom:12px solid #000;border-radius:8px 8px 0 0;background-image:linear-gradient(0deg,#000,transparent),linear-gradient(90deg,#fff,hsla(0,0%,100%,0))}.react-colorful__alpha-gradient,.react-colorful__pointer-fill{content:\"\";position:absolute;left:0;top:0;right:0;bottom:0;pointer-events:none;border-radius:inherit}.react-colorful__alpha-gradient,.react-colorful__saturation{box-shadow:inset 0 0 0 1px rgba(0,0,0,.05)}.react-colorful__alpha,.react-colorful__hue{position:relative;height:24px}.react-colorful__hue{background:linear-gradient(90deg,red 0,#ff0 17%,#0f0 33%,#0ff 50%,#00f 67%,#f0f 83%,red)}.react-colorful__last-control{border-radius:0 0 8px 8px}.react-colorful__interactive{position:absolute;left:0;top:0;right:0;bottom:0;border-radius:inherit;outline:none;touch-action:none}.react-colorful__pointer{position:absolute;z-index:1;box-sizing:border-box;width:28px;height:28px;transform:translate(-50%,-50%);background-color:#fff;border:2px solid #fff;border-radius:50%;box-shadow:0 2px 4px rgba(0,0,0,.2)}.react-colorful__interactive:focus .react-colorful__pointer{transform:translate(-50%,-50%) scale(1.1)}.react-colorful__alpha,.react-colorful__alpha-pointer{background-color:#fff;background-image:url('data:image/svg+xml;charset=utf-8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" fill-opacity=\".05\"><path d=\"M8 0h8v8H8zM0 8h8v8H0z\"/></svg>')}.react-colorful__saturation-pointer{z-index:3}.react-colorful__hue-pointer{z-index:2}`,J.set(r2,t2);var n2=$();n2&&t2.setAttribute(\"nonce\",n2),r2.head.appendChild(t2);}},[]);},U=function(t2){var n2=t2.className,o2=t2.colorModel,a2=t2.color,l2=a2===void 0?o2.defaultColor:a2,i2=t2.onChange,s2=c(t2,[\"className\",\"colorModel\",\"color\",\"onChange\"]),f2=useRef(null);Q(f2);var v2=Y(o2,l2,i2),d2=v2[0],h2=v2[1],m2=g([\"react-colorful\",n2]);return e.createElement(\"div\",u({},s2,{ref:f2,className:m2}),e.createElement(T,{hsva:d2,onChange:h2}),e.createElement(S,{hue:d2.h,onChange:h2,className:\"react-colorful__last-control\"}))},W={defaultColor:\"000\",toHsva:x,fromHsva:function(e2){return w({h:e2.h,s:e2.s,v:e2.v,a:1})},equal:X},Z=function(r2){return e.createElement(U,u({},r2,{colorModel:W}))},ee=function(r2){var t2=r2.className,n2=r2.hsva,o2=r2.onChange,a2={backgroundImage:\"linear-gradient(90deg, \"+k(Object.assign({},n2,{a:0}))+\", \"+k(Object.assign({},n2,{a:1}))+\")\"},l2=g([\"react-colorful__alpha\",t2]),u2=b(100*n2.a);return e.createElement(\"div\",{className:l2},e.createElement(\"div\",{className:\"react-colorful__alpha-gradient\",style:a2}),e.createElement(m,{onMove:function(e2){o2({a:e2.left});},onKey:function(e2){o2({a:s(n2.a+e2.left)});},\"aria-label\":\"Alpha\",\"aria-valuetext\":u2+\"%\",\"aria-valuenow\":u2,\"aria-valuemin\":\"0\",\"aria-valuemax\":\"100\"},e.createElement(p,{className:\"react-colorful__alpha-pointer\",left:n2.a,color:k(n2)})))},re=function(t2){var n2=t2.className,o2=t2.colorModel,a2=t2.color,l2=a2===void 0?o2.defaultColor:a2,i2=t2.onChange,s2=c(t2,[\"className\",\"colorModel\",\"color\",\"onChange\"]),f2=useRef(null);Q(f2);var v2=Y(o2,l2,i2),d2=v2[0],h2=v2[1],m2=g([\"react-colorful\",n2]);return e.createElement(\"div\",u({},s2,{ref:f2,className:m2}),e.createElement(T,{hsva:d2,onChange:h2}),e.createElement(S,{hue:d2.h,onChange:h2}),e.createElement(ee,{hsva:d2,onChange:h2,className:\"react-colorful__last-control\"}))};var le={defaultColor:\"hsla(0, 0%, 0%, 1)\",toHsva:H,fromHsva:k,equal:P},ue=function(r2){return e.createElement(re,u({},r2,{colorModel:le}))};var Ee={defaultColor:\"rgba(0, 0, 0, 1)\",toHsva:z,fromHsva:function(e2){var r2=I(e2);return \"rgba(\"+r2.r+\", \"+r2.g+\", \"+r2.b+\", \"+r2.a+\")\"},equal:P},He=function(r2){return e.createElement(re,u({},r2,{colorModel:Ee}))};var Wrapper=styled.div({position:\"relative\",maxWidth:250,'&[aria-readonly=\"true\"]':{opacity:.5}}),PickerTooltip=styled(WithTooltip)({position:\"absolute\",zIndex:1,top:4,left:4,\"[aria-readonly=true] &\":{cursor:\"not-allowed\"}}),TooltipContent=styled.div({width:200,margin:5,\".react-colorful__saturation\":{borderRadius:\"4px 4px 0 0\"},\".react-colorful__hue\":{boxShadow:\"inset 0 0 0 1px rgb(0 0 0 / 5%)\"},\".react-colorful__last-control\":{borderRadius:\"0 0 4px 4px\"}}),Note=styled(TooltipNote)(({theme})=>({fontFamily:theme.typography.fonts.base})),Swatches=styled.div({display:\"grid\",gridTemplateColumns:\"repeat(9, 16px)\",gap:6,padding:3,marginTop:5,width:200}),SwatchColor=styled.div(({theme,active})=>({width:16,height:16,boxShadow:active?`${theme.appBorderColor} 0 0 0 1px inset, ${theme.textMutedColor}50 0 0 0 4px`:`${theme.appBorderColor} 0 0 0 1px inset`,borderRadius:theme.appBorderRadius})),swatchBackground=`url('data:image/svg+xml;charset=utf-8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" fill-opacity=\".05\"><path d=\"M8 0h8v8H8zM0 8h8v8H0z\"/></svg>')`,Swatch=({value,style,...props})=>{let backgroundImage=`linear-gradient(${value}, ${value}), ${swatchBackground}, linear-gradient(#fff, #fff)`;return e.createElement(SwatchColor,{...props,style:{...style,backgroundImage}})},Input=styled(Form.Input)(({theme,readOnly})=>({width:\"100%\",paddingLeft:30,paddingRight:30,boxSizing:\"border-box\",fontFamily:theme.typography.fonts.base})),ToggleIcon=styled(MarkupIcon)(({theme})=>({position:\"absolute\",zIndex:1,top:6,right:7,width:20,height:20,padding:4,boxSizing:\"border-box\",cursor:\"pointer\",color:theme.input.color})),ColorSpace=(ColorSpace2=>(ColorSpace2.RGB=\"rgb\",ColorSpace2.HSL=\"hsl\",ColorSpace2.HEX=\"hex\",ColorSpace2))(ColorSpace||{}),COLOR_SPACES=Object.values(ColorSpace),COLOR_REGEXP=/\\(([0-9]+),\\s*([0-9]+)%?,\\s*([0-9]+)%?,?\\s*([0-9.]+)?\\)/,RGB_REGEXP=/^\\s*rgba?\\(([0-9]+),\\s*([0-9]+),\\s*([0-9]+),?\\s*([0-9.]+)?\\)\\s*$/i,HSL_REGEXP=/^\\s*hsla?\\(([0-9]+),\\s*([0-9]+)%,\\s*([0-9]+)%,?\\s*([0-9.]+)?\\)\\s*$/i,HEX_REGEXP=/^\\s*#?([0-9a-f]{3}|[0-9a-f]{6})\\s*$/i,SHORTHEX_REGEXP=/^\\s*#?([0-9a-f]{3})\\s*$/i,ColorPicker={hex:Z,rgb:He,hsl:ue},fallbackColor={hex:\"transparent\",rgb:\"rgba(0, 0, 0, 0)\",hsl:\"hsla(0, 0%, 0%, 0)\"},stringToArgs=value=>{let match=value?.match(COLOR_REGEXP);if(!match)return [0,0,0,1];let[,x2,y2,z2,a2=1]=match;return [x2,y2,z2,a2].map(Number)},parseRgb=value=>{let[r2,g2,b2,a2]=stringToArgs(value),[h2,s2,l2]=import_color_convert.default.rgb.hsl([r2,g2,b2])||[0,0,0];return {valid:!0,value,keyword:import_color_convert.default.rgb.keyword([r2,g2,b2]),colorSpace:\"rgb\",rgb:value,hsl:`hsla(${h2}, ${s2}%, ${l2}%, ${a2})`,hex:`#${import_color_convert.default.rgb.hex([r2,g2,b2]).toLowerCase()}`}},parseHsl=value=>{let[h2,s2,l2,a2]=stringToArgs(value),[r2,g2,b2]=import_color_convert.default.hsl.rgb([h2,s2,l2])||[0,0,0];return {valid:!0,value,keyword:import_color_convert.default.hsl.keyword([h2,s2,l2]),colorSpace:\"hsl\",rgb:`rgba(${r2}, ${g2}, ${b2}, ${a2})`,hsl:value,hex:`#${import_color_convert.default.hsl.hex([h2,s2,l2]).toLowerCase()}`}},parseHexOrKeyword=value=>{let plain=value.replace(\"#\",\"\"),rgb=import_color_convert.default.keyword.rgb(plain)||import_color_convert.default.hex.rgb(plain),hsl=import_color_convert.default.rgb.hsl(rgb),mapped=value;/[^#a-f0-9]/i.test(value)?mapped=plain:HEX_REGEXP.test(value)&&(mapped=`#${plain}`);let valid=!0;if(mapped.startsWith(\"#\"))valid=HEX_REGEXP.test(mapped);else try{import_color_convert.default.keyword.hex(mapped);}catch{valid=!1;}return {valid,value:mapped,keyword:import_color_convert.default.rgb.keyword(rgb),colorSpace:\"hex\",rgb:`rgba(${rgb[0]}, ${rgb[1]}, ${rgb[2]}, 1)`,hsl:`hsla(${hsl[0]}, ${hsl[1]}%, ${hsl[2]}%, 1)`,hex:mapped}},parseValue=value=>{if(value)return RGB_REGEXP.test(value)?parseRgb(value):HSL_REGEXP.test(value)?parseHsl(value):parseHexOrKeyword(value)},getRealValue=(value,color,colorSpace)=>{if(!value||!color?.valid)return fallbackColor[colorSpace];if(colorSpace!==\"hex\")return color?.[colorSpace]||fallbackColor[colorSpace];if(!color.hex.startsWith(\"#\"))try{return `#${import_color_convert.default.keyword.hex(color.hex)}`}catch{return fallbackColor.hex}let short=color.hex.match(SHORTHEX_REGEXP);if(!short)return HEX_REGEXP.test(color.hex)?color.hex:fallbackColor.hex;let[r2,g2,b2]=short[1].split(\"\");return `#${r2}${r2}${g2}${g2}${b2}${b2}`},useColorInput=(initialValue,onChange)=>{let[value,setValue]=useState(initialValue||\"\"),[color,setColor]=useState(()=>parseValue(value)),[colorSpace,setColorSpace]=useState(color?.colorSpace||\"hex\");useEffect(()=>{let nextValue=initialValue||\"\",nextColor=parseValue(nextValue);setValue(nextValue),setColor(nextColor),setColorSpace(nextColor?.colorSpace||\"hex\");},[initialValue]);let realValue=useMemo(()=>getRealValue(value,color,colorSpace).toLowerCase(),[value,color,colorSpace]),updateValue=useCallback(update=>{let parsed=parseValue(update),v2=parsed?.value||update||\"\";setValue(v2),v2===\"\"&&(setColor(void 0),onChange(void 0)),parsed&&(setColor(parsed),setColorSpace(parsed.colorSpace),onChange(parsed.value));},[onChange]),cycleColorSpace=useCallback(()=>{let nextIndex=(COLOR_SPACES.indexOf(colorSpace)+1)%COLOR_SPACES.length,nextSpace=COLOR_SPACES[nextIndex];setColorSpace(nextSpace);let updatedValue=color?.[nextSpace]||\"\";setValue(updatedValue),onChange(updatedValue);},[color,colorSpace,onChange]);return {value,realValue,updateValue,color,colorSpace,cycleColorSpace}},id=value=>value.replace(/\\s*/,\"\").toLowerCase(),usePresets=(presetColors,currentColor,colorSpace)=>{let[selectedColors,setSelectedColors]=useState(currentColor?.valid?[currentColor]:[]);useEffect(()=>{currentColor===void 0&&setSelectedColors([]);},[currentColor]);let presets=useMemo(()=>(presetColors||[]).map(preset=>typeof preset==\"string\"?parseValue(preset):preset.title?{...parseValue(preset.color),keyword:preset.title}:parseValue(preset.color)).concat(selectedColors).filter(Boolean).slice(-27),[presetColors,selectedColors]),addPreset=useCallback(color=>{color?.valid&&(presets.some(preset=>preset&&preset[colorSpace]&&id(preset[colorSpace]||\"\")===id(color[colorSpace]||\"\"))||setSelectedColors(arr=>arr.concat(color)));},[colorSpace,presets]);return {presets,addPreset}},ColorControl=({name,value:initialValue,onChange,onFocus,onBlur,presetColors,startOpen=!1,argType})=>{let debouncedOnChange=useCallback(debounce(onChange,200),[onChange]),{value,realValue,updateValue,color,colorSpace,cycleColorSpace}=useColorInput(initialValue,debouncedOnChange),{presets,addPreset}=usePresets(presetColors??[],color,colorSpace),Picker=ColorPicker[colorSpace],readonly=!!argType?.table?.readonly;return e.createElement(Wrapper,{\"aria-readonly\":readonly},e.createElement(PickerTooltip,{startOpen,trigger:readonly?null:void 0,closeOnOutsideClick:!0,onVisibleChange:()=>color&&addPreset(color),tooltip:e.createElement(TooltipContent,null,e.createElement(Picker,{color:realValue===\"transparent\"?\"#000000\":realValue,onChange:updateValue,onFocus,onBlur}),presets.length>0&&e.createElement(Swatches,null,presets.map((preset,index)=>e.createElement(WithTooltip,{key:`${preset?.value||index}-${index}`,hasChrome:!1,tooltip:e.createElement(Note,{note:preset?.keyword||preset?.value||\"\"})},e.createElement(Swatch,{value:preset?.[colorSpace]||\"\",active:!!(color&&preset&&preset[colorSpace]&&id(preset[colorSpace]||\"\")===id(color[colorSpace])),onClick:()=>preset&&updateValue(preset.value||\"\")})))))},e.createElement(Swatch,{value:realValue,style:{margin:4}})),e.createElement(Input,{id:getControlId(name),value,onChange:e2=>updateValue(e2.target.value),onFocus:e2=>e2.target.select(),readOnly:readonly,placeholder:\"Choose color...\"}),value?e.createElement(ToggleIcon,{onClick:cycleColorSpace}):null)},Color_default=ColorControl;\n\nexport { ColorControl, Color_default as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,mBAAsF;AAKtF,IAAI,qBAAmB,WAAW,EAAC,yCAAyC,SAAQ,QAAO;AAAC,SAAO,UAAQ,EAAC,WAAU,CAAC,KAAI,KAAI,GAAG,GAAE,cAAa,CAAC,KAAI,KAAI,GAAG,GAAE,MAAK,CAAC,GAAE,KAAI,GAAG,GAAE,YAAW,CAAC,KAAI,KAAI,GAAG,GAAE,OAAM,CAAC,KAAI,KAAI,GAAG,GAAE,OAAM,CAAC,KAAI,KAAI,GAAG,GAAE,QAAO,CAAC,KAAI,KAAI,GAAG,GAAE,OAAM,CAAC,GAAE,GAAE,CAAC,GAAE,gBAAe,CAAC,KAAI,KAAI,GAAG,GAAE,MAAK,CAAC,GAAE,GAAE,GAAG,GAAE,YAAW,CAAC,KAAI,IAAG,GAAG,GAAE,OAAM,CAAC,KAAI,IAAG,EAAE,GAAE,WAAU,CAAC,KAAI,KAAI,GAAG,GAAE,WAAU,CAAC,IAAG,KAAI,GAAG,GAAE,YAAW,CAAC,KAAI,KAAI,CAAC,GAAE,WAAU,CAAC,KAAI,KAAI,EAAE,GAAE,OAAM,CAAC,KAAI,KAAI,EAAE,GAAE,gBAAe,CAAC,KAAI,KAAI,GAAG,GAAE,UAAS,CAAC,KAAI,KAAI,GAAG,GAAE,SAAQ,CAAC,KAAI,IAAG,EAAE,GAAE,MAAK,CAAC,GAAE,KAAI,GAAG,GAAE,UAAS,CAAC,GAAE,GAAE,GAAG,GAAE,UAAS,CAAC,GAAE,KAAI,GAAG,GAAE,eAAc,CAAC,KAAI,KAAI,EAAE,GAAE,UAAS,CAAC,KAAI,KAAI,GAAG,GAAE,WAAU,CAAC,GAAE,KAAI,CAAC,GAAE,UAAS,CAAC,KAAI,KAAI,GAAG,GAAE,WAAU,CAAC,KAAI,KAAI,GAAG,GAAE,aAAY,CAAC,KAAI,GAAE,GAAG,GAAE,gBAAe,CAAC,IAAG,KAAI,EAAE,GAAE,YAAW,CAAC,KAAI,KAAI,CAAC,GAAE,YAAW,CAAC,KAAI,IAAG,GAAG,GAAE,SAAQ,CAAC,KAAI,GAAE,CAAC,GAAE,YAAW,CAAC,KAAI,KAAI,GAAG,GAAE,cAAa,CAAC,KAAI,KAAI,GAAG,GAAE,eAAc,CAAC,IAAG,IAAG,GAAG,GAAE,eAAc,CAAC,IAAG,IAAG,EAAE,GAAE,eAAc,CAAC,IAAG,IAAG,EAAE,GAAE,eAAc,CAAC,GAAE,KAAI,GAAG,GAAE,YAAW,CAAC,KAAI,GAAE,GAAG,GAAE,UAAS,CAAC,KAAI,IAAG,GAAG,GAAE,aAAY,CAAC,GAAE,KAAI,GAAG,GAAE,SAAQ,CAAC,KAAI,KAAI,GAAG,GAAE,SAAQ,CAAC,KAAI,KAAI,GAAG,GAAE,YAAW,CAAC,IAAG,KAAI,GAAG,GAAE,WAAU,CAAC,KAAI,IAAG,EAAE,GAAE,aAAY,CAAC,KAAI,KAAI,GAAG,GAAE,aAAY,CAAC,IAAG,KAAI,EAAE,GAAE,SAAQ,CAAC,KAAI,GAAE,GAAG,GAAE,WAAU,CAAC,KAAI,KAAI,GAAG,GAAE,YAAW,CAAC,KAAI,KAAI,GAAG,GAAE,MAAK,CAAC,KAAI,KAAI,CAAC,GAAE,WAAU,CAAC,KAAI,KAAI,EAAE,GAAE,MAAK,CAAC,KAAI,KAAI,GAAG,GAAE,OAAM,CAAC,GAAE,KAAI,CAAC,GAAE,aAAY,CAAC,KAAI,KAAI,EAAE,GAAE,MAAK,CAAC,KAAI,KAAI,GAAG,GAAE,UAAS,CAAC,KAAI,KAAI,GAAG,GAAE,SAAQ,CAAC,KAAI,KAAI,GAAG,GAAE,WAAU,CAAC,KAAI,IAAG,EAAE,GAAE,QAAO,CAAC,IAAG,GAAE,GAAG,GAAE,OAAM,CAAC,KAAI,KAAI,GAAG,GAAE,OAAM,CAAC,KAAI,KAAI,GAAG,GAAE,UAAS,CAAC,KAAI,KAAI,GAAG,GAAE,eAAc,CAAC,KAAI,KAAI,GAAG,GAAE,WAAU,CAAC,KAAI,KAAI,CAAC,GAAE,cAAa,CAAC,KAAI,KAAI,GAAG,GAAE,WAAU,CAAC,KAAI,KAAI,GAAG,GAAE,YAAW,CAAC,KAAI,KAAI,GAAG,GAAE,WAAU,CAAC,KAAI,KAAI,GAAG,GAAE,sBAAqB,CAAC,KAAI,KAAI,GAAG,GAAE,WAAU,CAAC,KAAI,KAAI,GAAG,GAAE,YAAW,CAAC,KAAI,KAAI,GAAG,GAAE,WAAU,CAAC,KAAI,KAAI,GAAG,GAAE,WAAU,CAAC,KAAI,KAAI,GAAG,GAAE,aAAY,CAAC,KAAI,KAAI,GAAG,GAAE,eAAc,CAAC,IAAG,KAAI,GAAG,GAAE,cAAa,CAAC,KAAI,KAAI,GAAG,GAAE,gBAAe,CAAC,KAAI,KAAI,GAAG,GAAE,gBAAe,CAAC,KAAI,KAAI,GAAG,GAAE,gBAAe,CAAC,KAAI,KAAI,GAAG,GAAE,aAAY,CAAC,KAAI,KAAI,GAAG,GAAE,MAAK,CAAC,GAAE,KAAI,CAAC,GAAE,WAAU,CAAC,IAAG,KAAI,EAAE,GAAE,OAAM,CAAC,KAAI,KAAI,GAAG,GAAE,SAAQ,CAAC,KAAI,GAAE,GAAG,GAAE,QAAO,CAAC,KAAI,GAAE,CAAC,GAAE,kBAAiB,CAAC,KAAI,KAAI,GAAG,GAAE,YAAW,CAAC,GAAE,GAAE,GAAG,GAAE,cAAa,CAAC,KAAI,IAAG,GAAG,GAAE,cAAa,CAAC,KAAI,KAAI,GAAG,GAAE,gBAAe,CAAC,IAAG,KAAI,GAAG,GAAE,iBAAgB,CAAC,KAAI,KAAI,GAAG,GAAE,mBAAkB,CAAC,GAAE,KAAI,GAAG,GAAE,iBAAgB,CAAC,IAAG,KAAI,GAAG,GAAE,iBAAgB,CAAC,KAAI,IAAG,GAAG,GAAE,cAAa,CAAC,IAAG,IAAG,GAAG,GAAE,WAAU,CAAC,KAAI,KAAI,GAAG,GAAE,WAAU,CAAC,KAAI,KAAI,GAAG,GAAE,UAAS,CAAC,KAAI,KAAI,GAAG,GAAE,aAAY,CAAC,KAAI,KAAI,GAAG,GAAE,MAAK,CAAC,GAAE,GAAE,GAAG,GAAE,SAAQ,CAAC,KAAI,KAAI,GAAG,GAAE,OAAM,CAAC,KAAI,KAAI,CAAC,GAAE,WAAU,CAAC,KAAI,KAAI,EAAE,GAAE,QAAO,CAAC,KAAI,KAAI,CAAC,GAAE,WAAU,CAAC,KAAI,IAAG,CAAC,GAAE,QAAO,CAAC,KAAI,KAAI,GAAG,GAAE,eAAc,CAAC,KAAI,KAAI,GAAG,GAAE,WAAU,CAAC,KAAI,KAAI,GAAG,GAAE,eAAc,CAAC,KAAI,KAAI,GAAG,GAAE,eAAc,CAAC,KAAI,KAAI,GAAG,GAAE,YAAW,CAAC,KAAI,KAAI,GAAG,GAAE,WAAU,CAAC,KAAI,KAAI,GAAG,GAAE,MAAK,CAAC,KAAI,KAAI,EAAE,GAAE,MAAK,CAAC,KAAI,KAAI,GAAG,GAAE,MAAK,CAAC,KAAI,KAAI,GAAG,GAAE,YAAW,CAAC,KAAI,KAAI,GAAG,GAAE,QAAO,CAAC,KAAI,GAAE,GAAG,GAAE,eAAc,CAAC,KAAI,IAAG,GAAG,GAAE,KAAI,CAAC,KAAI,GAAE,CAAC,GAAE,WAAU,CAAC,KAAI,KAAI,GAAG,GAAE,WAAU,CAAC,IAAG,KAAI,GAAG,GAAE,aAAY,CAAC,KAAI,IAAG,EAAE,GAAE,QAAO,CAAC,KAAI,KAAI,GAAG,GAAE,YAAW,CAAC,KAAI,KAAI,EAAE,GAAE,UAAS,CAAC,IAAG,KAAI,EAAE,GAAE,UAAS,CAAC,KAAI,KAAI,GAAG,GAAE,QAAO,CAAC,KAAI,IAAG,EAAE,GAAE,QAAO,CAAC,KAAI,KAAI,GAAG,GAAE,SAAQ,CAAC,KAAI,KAAI,GAAG,GAAE,WAAU,CAAC,KAAI,IAAG,GAAG,GAAE,WAAU,CAAC,KAAI,KAAI,GAAG,GAAE,WAAU,CAAC,KAAI,KAAI,GAAG,GAAE,MAAK,CAAC,KAAI,KAAI,GAAG,GAAE,aAAY,CAAC,GAAE,KAAI,GAAG,GAAE,WAAU,CAAC,IAAG,KAAI,GAAG,GAAE,KAAI,CAAC,KAAI,KAAI,GAAG,GAAE,MAAK,CAAC,GAAE,KAAI,GAAG,GAAE,SAAQ,CAAC,KAAI,KAAI,GAAG,GAAE,QAAO,CAAC,KAAI,IAAG,EAAE,GAAE,WAAU,CAAC,IAAG,KAAI,GAAG,GAAE,QAAO,CAAC,KAAI,KAAI,GAAG,GAAE,OAAM,CAAC,KAAI,KAAI,GAAG,GAAE,OAAM,CAAC,KAAI,KAAI,GAAG,GAAE,YAAW,CAAC,KAAI,KAAI,GAAG,GAAE,QAAO,CAAC,KAAI,KAAI,CAAC,GAAE,aAAY,CAAC,KAAI,KAAI,EAAE,EAAC;AAAE,EAAC,CAAC;AAAE,IAAI,sBAAoB,WAAW,EAAC,kDAAkD,SAAQ,QAAO;AAAC,MAAI,cAAY,mBAAmB,GAAE,kBAAgB,CAAC;AAAE,WAAQ,OAAO,OAAO,KAAK,WAAW,EAAE,iBAAgB,YAAY,GAAG,CAAC,IAAE;AAAI,MAAI,WAAS,EAAC,KAAI,EAAC,UAAS,GAAE,QAAO,MAAK,GAAE,KAAI,EAAC,UAAS,GAAE,QAAO,MAAK,GAAE,KAAI,EAAC,UAAS,GAAE,QAAO,MAAK,GAAE,KAAI,EAAC,UAAS,GAAE,QAAO,MAAK,GAAE,MAAK,EAAC,UAAS,GAAE,QAAO,OAAM,GAAE,KAAI,EAAC,UAAS,GAAE,QAAO,MAAK,GAAE,KAAI,EAAC,UAAS,GAAE,QAAO,MAAK,GAAE,KAAI,EAAC,UAAS,GAAE,QAAO,MAAK,GAAE,KAAI,EAAC,UAAS,GAAE,QAAO,CAAC,KAAK,EAAC,GAAE,SAAQ,EAAC,UAAS,GAAE,QAAO,CAAC,SAAS,EAAC,GAAE,QAAO,EAAC,UAAS,GAAE,QAAO,CAAC,QAAQ,EAAC,GAAE,SAAQ,EAAC,UAAS,GAAE,QAAO,CAAC,SAAS,EAAC,GAAE,KAAI,EAAC,UAAS,GAAE,QAAO,CAAC,KAAI,KAAI,GAAG,EAAC,GAAE,OAAM,EAAC,UAAS,GAAE,QAAO,CAAC,OAAM,OAAM,KAAK,EAAC,GAAE,MAAK,EAAC,UAAS,GAAE,QAAO,CAAC,MAAM,EAAC,EAAC;AAAE,SAAO,UAAQ;AAAS,WAAQ,SAAS,OAAO,KAAK,QAAQ,GAAE;AAAC,QAAG,EAAE,cAAa,SAAS,KAAK,GAAG,OAAM,IAAI,MAAM,gCAA8B,KAAK;AAAE,QAAG,EAAE,YAAW,SAAS,KAAK,GAAG,OAAM,IAAI,MAAM,sCAAoC,KAAK;AAAE,QAAG,SAAS,KAAK,EAAE,OAAO,WAAS,SAAS,KAAK,EAAE,SAAS,OAAM,IAAI,MAAM,wCAAsC,KAAK;AAAE,QAAG,EAAC,UAAS,OAAM,IAAE,SAAS,KAAK;AAAE,WAAO,SAAS,KAAK,EAAE,UAAS,OAAO,SAAS,KAAK,EAAE,QAAO,OAAO,eAAe,SAAS,KAAK,GAAE,YAAW,EAAC,OAAM,SAAQ,CAAC,GAAE,OAAO,eAAe,SAAS,KAAK,GAAE,UAAS,EAAC,OAAM,OAAM,CAAC;AAAA,EAAE;AAAC,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,MAAI,KAAK,IAAI,IAAG,IAAG,EAAE,GAAE,MAAI,KAAK,IAAI,IAAG,IAAG,EAAE,GAAE,QAAM,MAAI,KAAI,IAAG;AAAG,YAAM,MAAI,KAAG,IAAE,OAAK,MAAI,MAAI,KAAG,MAAI,QAAM,OAAK,MAAI,KAAG,KAAG,KAAG,MAAI,QAAM,OAAK,QAAM,KAAG,KAAG,KAAG,MAAI,QAAO,KAAG,KAAK,IAAI,KAAG,IAAG,GAAG,GAAE,KAAG,MAAI,MAAI;AAAK,QAAI,MAAI,MAAI,OAAK;AAAE,WAAO,QAAM,MAAI,KAAG,IAAE,MAAI,MAAG,KAAG,SAAO,MAAI,OAAK,KAAG,SAAO,IAAE,MAAI,MAAK,CAAC,IAAG,KAAG,KAAI,KAAG,GAAG;AAAA,EAAC;AAAE,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,MAAK,MAAK,MAAK,IAAG,IAAG,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,KAAK,IAAI,IAAG,IAAG,EAAE,GAAE,OAAK,KAAG,KAAK,IAAI,IAAG,IAAG,EAAE,GAAE,QAAM,SAAS,IAAG;AAAC,cAAQ,KAAG,MAAI,IAAE,OAAK,IAAE;AAAA,IAAC;AAAE,WAAO,SAAO,KAAG,KAAG,GAAE,KAAG,MAAI,KAAG,OAAK,IAAG,OAAK,MAAM,EAAE,GAAE,OAAK,MAAM,EAAE,GAAE,OAAK,MAAM,EAAE,GAAE,OAAK,KAAG,KAAG,OAAK,OAAK,OAAK,KAAG,KAAG,IAAE,IAAE,OAAK,OAAK,OAAK,OAAK,KAAG,IAAE,IAAE,OAAK,OAAM,KAAG,IAAE,MAAI,IAAE,KAAG,MAAI,MAAI,KAAI,CAAC,KAAG,KAAI,KAAG,KAAI,KAAG,GAAG;AAAA,EAAC;AAAE,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,KAAG,IAAI,CAAC,GAAE,KAAG,IAAI,CAAC,GAAE,KAAG,IAAI,CAAC,GAAE,KAAG,SAAS,IAAI,IAAI,GAAG,EAAE,CAAC,GAAE,KAAG,IAAE,MAAI,KAAK,IAAI,IAAG,KAAK,IAAI,IAAG,EAAE,CAAC;AAAE,WAAO,KAAG,IAAE,IAAE,MAAI,KAAK,IAAI,IAAG,KAAK,IAAI,IAAG,EAAE,CAAC,GAAE,CAAC,IAAG,KAAG,KAAI,KAAG,GAAG;AAAA,EAAC;AAAE,WAAS,IAAI,OAAK,SAAS,KAAI;AAAC,QAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,KAAK,IAAI,IAAE,IAAG,IAAE,IAAG,IAAE,EAAE,GAAE,MAAI,IAAE,KAAG,OAAK,IAAE,OAAK,GAAE,MAAI,IAAE,KAAG,OAAK,IAAE,OAAK,GAAE,MAAI,IAAE,KAAG,OAAK,IAAE,OAAK;AAAE,WAAO,CAAC,KAAG,KAAI,KAAG,KAAI,KAAG,KAAI,KAAG,GAAG;AAAA,EAAC;AAAE,WAAS,oBAAoB,IAAG,IAAG;AAAC,YAAQ,GAAG,CAAC,IAAE,GAAG,CAAC,MAAI,KAAG,GAAG,CAAC,IAAE,GAAG,CAAC,MAAI,KAAG,GAAG,CAAC,IAAE,GAAG,CAAC,MAAI;AAAA,EAAC;AAAC,WAAS,IAAI,UAAQ,SAAS,KAAI;AAAC,QAAI,WAAS,gBAAgB,GAAG;AAAE,QAAG,SAAS,QAAO;AAAS,QAAI,yBAAuB,IAAE,GAAE;AAAsB,aAAQ,WAAW,OAAO,KAAK,WAAW,GAAE;AAAC,UAAI,QAAM,YAAY,OAAO,GAAE,WAAS,oBAAoB,KAAI,KAAK;AAAE,iBAAS,2BAAyB,yBAAuB,UAAS,wBAAsB;AAAA,IAAS;AAAC,WAAO;AAAA,EAAqB;AAAE,WAAS,QAAQ,MAAI,SAAS,SAAQ;AAAC,WAAO,YAAY,OAAO;AAAA,EAAC;AAAE,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE;AAAI,SAAG,KAAG,YAAS,KAAG,SAAM,UAAQ,MAAI,KAAG,OAAM,KAAG,KAAG,YAAS,KAAG,SAAM,UAAQ,MAAI,KAAG,OAAM,KAAG,KAAG,YAAS,KAAG,SAAM,UAAQ,MAAI,KAAG;AAAM,QAAI,KAAG,KAAG,SAAM,KAAG,SAAM,KAAG,QAAM,KAAG,KAAG,SAAM,KAAG,SAAM,KAAG,QAAM,KAAG,KAAG,SAAM,KAAG,SAAM,KAAG;AAAM,WAAO,CAAC,KAAG,KAAI,KAAG,KAAI,KAAG,GAAG;AAAA,EAAC;AAAE,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,MAAI,SAAS,IAAI,IAAI,GAAG,GAAE,KAAG,IAAI,CAAC,GAAE,KAAG,IAAI,CAAC,GAAE,KAAG,IAAI,CAAC;AAAE,UAAI,QAAO,MAAI,KAAI,MAAI,SAAQ,KAAG,KAAG,UAAQ,OAAK,IAAE,KAAG,QAAM,KAAG,KAAG,KAAI,KAAG,KAAG,UAAQ,OAAK,IAAE,KAAG,QAAM,KAAG,KAAG,KAAI,KAAG,KAAG,UAAQ,OAAK,IAAE,KAAG,QAAM,KAAG,KAAG;AAAI,QAAI,KAAG,MAAI,KAAG,IAAG,KAAG,OAAK,KAAG,KAAI,KAAG,OAAK,KAAG;AAAI,WAAO,CAAC,IAAG,IAAG,EAAE;AAAA,EAAC;AAAE,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,IAAG,IAAG;AAAI,QAAG,OAAK,EAAE,QAAO,MAAI,KAAG,KAAI,CAAC,KAAI,KAAI,GAAG;AAAE,SAAG,MAAG,KAAG,MAAI,IAAE,MAAI,KAAG,KAAG,KAAG,KAAG;AAAG,QAAI,KAAG,IAAE,KAAG,IAAG,MAAI,CAAC,GAAE,GAAE,CAAC;AAAE,aAAQ,KAAG,GAAE,KAAG,GAAE,KAAK,MAAG,KAAG,IAAE,IAAE,EAAE,KAAG,IAAG,KAAG,KAAG,MAAK,KAAG,KAAG,MAAK,IAAE,KAAG,IAAE,MAAI,MAAI,KAAG,MAAI,IAAE,KAAG,IAAE,KAAG,IAAE,MAAI,KAAG,IAAE,KAAG,IAAE,MAAI,MAAI,KAAG,OAAK,IAAE,IAAE,MAAI,IAAE,MAAI,IAAG,IAAI,EAAE,IAAE,MAAI;AAAI,WAAO;AAAA,EAAG;AAAE,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,KAAG,IAAI,CAAC,GAAE,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,OAAK,IAAG,OAAK,KAAK,IAAI,IAAG,IAAG;AAAE,UAAI,GAAE,MAAI,MAAI,IAAE,KAAG,IAAE,IAAG,QAAM,QAAM,IAAE,OAAK,IAAE;AAAK,QAAI,MAAI,KAAG,MAAI,GAAE,KAAG,OAAK,IAAE,IAAE,QAAM,OAAK,QAAM,IAAE,MAAI,KAAG;AAAI,WAAO,CAAC,IAAG,KAAG,KAAI,KAAG,GAAG;AAAA,EAAC;AAAE,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,KAAG,IAAI,CAAC,IAAE,IAAG,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,KAAK,MAAM,EAAE,IAAE,GAAE,KAAG,KAAG,KAAK,MAAM,EAAE,GAAE,KAAG,MAAI,MAAI,IAAE,KAAI,KAAG,MAAI,MAAI,IAAE,KAAG,KAAI,KAAG,MAAI,MAAI,IAAE,MAAI,IAAE;AAAK,YAAO,MAAI,KAAI,IAAG;AAAA,MAAC,KAAK;AAAE,eAAO,CAAC,IAAG,IAAG,EAAE;AAAA,MAAE,KAAK;AAAE,eAAO,CAAC,IAAG,IAAG,EAAE;AAAA,MAAE,KAAK;AAAE,eAAO,CAAC,IAAG,IAAG,EAAE;AAAA,MAAE,KAAK;AAAE,eAAO,CAAC,IAAG,IAAG,EAAE;AAAA,MAAE,KAAK;AAAE,eAAO,CAAC,IAAG,IAAG,EAAE;AAAA,MAAE,KAAK;AAAE,eAAO,CAAC,IAAG,IAAG,EAAE;AAAA,IAAC;AAAA,EAAC;AAAE,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,KAAG,IAAI,CAAC,GAAE,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,OAAK,KAAK,IAAI,IAAG,IAAG,GAAE,IAAG;AAAG,UAAI,IAAE,MAAI;AAAG,QAAI,QAAM,IAAE,MAAI;AAAK,WAAO,KAAG,KAAG,MAAK,MAAI,QAAM,IAAE,OAAK,IAAE,MAAK,KAAG,MAAI,GAAE,MAAI,GAAE,CAAC,IAAG,KAAG,KAAI,KAAG,GAAG;AAAA,EAAC;AAAE,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,QAAM,KAAG,IAAG;AAAG,YAAM,MAAI,MAAI,OAAM,MAAI;AAAO,QAAI,KAAG,KAAK,MAAM,IAAE,EAAE,GAAE,KAAG,IAAE;AAAG,SAAG,IAAE,KAAG,KAAI,KAAG,OAAK,MAAI,KAAG,IAAE;AAAI,QAAI,KAAG,KAAG,MAAI,KAAG,KAAI,IAAG,IAAG;AAAG,YAAO,IAAG;AAAA,MAAC;AAAA,MAAQ,KAAK;AAAA,MAAE,KAAK;AAAE,aAAG,IAAG,KAAG,IAAG,KAAG;AAAG;AAAA,MAAM,KAAK;AAAE,aAAG,IAAG,KAAG,IAAG,KAAG;AAAG;AAAA,MAAM,KAAK;AAAE,aAAG,IAAG,KAAG,IAAG,KAAG;AAAG;AAAA,MAAM,KAAK;AAAE,aAAG,IAAG,KAAG,IAAG,KAAG;AAAG;AAAA,MAAM,KAAK;AAAE,aAAG,IAAG,KAAG,IAAG,KAAG;AAAG;AAAA,MAAM,KAAK;AAAE,aAAG,IAAG,KAAG,IAAG,KAAG;AAAG;AAAA,IAAK;AAAC,WAAO,CAAC,KAAG,KAAI,KAAG,KAAI,KAAG,GAAG;AAAA,EAAC;AAAE,WAAS,KAAK,MAAI,SAAS,MAAK;AAAC,QAAI,KAAG,KAAK,CAAC,IAAE,KAAI,KAAG,KAAK,CAAC,IAAE,KAAI,KAAG,KAAK,CAAC,IAAE,KAAI,KAAG,KAAK,CAAC,IAAE,KAAI,KAAG,IAAE,KAAK,IAAI,GAAE,MAAI,IAAE,MAAI,EAAE,GAAE,KAAG,IAAE,KAAK,IAAI,GAAE,MAAI,IAAE,MAAI,EAAE,GAAE,KAAG,IAAE,KAAK,IAAI,GAAE,MAAI,IAAE,MAAI,EAAE;AAAE,WAAO,CAAC,KAAG,KAAI,KAAG,KAAI,KAAG,GAAG;AAAA,EAAC;AAAE,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,IAAG,IAAG;AAAG,WAAO,KAAG,KAAG,SAAO,KAAG,UAAQ,KAAG,SAAO,KAAG,KAAG,UAAO,KAAG,SAAO,KAAG,QAAM,KAAG,KAAG,SAAM,KAAG,SAAM,KAAG,OAAM,KAAG,KAAG,WAAS,QAAM,OAAK,IAAE,OAAK,QAAK,KAAG,OAAM,KAAG,KAAG,WAAS,QAAM,OAAK,IAAE,OAAK,QAAK,KAAG,OAAM,KAAG,KAAG,WAAS,QAAM,OAAK,IAAE,OAAK,QAAK,KAAG,OAAM,KAAG,KAAK,IAAI,KAAK,IAAI,GAAE,EAAE,GAAE,CAAC,GAAE,KAAG,KAAK,IAAI,KAAK,IAAI,GAAE,EAAE,GAAE,CAAC,GAAE,KAAG,KAAK,IAAI,KAAK,IAAI,GAAE,EAAE,GAAE,CAAC,GAAE,CAAC,KAAG,KAAI,KAAG,KAAI,KAAG,GAAG;AAAA,EAAC;AAAE,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,KAAG,IAAI,CAAC,GAAE,KAAG,IAAI,CAAC,GAAE,KAAG,IAAI,CAAC;AAAE,UAAI,QAAO,MAAI,KAAI,MAAI,SAAQ,KAAG,KAAG,UAAQ,OAAK,IAAE,KAAG,QAAM,KAAG,KAAG,KAAI,KAAG,KAAG,UAAQ,OAAK,IAAE,KAAG,QAAM,KAAG,KAAG,KAAI,KAAG,KAAG,UAAQ,OAAK,IAAE,KAAG,QAAM,KAAG,KAAG;AAAI,QAAI,KAAG,MAAI,KAAG,IAAG,KAAG,OAAK,KAAG,KAAI,KAAG,OAAK,KAAG;AAAI,WAAO,CAAC,IAAG,IAAG,EAAE;AAAA,EAAC;AAAE,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,KAAG,IAAI,CAAC,GAAE,KAAG,IAAI,CAAC,GAAE,KAAG,IAAI,CAAC,GAAE,IAAG,IAAG;AAAG,UAAI,KAAG,MAAI,KAAI,KAAG,KAAG,MAAI,IAAG,KAAG,KAAG,KAAG;AAAI,QAAI,MAAI,MAAI,GAAE,MAAI,MAAI,GAAE,MAAI,MAAI;AAAE,WAAO,KAAG,MAAI,UAAQ,OAAK,KAAG,KAAG,OAAK,OAAM,KAAG,MAAI,UAAQ,OAAK,KAAG,KAAG,OAAK,OAAM,KAAG,MAAI,UAAQ,OAAK,KAAG,KAAG,OAAK,OAAM,MAAI,QAAO,MAAI,KAAI,MAAI,SAAQ,CAAC,IAAG,IAAG,EAAE;AAAA,EAAC;AAAE,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,KAAG,IAAI,CAAC,GAAE,KAAG,IAAI,CAAC,GAAE,KAAG,IAAI,CAAC,GAAE;AAAG,SAAG,KAAK,MAAM,IAAG,EAAE,IAAE,MAAI,IAAE,KAAK,IAAG,KAAG,MAAI,MAAI;AAAK,QAAI,KAAG,KAAK,KAAK,KAAG,KAAG,KAAG,EAAE;AAAE,WAAO,CAAC,IAAG,IAAG,EAAE;AAAA,EAAC;AAAE,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,KAAG,IAAI,CAAC,GAAE,KAAG,IAAI,CAAC,GAAE,KAAG,IAAI,CAAC,IAAE,MAAI,IAAE,KAAK,IAAG,KAAG,KAAG,KAAK,IAAI,EAAE,GAAE,KAAG,KAAG,KAAK,IAAI,EAAE;AAAE,WAAO,CAAC,IAAG,IAAG,EAAE;AAAA,EAAC;AAAE,WAAS,IAAI,SAAO,SAAS,MAAK,aAAW,MAAK;AAAC,QAAG,CAAC,IAAG,IAAG,EAAE,IAAE,MAAK,QAAM,eAAa,OAAK,SAAS,IAAI,IAAI,IAAI,EAAE,CAAC,IAAE;AAAW,QAAG,QAAM,KAAK,MAAM,QAAM,EAAE,GAAE,UAAQ,EAAE,QAAO;AAAG,QAAI,OAAK,MAAI,KAAK,MAAM,KAAG,GAAG,KAAG,IAAE,KAAK,MAAM,KAAG,GAAG,KAAG,IAAE,KAAK,MAAM,KAAG,GAAG;AAAG,WAAO,UAAQ,MAAI,QAAM,KAAI;AAAA,EAAI;AAAE,WAAS,IAAI,SAAO,SAAS,MAAK;AAAC,WAAO,SAAS,IAAI,OAAO,SAAS,IAAI,IAAI,IAAI,GAAE,KAAK,CAAC,CAAC;AAAA,EAAC;AAAE,WAAS,IAAI,UAAQ,SAAS,MAAK;AAAC,QAAI,KAAG,KAAK,CAAC,GAAE,KAAG,KAAK,CAAC,GAAE,KAAG,KAAK,CAAC;AAAE,WAAO,OAAK,MAAI,OAAK,KAAG,KAAG,IAAE,KAAG,KAAG,MAAI,MAAI,KAAK,OAAO,KAAG,KAAG,MAAI,EAAE,IAAE,MAAI,KAAG,KAAG,KAAK,MAAM,KAAG,MAAI,CAAC,IAAE,IAAE,KAAK,MAAM,KAAG,MAAI,CAAC,IAAE,KAAK,MAAM,KAAG,MAAI,CAAC;AAAA,EAAC;AAAE,WAAS,OAAO,MAAI,SAAS,MAAK;AAAC,QAAI,QAAM,OAAK;AAAG,QAAG,UAAQ,KAAG,UAAQ,EAAE,QAAO,OAAK,OAAK,SAAO,MAAK,QAAM,QAAM,OAAK,KAAI,CAAC,OAAM,OAAM,KAAK;AAAE,QAAI,QAAM,CAAC,EAAE,OAAK,MAAI,KAAG,KAAG,MAAI,QAAM,KAAG,OAAK,KAAI,MAAI,SAAO,IAAE,KAAG,OAAK,KAAI,MAAI,SAAO,IAAE,KAAG,OAAK;AAAI,WAAO,CAAC,IAAG,IAAG,EAAE;AAAA,EAAC;AAAE,WAAS,QAAQ,MAAI,SAAS,MAAK;AAAC,QAAG,QAAM,KAAI;AAAC,UAAI,MAAI,OAAK,OAAK,KAAG;AAAE,aAAO,CAAC,IAAG,IAAG,EAAE;AAAA,IAAC;AAAC,YAAM;AAAG,QAAI,KAAI,KAAG,KAAK,MAAM,OAAK,EAAE,IAAE,IAAE,KAAI,KAAG,KAAK,OAAO,MAAI,OAAK,MAAI,CAAC,IAAE,IAAE,KAAI,KAAG,MAAI,IAAE,IAAE;AAAI,WAAO,CAAC,IAAG,IAAG,EAAE;AAAA,EAAC;AAAE,WAAS,IAAI,MAAI,SAAS,MAAK;AAAC,QAAI,YAAU,KAAK,MAAM,KAAK,CAAC,CAAC,IAAE,QAAM,QAAM,KAAK,MAAM,KAAK,CAAC,CAAC,IAAE,QAAM,MAAI,KAAK,MAAM,KAAK,CAAC,CAAC,IAAE,MAAM,SAAS,EAAE,EAAE,YAAY;AAAE,WAAO,SAAS,UAAU,OAAO,MAAM,IAAE;AAAA,EAAM;AAAE,WAAS,IAAI,MAAI,SAAS,MAAK;AAAC,QAAI,QAAM,KAAK,SAAS,EAAE,EAAE,MAAM,0BAA0B;AAAE,QAAG,CAAC,MAAM,QAAO,CAAC,GAAE,GAAE,CAAC;AAAE,QAAI,cAAY,MAAM,CAAC;AAAE,UAAM,CAAC,EAAE,WAAS,MAAI,cAAY,YAAY,MAAM,EAAE,EAAE,IAAI,UAAM,OAAK,IAAI,EAAE,KAAK,EAAE;AAAG,QAAI,UAAQ,SAAS,aAAY,EAAE,GAAE,KAAG,WAAS,KAAG,KAAI,KAAG,WAAS,IAAE,KAAI,KAAG,UAAQ;AAAI,WAAO,CAAC,IAAG,IAAG,EAAE;AAAA,EAAC;AAAE,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,MAAI,KAAK,IAAI,KAAK,IAAI,IAAG,EAAE,GAAE,EAAE,GAAE,MAAI,KAAK,IAAI,KAAK,IAAI,IAAG,EAAE,GAAE,EAAE,GAAE,SAAO,MAAI,KAAI,WAAU;AAAI,WAAO,SAAO,IAAE,YAAU,OAAK,IAAE,UAAQ,YAAU,GAAE,UAAQ,IAAE,MAAI,IAAE,QAAM,KAAG,OAAK,KAAG,MAAI,SAAO,IAAE,QAAM,KAAG,MAAI,KAAG,KAAG,MAAI,SAAO,MAAI,KAAG,KAAG,MAAI,QAAO,OAAK,GAAE,OAAK,GAAE,CAAC,MAAI,KAAI,SAAO,KAAI,YAAU,GAAG;AAAA,EAAC;AAAE,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,KAAG,MAAG,IAAE,KAAG,KAAG,IAAE,MAAI,IAAE,KAAI,KAAG;AAAE,WAAO,KAAG,MAAI,MAAI,KAAG,MAAG,OAAK,IAAE,MAAK,CAAC,IAAI,CAAC,GAAE,KAAG,KAAI,KAAG,GAAG;AAAA,EAAC;AAAE,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,KAAG,IAAG,KAAG;AAAE,WAAO,KAAG,MAAI,MAAI,KAAG,OAAK,IAAE,MAAK,CAAC,IAAI,CAAC,GAAE,KAAG,KAAI,KAAG,GAAG;AAAA,EAAC;AAAE,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE;AAAI,QAAG,OAAK,EAAE,QAAO,CAAC,KAAG,KAAI,KAAG,KAAI,KAAG,GAAG;AAAE,QAAI,OAAK,CAAC,GAAE,GAAE,CAAC,GAAE,KAAG,KAAG,IAAE,GAAE,KAAG,KAAG,GAAE,KAAG,IAAE,IAAG,KAAG;AAAE,YAAO,KAAK,MAAM,EAAE,GAAE;AAAA,MAAC,KAAK;AAAE,aAAK,CAAC,IAAE,GAAE,KAAK,CAAC,IAAE,IAAG,KAAK,CAAC,IAAE;AAAE;AAAA,MAAM,KAAK;AAAE,aAAK,CAAC,IAAE,IAAG,KAAK,CAAC,IAAE,GAAE,KAAK,CAAC,IAAE;AAAE;AAAA,MAAM,KAAK;AAAE,aAAK,CAAC,IAAE,GAAE,KAAK,CAAC,IAAE,GAAE,KAAK,CAAC,IAAE;AAAG;AAAA,MAAM,KAAK;AAAE,aAAK,CAAC,IAAE,GAAE,KAAK,CAAC,IAAE,IAAG,KAAK,CAAC,IAAE;AAAE;AAAA,MAAM,KAAK;AAAE,aAAK,CAAC,IAAE,IAAG,KAAK,CAAC,IAAE,GAAE,KAAK,CAAC,IAAE;AAAE;AAAA,MAAM;AAAQ,aAAK,CAAC,IAAE,GAAE,KAAK,CAAC,IAAE,GAAE,KAAK,CAAC,IAAE;AAAA,IAAG;AAAC,WAAO,MAAI,IAAE,MAAI,IAAG,EAAE,KAAG,KAAK,CAAC,IAAE,MAAI,MAAK,KAAG,KAAK,CAAC,IAAE,MAAI,MAAK,KAAG,KAAK,CAAC,IAAE,MAAI,GAAG;AAAA,EAAC;AAAE,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,KAAG,MAAI,IAAE,KAAI,KAAG;AAAE,WAAO,KAAG,MAAI,KAAG,KAAG,KAAI,CAAC,IAAI,CAAC,GAAE,KAAG,KAAI,KAAG,GAAG;AAAA,EAAC;AAAE,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,OAAK,IAAE,MAAI,MAAG,IAAG,KAAG;AAAE,WAAO,KAAG,KAAG,KAAG,MAAG,KAAG,MAAI,IAAE,MAAI,MAAI,OAAI,KAAG,MAAI,KAAG,MAAI,KAAG,IAAE,OAAM,CAAC,IAAI,CAAC,GAAE,KAAG,KAAI,KAAG,GAAG;AAAA,EAAC;AAAE,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,KAAG,MAAI,IAAE;AAAI,WAAO,CAAC,IAAI,CAAC,IAAG,KAAG,MAAI,MAAK,IAAE,MAAI,GAAG;AAAA,EAAC;AAAE,WAAS,IAAI,MAAI,SAAS,KAAI;AAAC,QAAI,KAAG,IAAI,CAAC,IAAE,KAAI,KAAG,IAAE,IAAI,CAAC,IAAE,KAAI,KAAG,KAAG,IAAG,KAAG;AAAE,WAAO,KAAG,MAAI,MAAI,KAAG,OAAK,IAAE,MAAK,CAAC,IAAI,CAAC,GAAE,KAAG,KAAI,KAAG,GAAG;AAAA,EAAC;AAAE,WAAS,MAAM,MAAI,SAAS,OAAM;AAAC,WAAO,CAAC,MAAM,CAAC,IAAE,QAAM,KAAI,MAAM,CAAC,IAAE,QAAM,KAAI,MAAM,CAAC,IAAE,QAAM,GAAG;AAAA,EAAC;AAAE,WAAS,IAAI,QAAM,SAAS,KAAI;AAAC,WAAO,CAAC,IAAI,CAAC,IAAE,MAAI,OAAM,IAAI,CAAC,IAAE,MAAI,OAAM,IAAI,CAAC,IAAE,MAAI,KAAK;AAAA,EAAC;AAAE,WAAS,KAAK,MAAI,SAAS,MAAK;AAAC,WAAO,CAAC,KAAK,CAAC,IAAE,MAAI,KAAI,KAAK,CAAC,IAAE,MAAI,KAAI,KAAK,CAAC,IAAE,MAAI,GAAG;AAAA,EAAC;AAAE,WAAS,KAAK,MAAI,SAAS,MAAK;AAAC,WAAO,CAAC,GAAE,GAAE,KAAK,CAAC,CAAC;AAAA,EAAC;AAAE,WAAS,KAAK,MAAI,SAAS,KAAK;AAAI,WAAS,KAAK,MAAI,SAAS,MAAK;AAAC,WAAO,CAAC,GAAE,KAAI,KAAK,CAAC,CAAC;AAAA,EAAC;AAAE,WAAS,KAAK,OAAK,SAAS,MAAK;AAAC,WAAO,CAAC,GAAE,GAAE,GAAE,KAAK,CAAC,CAAC;AAAA,EAAC;AAAE,WAAS,KAAK,MAAI,SAAS,MAAK;AAAC,WAAO,CAAC,KAAK,CAAC,GAAE,GAAE,CAAC;AAAA,EAAC;AAAE,WAAS,KAAK,MAAI,SAAS,MAAK;AAAC,QAAI,MAAI,KAAK,MAAM,KAAK,CAAC,IAAE,MAAI,GAAG,IAAE,KAAI,WAAS,OAAK,OAAK,OAAK,KAAG,KAAK,SAAS,EAAE,EAAE,YAAY;AAAE,WAAO,SAAS,UAAU,OAAO,MAAM,IAAE;AAAA,EAAM;AAAE,WAAS,IAAI,OAAK,SAAS,KAAI;AAAC,WAAO,EAAE,IAAI,CAAC,IAAE,IAAI,CAAC,IAAE,IAAI,CAAC,KAAG,IAAE,MAAI,GAAG;AAAA,EAAC;AAAE,EAAC,CAAC;AAAE,IAAI,gBAAc,WAAW,EAAC,4CAA4C,SAAQ,QAAO;AAAC,MAAI,cAAY,oBAAoB;AAAE,WAAS,aAAY;AAAC,QAAI,QAAM,CAAC,GAAE,SAAO,OAAO,KAAK,WAAW;AAAE,aAAQ,MAAI,OAAO,QAAO,KAAG,GAAE,KAAG,KAAI,KAAK,OAAM,OAAO,EAAE,CAAC,IAAE,EAAC,UAAS,IAAG,QAAO,KAAI;AAAE,WAAO;AAAA,EAAK;AAAC,WAAS,UAAU,WAAU;AAAC,QAAI,QAAM,WAAW,GAAE,QAAM,CAAC,SAAS;AAAE,SAAI,MAAM,SAAS,EAAE,WAAS,GAAE,MAAM,UAAQ;AAAC,UAAI,UAAQ,MAAM,IAAI,GAAE,YAAU,OAAO,KAAK,YAAY,OAAO,CAAC;AAAE,eAAQ,MAAI,UAAU,QAAO,KAAG,GAAE,KAAG,KAAI,MAAK;AAAC,YAAI,WAAS,UAAU,EAAE,GAAE,OAAK,MAAM,QAAQ;AAAE,aAAK,aAAW,OAAK,KAAK,WAAS,MAAM,OAAO,EAAE,WAAS,GAAE,KAAK,SAAO,SAAQ,MAAM,QAAQ,QAAQ;AAAA,MAAG;AAAA,IAAC;AAAC,WAAO;AAAA,EAAK;AAAC,WAAS,KAAK,MAAK,IAAG;AAAC,WAAO,SAAS,MAAK;AAAC,aAAO,GAAG,KAAK,IAAI,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,WAAS,eAAe,SAAQ,OAAM;AAAC,QAAI,OAAK,CAAC,MAAM,OAAO,EAAE,QAAO,OAAO,GAAE,KAAG,YAAY,MAAM,OAAO,EAAE,MAAM,EAAE,OAAO,GAAE,MAAI,MAAM,OAAO,EAAE;AAAO,WAAK,MAAM,GAAG,EAAE,SAAQ,MAAK,QAAQ,MAAM,GAAG,EAAE,MAAM,GAAE,KAAG,KAAK,YAAY,MAAM,GAAG,EAAE,MAAM,EAAE,GAAG,GAAE,EAAE,GAAE,MAAI,MAAM,GAAG,EAAE;AAAO,WAAO,GAAG,aAAW,MAAK;AAAA,EAAE;AAAC,SAAO,UAAQ,SAAS,WAAU;AAAC,QAAI,QAAM,UAAU,SAAS,GAAE,aAAW,CAAC,GAAE,SAAO,OAAO,KAAK,KAAK;AAAE,aAAQ,MAAI,OAAO,QAAO,KAAG,GAAE,KAAG,KAAI,MAAK;AAAC,UAAI,UAAQ,OAAO,EAAE;AAAE,YAAM,OAAO,EAAE,WAAS,SAAO,WAAW,OAAO,IAAE,eAAe,SAAQ,KAAK;AAAA,IAAG;AAAC,WAAO;AAAA,EAAU;AAAE,EAAC,CAAC;AAAE,IAAI,wBAAsB,WAAW,EAAC,4CAA4C,SAAQ,QAAO;AAAC,MAAI,cAAY,oBAAoB,GAAE,QAAM,cAAc,GAAE,WAAS,CAAC,GAAE,SAAO,OAAO,KAAK,WAAW;AAAE,WAAS,QAAQ,IAAG;AAAC,QAAI,YAAU,YAAY,MAAK;AAAC,UAAI,OAAK,KAAK,CAAC;AAAE,aAAO,QAAM,OAAK,QAAM,KAAK,SAAO,MAAI,OAAK,OAAM,GAAG,IAAI;AAAA,IAAE;AAAE,WAAO,gBAAe,OAAK,UAAU,aAAW,GAAG,aAAY;AAAA,EAAS;AAAC,WAAS,YAAY,IAAG;AAAC,QAAI,YAAU,YAAY,MAAK;AAAC,UAAI,OAAK,KAAK,CAAC;AAAE,UAAG,QAAM,KAAK,QAAO;AAAK,WAAK,SAAO,MAAI,OAAK;AAAM,UAAI,SAAO,GAAG,IAAI;AAAE,UAAG,OAAO,UAAQ,SAAS,UAAQ,MAAI,OAAO,QAAO,KAAG,GAAE,KAAG,KAAI,KAAK,QAAO,EAAE,IAAE,KAAK,MAAM,OAAO,EAAE,CAAC;AAAE,aAAO;AAAA,IAAM;AAAE,WAAO,gBAAe,OAAK,UAAU,aAAW,GAAG,aAAY;AAAA,EAAS;AAAC,SAAO,QAAQ,eAAW;AAAC,aAAS,SAAS,IAAE,CAAC,GAAE,OAAO,eAAe,SAAS,SAAS,GAAE,YAAW,EAAC,OAAM,YAAY,SAAS,EAAE,SAAQ,CAAC,GAAE,OAAO,eAAe,SAAS,SAAS,GAAE,UAAS,EAAC,OAAM,YAAY,SAAS,EAAE,OAAM,CAAC;AAAE,QAAI,SAAO,MAAM,SAAS;AAAE,WAAO,KAAK,MAAM,EAAE,QAAQ,aAAS;AAAC,UAAI,KAAG,OAAO,OAAO;AAAE,eAAS,SAAS,EAAE,OAAO,IAAE,YAAY,EAAE,GAAE,SAAS,SAAS,EAAE,OAAO,EAAE,MAAI,QAAQ,EAAE;AAAA,IAAE,CAAC;AAAA,EAAE,CAAC;AAAE,SAAO,UAAQ;AAAS,EAAC,CAAC;AAAE,IAAI,uBAAqBA,SAAQ,sBAAsB,CAAC;AAAE,SAAS,IAAG;AAAC,UAAQ,IAAE,OAAO,UAAQ,SAAS,IAAG;AAAC,aAAQ,KAAG,GAAE,KAAG,UAAU,QAAO,MAAK;AAAC,UAAI,KAAG,UAAU,EAAE;AAAE,eAAQ,MAAM,GAAG,QAAO,UAAU,eAAe,KAAK,IAAG,EAAE,MAAI,GAAG,EAAE,IAAE,GAAG,EAAE;AAAA,IAAG;AAAC,WAAO;AAAA,EAAE,GAAG,MAAM,MAAK,SAAS;AAAC;AAAC,SAAS,EAAE,IAAG,IAAG;AAAC,MAAG,MAAI,KAAK,QAAO,CAAC;AAAE,MAAI,IAAG,IAAG,KAAG,CAAC,GAAE,KAAG,OAAO,KAAK,EAAE;AAAE,OAAI,KAAG,GAAE,KAAG,GAAG,QAAO,KAAK,IAAG,QAAQ,KAAG,GAAG,EAAE,CAAC,KAAG,MAAI,GAAG,EAAE,IAAE,GAAG,EAAE;AAAG,SAAO;AAAE;AAAC,SAAS,EAAE,IAAG;AAAC,MAAI,SAAG,qBAAO,EAAE,GAAE,SAAG,qBAAO,SAAS,IAAG;AAAC,OAAG,WAAS,GAAG,QAAQ,EAAE;AAAA,EAAE,CAAC;AAAE,SAAO,GAAG,UAAQ,IAAG,GAAG;AAAO;AAAC,IAAI,IAAE,SAAS,IAAG,IAAG,IAAG;AAAC,SAAO,OAAK,WAAS,KAAG,IAAG,OAAK,WAAS,KAAG,IAAG,KAAG,KAAG,KAAG,KAAG,KAAG,KAAG;AAAE;AAA5F,IAA8F,IAAE,SAAS,IAAG;AAAC,SAAO,aAAY;AAAE;AAAlI,IAAoI,IAAE,SAAS,IAAG;AAAC,SAAO,MAAI,GAAG,cAAc,eAAa;AAAI;AAAhM,IAAkM,IAAE,SAAS,IAAG,IAAG,IAAG;AAAC,MAAI,KAAG,GAAG,sBAAsB,GAAE,KAAG,EAAE,EAAE,IAAE,SAAS,IAAG,IAAG;AAAC,aAAQ,KAAG,GAAE,KAAG,GAAG,QAAO,KAAK,KAAG,GAAG,EAAE,EAAE,eAAa,GAAG,QAAO,GAAG,EAAE;AAAE,WAAO,GAAG,CAAC;AAAA,EAAC,EAAE,GAAG,SAAQ,EAAE,IAAE;AAAG,SAAO,EAAC,MAAK,GAAG,GAAG,SAAO,GAAG,OAAK,EAAE,EAAE,EAAE,gBAAc,GAAG,KAAK,GAAE,KAAI,GAAG,GAAG,SAAO,GAAG,MAAI,EAAE,EAAE,EAAE,gBAAc,GAAG,MAAM,EAAC;AAAC;AAAhf,IAAkf,IAAE,SAAS,IAAG;AAAC,GAAC,EAAE,EAAE,KAAG,GAAG,eAAe;AAAE;AAA7hB,IAA+hB,IAAE,aAAAC,QAAE,KAAK,SAAS,IAAG;AAAC,MAAI,KAAG,GAAG,QAAO,KAAG,GAAG,OAAM,KAAG,EAAE,IAAG,CAAC,UAAS,OAAO,CAAC,GAAE,SAAG,qBAAO,IAAI,GAAE,KAAG,EAAE,EAAE,GAAE,KAAG,EAAE,EAAE,GAAE,SAAG,qBAAO,IAAI,GAAE,SAAG,qBAAO,KAAE,GAAE,SAAG,sBAAQ,WAAU;AAAC,QAAI,KAAG,SAAS,IAAG;AAAC,QAAE,EAAE,IAAG,EAAE,EAAE,IAAE,GAAG,QAAQ,SAAO,IAAE,GAAG,UAAQ,MAAI,GAAG,UAAQ,GAAG,EAAE,GAAG,SAAQ,IAAG,GAAG,OAAO,CAAC,IAAE,GAAG,KAAE;AAAA,IAAE,GAAE,KAAG,WAAU;AAAC,aAAO,GAAG,KAAE;AAAA,IAAC;AAAE,aAAS,GAAG,IAAG;AAAC,UAAI,KAAG,GAAG,SAAQ,KAAG,EAAE,GAAG,OAAO,GAAE,KAAG,KAAG,GAAG,mBAAiB,GAAG;AAAoB,SAAG,KAAG,cAAY,aAAY,EAAE,GAAE,GAAG,KAAG,aAAW,WAAU,EAAE;AAAA,IAAE;AAAC,WAAO,CAAC,SAAS,IAAG;AAAC,UAAI,KAAG,GAAG,aAAY,KAAG,GAAG;AAAQ,UAAG,OAAK,EAAE,EAAE,GAAE,CAAC,SAAS,IAAG,IAAG;AAAC,eAAO,MAAI,CAAC,EAAE,EAAE;AAAA,MAAC,EAAE,IAAG,GAAG,OAAO,KAAG,KAAI;AAAC,YAAG,EAAE,EAAE,GAAE;AAAC,aAAG,UAAQ;AAAG,cAAI,KAAG,GAAG,kBAAgB,CAAC;AAAE,aAAG,WAAS,GAAG,UAAQ,GAAG,CAAC,EAAE;AAAA,QAAY;AAAC,WAAG,MAAM,GAAE,GAAG,EAAE,IAAG,IAAG,GAAG,OAAO,CAAC,GAAE,GAAG,IAAE;AAAA,MAAE;AAAA,IAAC,GAAE,SAAS,IAAG;AAAC,UAAI,KAAG,GAAG,SAAO,GAAG;AAAQ,WAAG,MAAI,KAAG,OAAK,GAAG,eAAe,GAAE,GAAG,EAAC,MAAK,OAAK,KAAG,OAAI,OAAK,KAAG,QAAK,GAAE,KAAI,OAAK,KAAG,OAAI,OAAK,KAAG,QAAK,EAAC,CAAC;AAAA,IAAG,GAAE,EAAE;AAAA,EAAC,GAAE,CAAC,IAAG,EAAE,CAAC,GAAE,KAAG,GAAG,CAAC,GAAE,KAAG,GAAG,CAAC,GAAE,KAAG,GAAG,CAAC;AAAE,aAAO,wBAAU,WAAU;AAAC,WAAO;AAAA,EAAE,GAAE,CAAC,EAAE,CAAC,GAAE,aAAAA,QAAE,cAAc,OAAM,EAAE,CAAC,GAAE,IAAG,EAAC,cAAa,IAAG,aAAY,IAAG,WAAU,+BAA8B,KAAI,IAAG,WAAU,IAAG,UAAS,GAAE,MAAK,SAAQ,CAAC,CAAC;AAAC,CAAC;AAA3oD,IAA6oD,IAAE,SAAS,IAAG;AAAC,SAAO,GAAG,OAAO,OAAO,EAAE,KAAK,GAAG;AAAC;AAA/rD,IAAisD,IAAE,SAAS,IAAG;AAAC,MAAI,KAAG,GAAG,OAAM,KAAG,GAAG,MAAK,KAAG,GAAG,KAAI,KAAG,OAAK,SAAO,MAAG,IAAG,KAAG,EAAE,CAAC,2BAA0B,GAAG,SAAS,CAAC;AAAE,SAAO,aAAAA,QAAE,cAAc,OAAM,EAAC,WAAU,IAAG,OAAM,EAAC,KAAI,MAAI,KAAG,KAAI,MAAK,MAAI,KAAG,IAAG,EAAC,GAAE,aAAAA,QAAE,cAAc,OAAM,EAAC,WAAU,gCAA+B,OAAM,EAAC,iBAAgB,GAAE,EAAC,CAAC,CAAC;AAAC;AAAz+D,IAA2+D,IAAE,SAAS,IAAG,IAAG,IAAG;AAAC,SAAO,OAAK,WAAS,KAAG,IAAG,OAAK,WAAS,KAAG,KAAK,IAAI,IAAG,EAAE,IAAG,KAAK,MAAM,KAAG,EAAE,IAAE;AAAE;AAAjlE,IAAmlE,IAAE,EAAC,MAAK,KAAG,MAAK,KAAI,KAAI,OAAK,IAAE,KAAK,IAAG;AAA1nE,IAA4nE,IAAE,SAAS,IAAG;AAAC,SAAO,EAAE,EAAE,EAAE,CAAC;AAAC;AAA1pE,IAA4pE,IAAE,SAAS,IAAG;AAAC,SAAO,GAAG,CAAC,MAAI,QAAM,KAAG,GAAG,UAAU,CAAC,IAAG,GAAG,SAAO,IAAE,EAAC,GAAE,SAAS,GAAG,CAAC,IAAE,GAAG,CAAC,GAAE,EAAE,GAAE,GAAE,SAAS,GAAG,CAAC,IAAE,GAAG,CAAC,GAAE,EAAE,GAAE,GAAE,SAAS,GAAG,CAAC,IAAE,GAAG,CAAC,GAAE,EAAE,GAAE,GAAE,GAAG,WAAS,IAAE,EAAE,SAAS,GAAG,CAAC,IAAE,GAAG,CAAC,GAAE,EAAE,IAAE,KAAI,CAAC,IAAE,EAAC,IAAE,EAAC,GAAE,SAAS,GAAG,UAAU,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,SAAS,GAAG,UAAU,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,SAAS,GAAG,UAAU,GAAE,CAAC,GAAE,EAAE,GAAE,GAAE,GAAG,WAAS,IAAE,EAAE,SAAS,GAAG,UAAU,GAAE,CAAC,GAAE,EAAE,IAAE,KAAI,CAAC,IAAE,EAAC;AAAC;AAArgF,IAAugF,IAAE,SAAS,IAAG,IAAG;AAAC,SAAO,OAAK,WAAS,KAAG,QAAO,OAAO,EAAE,KAAG,EAAE,EAAE,KAAG;AAAE;AAA7kF,IAA+kF,IAAE,SAAS,IAAG;AAAC,MAAI,KAAG,6HAA6H,KAAK,EAAE;AAAE,SAAO,KAAG,EAAE,EAAC,GAAE,EAAE,GAAG,CAAC,GAAE,GAAG,CAAC,CAAC,GAAE,GAAE,OAAO,GAAG,CAAC,CAAC,GAAE,GAAE,OAAO,GAAG,CAAC,CAAC,GAAE,GAAE,GAAG,CAAC,MAAI,SAAO,IAAE,OAAO,GAAG,CAAC,CAAC,KAAG,GAAG,CAAC,IAAE,MAAI,GAAE,CAAC,IAAE,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC;AAAC;AAAE,IAAI,IAAE,SAAS,IAAG;AAAC,MAAI,KAAG,GAAG,GAAE,KAAG,GAAG;AAAE,SAAO,EAAC,GAAE,GAAG,GAAE,IAAG,OAAK,KAAG,KAAG,KAAG,MAAI,MAAI,OAAK,IAAE,IAAE,MAAI,KAAG,MAAI,MAAI,GAAE,GAAE,KAAG,IAAG,GAAE,GAAG,EAAC;AAAC;AAAxH,IAA0H,IAAE,SAAS,IAAG;AAAC,SAAO,EAAE,EAAE,EAAE,CAAC;AAAC;AAAxJ,IAA0J,IAAE,SAAS,IAAG;AAAC,MAAI,KAAG,GAAG,GAAE,KAAG,GAAG,GAAE,KAAG,GAAG,GAAE,MAAI,MAAI,MAAI,KAAG;AAAI,SAAO,EAAC,GAAE,EAAE,GAAG,CAAC,GAAE,GAAE,EAAE,KAAG,KAAG,KAAG,MAAI,KAAG,KAAG,OAAK,MAAI,MAAI,KAAG,MAAI,MAAI,MAAI,CAAC,GAAE,GAAE,EAAE,KAAG,CAAC,GAAE,GAAE,EAAE,IAAG,CAAC,EAAC;AAAC;AAApT,IAAsT,IAAE,SAAS,IAAG;AAAC,MAAI,KAAG,EAAE,EAAE;AAAE,SAAO,SAAO,GAAG,IAAE,OAAK,GAAG,IAAE,QAAM,GAAG,IAAE;AAAI;AAA9X,IAAgY,IAAE,SAAS,IAAG;AAAC,MAAI,KAAG,EAAE,EAAE;AAAE,SAAO,UAAQ,GAAG,IAAE,OAAK,GAAG,IAAE,QAAM,GAAG,IAAE,QAAM,GAAG,IAAE;AAAG;AAAnd,IAAqd,IAAE,SAAS,IAAG;AAAC,MAAI,KAAG,GAAG,GAAE,KAAG,GAAG,GAAE,KAAG,GAAG,GAAE,KAAG,GAAG;AAAE,OAAG,KAAG,MAAI,GAAE,MAAI,KAAI,MAAI;AAAI,MAAI,KAAG,KAAK,MAAM,EAAE,GAAE,KAAG,MAAI,IAAE,KAAI,KAAG,MAAI,KAAG,KAAG,MAAI,KAAI,KAAG,MAAI,KAAG,IAAE,KAAG,MAAI,KAAI,KAAG,KAAG;AAAE,SAAO,EAAC,GAAE,EAAE,MAAI,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,EAAE,EAAE,CAAC,GAAE,GAAE,EAAE,MAAI,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,EAAE,EAAE,CAAC,GAAE,GAAE,EAAE,MAAI,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,EAAE,EAAE,CAAC,GAAE,GAAE,EAAE,IAAG,CAAC,EAAC;AAAC;AAAE,IAAI,IAAE,SAAS,IAAG;AAAC,MAAI,KAAG,iHAAiH,KAAK,EAAE;AAAE,SAAO,KAAG,EAAE,EAAC,GAAE,OAAO,GAAG,CAAC,CAAC,KAAG,GAAG,CAAC,IAAE,MAAI,MAAI,IAAG,GAAE,OAAO,GAAG,CAAC,CAAC,KAAG,GAAG,CAAC,IAAE,MAAI,MAAI,IAAG,GAAE,OAAO,GAAG,CAAC,CAAC,KAAG,GAAG,CAAC,IAAE,MAAI,MAAI,IAAG,GAAE,GAAG,CAAC,MAAI,SAAO,IAAE,OAAO,GAAG,CAAC,CAAC,KAAG,GAAG,CAAC,IAAE,MAAI,GAAE,CAAC,IAAE,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC;AAAC;AAAE,IAAI,IAAE,SAAS,IAAG;AAAC,MAAI,KAAG,GAAG,SAAS,EAAE;AAAE,SAAO,GAAG,SAAO,IAAE,MAAI,KAAG;AAAE;AAAtE,IAAwE,IAAE,SAAS,IAAG;AAAC,MAAI,KAAG,GAAG,GAAE,KAAG,GAAG,GAAE,KAAG,GAAG,GAAE,KAAG,GAAG,GAAE,KAAG,KAAG,IAAE,EAAE,EAAE,MAAI,EAAE,CAAC,IAAE;AAAG,SAAO,MAAI,EAAE,EAAE,IAAE,EAAE,EAAE,IAAE,EAAE,EAAE,IAAE;AAAE;AAAlL,IAAoL,IAAE,SAAS,IAAG;AAAC,MAAI,KAAG,GAAG,GAAE,KAAG,GAAG,GAAE,KAAG,GAAG,GAAE,KAAG,GAAG,GAAE,KAAG,KAAK,IAAI,IAAG,IAAG,EAAE,GAAE,KAAG,KAAG,KAAK,IAAI,IAAG,IAAG,EAAE,GAAE,KAAG,KAAG,OAAK,MAAI,KAAG,MAAI,KAAG,OAAK,KAAG,KAAG,KAAG,MAAI,KAAG,KAAG,KAAG,MAAI,KAAG;AAAE,SAAO,EAAC,GAAE,EAAE,MAAI,KAAG,IAAE,KAAG,IAAE,GAAG,GAAE,GAAE,EAAE,KAAG,KAAG,KAAG,MAAI,CAAC,GAAE,GAAE,EAAE,KAAG,MAAI,GAAG,GAAE,GAAE,GAAE;AAAC;AAAE,IAAI,IAAE,aAAAA,QAAE,KAAK,SAAS,IAAG;AAAC,MAAI,KAAG,GAAG,KAAI,KAAG,GAAG,UAAS,KAAG,EAAE,CAAC,uBAAsB,GAAG,SAAS,CAAC;AAAE,SAAO,aAAAA,QAAE,cAAc,OAAM,EAAC,WAAU,GAAE,GAAE,aAAAA,QAAE,cAAc,GAAE,EAAC,QAAO,SAAS,IAAG;AAAC,OAAG,EAAC,GAAE,MAAI,GAAG,KAAI,CAAC;AAAA,EAAE,GAAE,OAAM,SAAS,IAAG;AAAC,OAAG,EAAC,GAAE,EAAE,KAAG,MAAI,GAAG,MAAK,GAAE,GAAG,EAAC,CAAC;AAAA,EAAE,GAAE,cAAa,OAAM,iBAAgB,EAAE,EAAE,GAAE,iBAAgB,OAAM,iBAAgB,IAAG,GAAE,aAAAA,QAAE,cAAc,GAAE,EAAC,WAAU,+BAA8B,MAAK,KAAG,KAAI,OAAM,EAAE,EAAC,GAAE,IAAG,GAAE,KAAI,GAAE,KAAI,GAAE,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;AAAC,CAAC;AAA/b,IAAic,IAAE,aAAAA,QAAE,KAAK,SAAS,IAAG;AAAC,MAAI,KAAG,GAAG,MAAK,KAAG,GAAG,UAAS,KAAG,EAAC,iBAAgB,EAAE,EAAC,GAAE,GAAG,GAAE,GAAE,KAAI,GAAE,KAAI,GAAE,EAAC,CAAC,EAAC;AAAE,SAAO,aAAAA,QAAE,cAAc,OAAM,EAAC,WAAU,8BAA6B,OAAM,GAAE,GAAE,aAAAA,QAAE,cAAc,GAAE,EAAC,QAAO,SAAS,IAAG;AAAC,OAAG,EAAC,GAAE,MAAI,GAAG,MAAK,GAAE,MAAI,MAAI,GAAG,IAAG,CAAC;AAAA,EAAE,GAAE,OAAM,SAAS,IAAG;AAAC,OAAG,EAAC,GAAE,EAAE,GAAG,IAAE,MAAI,GAAG,MAAK,GAAE,GAAG,GAAE,GAAE,EAAE,GAAG,IAAE,MAAI,GAAG,KAAI,GAAE,GAAG,EAAC,CAAC;AAAA,EAAE,GAAE,cAAa,SAAQ,kBAAiB,gBAAc,EAAE,GAAG,CAAC,IAAE,mBAAiB,EAAE,GAAG,CAAC,IAAE,IAAG,GAAE,aAAAA,QAAE,cAAc,GAAE,EAAC,WAAU,sCAAqC,KAAI,IAAE,GAAG,IAAE,KAAI,MAAK,GAAG,IAAE,KAAI,OAAM,EAAE,EAAE,EAAC,CAAC,CAAC,CAAC;AAAC,CAAC;AAA99B,IAAg+B,IAAE,SAAS,IAAG,IAAG;AAAC,MAAG,OAAK,GAAG,QAAO;AAAG,WAAQ,MAAM,GAAG,KAAG,GAAG,EAAE,MAAI,GAAG,EAAE,EAAE,QAAO;AAAG,SAAO;AAAE;AAA9jC,IAAgkC,IAAE,SAAS,IAAG,IAAG;AAAC,SAAO,GAAG,QAAQ,OAAM,EAAE,MAAI,GAAG,QAAQ,OAAM,EAAE;AAAC;AAApoC,IAAsoC,IAAE,SAAS,IAAG,IAAG;AAAC,SAAO,GAAG,YAAY,MAAI,GAAG,YAAY,KAAG,EAAE,EAAE,EAAE,GAAE,EAAE,EAAE,CAAC;AAAC;AAAE,SAAS,EAAE,IAAG,IAAG,IAAG;AAAC,MAAI,KAAG,EAAE,EAAE,GAAE,SAAG,uBAAS,WAAU;AAAC,WAAO,GAAG,OAAO,EAAE;AAAA,EAAC,CAAC,GAAE,KAAG,GAAG,CAAC,GAAE,KAAG,GAAG,CAAC,GAAE,SAAG,qBAAO,EAAC,OAAM,IAAG,MAAK,GAAE,CAAC;AAAE,8BAAU,WAAU;AAAC,QAAG,CAAC,GAAG,MAAM,IAAG,GAAG,QAAQ,KAAK,GAAE;AAAC,UAAI,KAAG,GAAG,OAAO,EAAE;AAAE,SAAG,UAAQ,EAAC,MAAK,IAAG,OAAM,GAAE,GAAE,GAAG,EAAE;AAAA,IAAE;AAAA,EAAC,GAAE,CAAC,IAAG,EAAE,CAAC,OAAE,wBAAU,WAAU;AAAC,QAAI;AAAG,MAAE,IAAG,GAAG,QAAQ,IAAI,KAAG,GAAG,MAAM,KAAG,GAAG,SAAS,EAAE,GAAE,GAAG,QAAQ,KAAK,MAAI,GAAG,UAAQ,EAAC,MAAK,IAAG,OAAM,GAAE,GAAE,GAAG,EAAE;AAAA,EAAG,GAAE,CAAC,IAAG,IAAG,EAAE,CAAC;AAAE,MAAI,SAAG,0BAAY,SAAS,IAAG;AAAC,OAAG,SAAS,IAAG;AAAC,aAAO,OAAO,OAAO,CAAC,GAAE,IAAG,EAAE;AAAA,IAAC,CAAC;AAAA,EAAE,GAAE,CAAC,CAAC;AAAE,SAAO,CAAC,IAAG,EAAE;AAAC;AAAC,IAAI,IAAE,OAAO,SAAO,MAAI,+BAAgB;AAAxC,IAAkD,IAAE,WAAU;AAAC,SAAQ,OAAO,oBAAkB,MAAI,oBAAkB;AAAO;AAAE,IAAI,IAAE,oBAAI;AAAV,IAAc,IAAE,SAAS,IAAG;AAAC,IAAE,WAAU;AAAC,QAAI,KAAG,GAAG,UAAQ,GAAG,QAAQ,gBAAc;AAAS,QAAG,OAAK,UAAQ,CAAC,EAAE,IAAI,EAAE,GAAE;AAAC,UAAI,KAAG,GAAG,cAAc,OAAO;AAAE,SAAG,YAAU,itDAAgtD,EAAE,IAAI,IAAG,EAAE;AAAE,UAAI,KAAG,EAAE;AAAE,YAAI,GAAG,aAAa,SAAQ,EAAE,GAAE,GAAG,KAAK,YAAY,EAAE;AAAA,IAAE;AAAA,EAAC,GAAE,CAAC,CAAC;AAAE;AAA/8D,IAAi9D,IAAE,SAAS,IAAG;AAAC,MAAI,KAAG,GAAG,WAAU,KAAG,GAAG,YAAW,KAAG,GAAG,OAAM,KAAG,OAAK,SAAO,GAAG,eAAa,IAAG,KAAG,GAAG,UAAS,KAAG,EAAE,IAAG,CAAC,aAAY,cAAa,SAAQ,UAAU,CAAC,GAAE,SAAG,qBAAO,IAAI;AAAE,IAAE,EAAE;AAAE,MAAI,KAAG,EAAE,IAAG,IAAG,EAAE,GAAE,KAAG,GAAG,CAAC,GAAE,KAAG,GAAG,CAAC,GAAE,KAAG,EAAE,CAAC,kBAAiB,EAAE,CAAC;AAAE,SAAO,aAAAA,QAAE,cAAc,OAAM,EAAE,CAAC,GAAE,IAAG,EAAC,KAAI,IAAG,WAAU,GAAE,CAAC,GAAE,aAAAA,QAAE,cAAc,GAAE,EAAC,MAAK,IAAG,UAAS,GAAE,CAAC,GAAE,aAAAA,QAAE,cAAc,GAAE,EAAC,KAAI,GAAG,GAAE,UAAS,IAAG,WAAU,+BAA8B,CAAC,CAAC;AAAC;AAAx4E,IAA04E,IAAE,EAAC,cAAa,OAAM,QAAO,GAAE,UAAS,SAAS,IAAG;AAAC,SAAO,EAAE,EAAC,GAAE,GAAG,GAAE,GAAE,GAAG,GAAE,GAAE,GAAG,GAAE,GAAE,EAAC,CAAC;AAAC,GAAE,OAAM,EAAC;AAA5+E,IAA8+E,IAAE,SAAS,IAAG;AAAC,SAAO,aAAAA,QAAE,cAAc,GAAE,EAAE,CAAC,GAAE,IAAG,EAAC,YAAW,EAAC,CAAC,CAAC;AAAC;AAA9iF,IAAgjF,KAAG,SAAS,IAAG;AAAC,MAAI,KAAG,GAAG,WAAU,KAAG,GAAG,MAAK,KAAG,GAAG,UAAS,KAAG,EAAC,iBAAgB,4BAA0B,EAAE,OAAO,OAAO,CAAC,GAAE,IAAG,EAAC,GAAE,EAAC,CAAC,CAAC,IAAE,OAAK,EAAE,OAAO,OAAO,CAAC,GAAE,IAAG,EAAC,GAAE,EAAC,CAAC,CAAC,IAAE,IAAG,GAAE,KAAG,EAAE,CAAC,yBAAwB,EAAE,CAAC,GAAE,KAAG,EAAE,MAAI,GAAG,CAAC;AAAE,SAAO,aAAAA,QAAE,cAAc,OAAM,EAAC,WAAU,GAAE,GAAE,aAAAA,QAAE,cAAc,OAAM,EAAC,WAAU,kCAAiC,OAAM,GAAE,CAAC,GAAE,aAAAA,QAAE,cAAc,GAAE,EAAC,QAAO,SAAS,IAAG;AAAC,OAAG,EAAC,GAAE,GAAG,KAAI,CAAC;AAAA,EAAE,GAAE,OAAM,SAAS,IAAG;AAAC,OAAG,EAAC,GAAE,EAAE,GAAG,IAAE,GAAG,IAAI,EAAC,CAAC;AAAA,EAAE,GAAE,cAAa,SAAQ,kBAAiB,KAAG,KAAI,iBAAgB,IAAG,iBAAgB,KAAI,iBAAgB,MAAK,GAAE,aAAAA,QAAE,cAAc,GAAE,EAAC,WAAU,iCAAgC,MAAK,GAAG,GAAE,OAAM,EAAE,EAAE,EAAC,CAAC,CAAC,CAAC;AAAC;AAAprG,IAAsrG,KAAG,SAAS,IAAG;AAAC,MAAI,KAAG,GAAG,WAAU,KAAG,GAAG,YAAW,KAAG,GAAG,OAAM,KAAG,OAAK,SAAO,GAAG,eAAa,IAAG,KAAG,GAAG,UAAS,KAAG,EAAE,IAAG,CAAC,aAAY,cAAa,SAAQ,UAAU,CAAC,GAAE,SAAG,qBAAO,IAAI;AAAE,IAAE,EAAE;AAAE,MAAI,KAAG,EAAE,IAAG,IAAG,EAAE,GAAE,KAAG,GAAG,CAAC,GAAE,KAAG,GAAG,CAAC,GAAE,KAAG,EAAE,CAAC,kBAAiB,EAAE,CAAC;AAAE,SAAO,aAAAA,QAAE,cAAc,OAAM,EAAE,CAAC,GAAE,IAAG,EAAC,KAAI,IAAG,WAAU,GAAE,CAAC,GAAE,aAAAA,QAAE,cAAc,GAAE,EAAC,MAAK,IAAG,UAAS,GAAE,CAAC,GAAE,aAAAA,QAAE,cAAc,GAAE,EAAC,KAAI,GAAG,GAAE,UAAS,GAAE,CAAC,GAAE,aAAAA,QAAE,cAAc,IAAG,EAAC,MAAK,IAAG,UAAS,IAAG,WAAU,+BAA8B,CAAC,CAAC;AAAC;AAAE,IAAI,KAAG,EAAC,cAAa,sBAAqB,QAAO,GAAE,UAAS,GAAE,OAAM,EAAC;AAArE,IAAuE,KAAG,SAAS,IAAG;AAAC,SAAO,aAAAA,QAAE,cAAc,IAAG,EAAE,CAAC,GAAE,IAAG,EAAC,YAAW,GAAE,CAAC,CAAC;AAAC;AAAE,IAAI,KAAG,EAAC,cAAa,oBAAmB,QAAO,GAAE,UAAS,SAAS,IAAG;AAAC,MAAI,KAAG,EAAE,EAAE;AAAE,SAAO,UAAQ,GAAG,IAAE,OAAK,GAAG,IAAE,OAAK,GAAG,IAAE,OAAK,GAAG,IAAE;AAAG,GAAE,OAAM,EAAC;AAAlJ,IAAoJ,KAAG,SAAS,IAAG;AAAC,SAAO,aAAAA,QAAE,cAAc,IAAG,EAAE,CAAC,GAAE,IAAG,EAAC,YAAW,GAAE,CAAC,CAAC;AAAC;AAAE,IAAI,UAAQ,GAAO,IAAI,EAAC,UAAS,YAAW,UAAS,KAAI,2BAA0B,EAAC,SAAQ,IAAE,EAAC,CAAC;AAAhG,IAAkG,gBAAc,GAAO,EAAW,EAAE,EAAC,UAAS,YAAW,QAAO,GAAE,KAAI,GAAE,MAAK,GAAE,0BAAyB,EAAC,QAAO,cAAa,EAAC,CAAC;AAA/N,IAAiO,iBAAe,GAAO,IAAI,EAAC,OAAM,KAAI,QAAO,GAAE,+BAA8B,EAAC,cAAa,cAAa,GAAE,wBAAuB,EAAC,WAAU,kCAAiC,GAAE,iCAAgC,EAAC,cAAa,cAAa,EAAC,CAAC;AAA5c,IAA8c,OAAK,GAAO,EAAW,EAAE,CAAC,EAAC,MAAK,OAAK,EAAC,YAAW,MAAM,WAAW,MAAM,KAAI,EAAE;AAA5hB,IAA8hB,WAAS,GAAO,IAAI,EAAC,SAAQ,QAAO,qBAAoB,mBAAkB,KAAI,GAAE,SAAQ,GAAE,WAAU,GAAE,OAAM,IAAG,CAAC;AAA9oB,IAAgpB,cAAY,GAAO,IAAI,CAAC,EAAC,OAAM,OAAM,OAAK,EAAC,OAAM,IAAG,QAAO,IAAG,WAAU,SAAO,GAAG,MAAM,cAAc,qBAAqB,MAAM,cAAc,iBAAe,GAAG,MAAM,cAAc,oBAAmB,cAAa,MAAM,gBAAe,EAAE;AAA53B,IAA83B,mBAAiB;AAA/4B,IAAojC,SAAO,CAAC,EAAC,OAAM,OAAM,GAAG,MAAK,MAAI;AAAC,MAAI,kBAAgB,mBAAmB,KAAK,KAAK,KAAK,MAAM,gBAAgB;AAAgC,SAAO,aAAAA,QAAE,cAAc,aAAY,EAAC,GAAG,OAAM,OAAM,EAAC,GAAG,OAAM,gBAAe,EAAC,CAAC;AAAC;AAAjxC,IAAmxC,QAAM,GAAO,GAAK,KAAK,EAAE,CAAC,EAAC,OAAM,SAAQ,OAAK,EAAC,OAAM,QAAO,aAAY,IAAG,cAAa,IAAG,WAAU,cAAa,YAAW,MAAM,WAAW,MAAM,KAAI,EAAE;AAA76C,IAA+6C,aAAW,GAAO,UAAU,EAAE,CAAC,EAAC,MAAK,OAAK,EAAC,UAAS,YAAW,QAAO,GAAE,KAAI,GAAE,OAAM,GAAE,OAAM,IAAG,QAAO,IAAG,SAAQ,GAAE,WAAU,cAAa,QAAO,WAAU,OAAM,MAAM,MAAM,MAAK,EAAE;AAAnmD,IAAqmD,cAAY,kBAAc,YAAY,MAAI,OAAM,YAAY,MAAI,OAAM,YAAY,MAAI,OAAM,cAAc,cAAY,CAAC,CAAC;AAA7tD,IAA+tD,eAAa,OAAO,OAAO,UAAU;AAApwD,IAAswD,eAAa;AAAnxD,IAA60D,aAAW;AAAx1D,IAA45D,aAAW;AAAv6D,IAA6+D,aAAW;AAAx/D,IAA+hE,kBAAgB;AAA/iE,IAA0kE,cAAY,EAAC,KAAI,GAAE,KAAI,IAAG,KAAI,GAAE;AAA1mE,IAA4mE,gBAAc,EAAC,KAAI,eAAc,KAAI,oBAAmB,KAAI,qBAAoB;AAA5rE,IAA8rE,eAAa,WAAO;AAAC,MAAI,QAAM,OAAO,MAAM,YAAY;AAAE,MAAG,CAAC,MAAM,QAAO,CAAC,GAAE,GAAE,GAAE,CAAC;AAAE,MAAG,CAAC,EAAC,IAAG,IAAG,IAAG,KAAG,CAAC,IAAE;AAAM,SAAO,CAAC,IAAG,IAAG,IAAG,EAAE,EAAE,IAAI,MAAM;AAAC;AAA70E,IAA+0E,WAAS,WAAO;AAAC,MAAG,CAAC,IAAG,IAAG,IAAG,EAAE,IAAE,aAAa,KAAK,GAAE,CAAC,IAAG,IAAG,EAAE,IAAE,qBAAqB,QAAQ,IAAI,IAAI,CAAC,IAAG,IAAG,EAAE,CAAC,KAAG,CAAC,GAAE,GAAE,CAAC;AAAE,SAAO,EAAC,OAAM,MAAG,OAAM,SAAQ,qBAAqB,QAAQ,IAAI,QAAQ,CAAC,IAAG,IAAG,EAAE,CAAC,GAAE,YAAW,OAAM,KAAI,OAAM,KAAI,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAI,KAAI,IAAI,qBAAqB,QAAQ,IAAI,IAAI,CAAC,IAAG,IAAG,EAAE,CAAC,EAAE,YAAY,CAAC,GAAE;AAAC;AAA3qF,IAA6qF,WAAS,WAAO;AAAC,MAAG,CAAC,IAAG,IAAG,IAAG,EAAE,IAAE,aAAa,KAAK,GAAE,CAAC,IAAG,IAAG,EAAE,IAAE,qBAAqB,QAAQ,IAAI,IAAI,CAAC,IAAG,IAAG,EAAE,CAAC,KAAG,CAAC,GAAE,GAAE,CAAC;AAAE,SAAO,EAAC,OAAM,MAAG,OAAM,SAAQ,qBAAqB,QAAQ,IAAI,QAAQ,CAAC,IAAG,IAAG,EAAE,CAAC,GAAE,YAAW,OAAM,KAAI,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAI,KAAI,OAAM,KAAI,IAAI,qBAAqB,QAAQ,IAAI,IAAI,CAAC,IAAG,IAAG,EAAE,CAAC,EAAE,YAAY,CAAC,GAAE;AAAC;AAAvgG,IAAygG,oBAAkB,WAAO;AAAC,MAAI,QAAM,MAAM,QAAQ,KAAI,EAAE,GAAE,MAAI,qBAAqB,QAAQ,QAAQ,IAAI,KAAK,KAAG,qBAAqB,QAAQ,IAAI,IAAI,KAAK,GAAE,MAAI,qBAAqB,QAAQ,IAAI,IAAI,GAAG,GAAE,SAAO;AAAM,gBAAc,KAAK,KAAK,IAAE,SAAO,QAAM,WAAW,KAAK,KAAK,MAAI,SAAO,IAAI,KAAK;AAAI,MAAI,QAAM;AAAG,MAAG,OAAO,WAAW,GAAG,EAAE,SAAM,WAAW,KAAK,MAAM;AAAA,MAAO,KAAG;AAAC,yBAAqB,QAAQ,QAAQ,IAAI,MAAM;AAAA,EAAE,QAAM;AAAC,YAAM;AAAA,EAAG;AAAC,SAAO,EAAC,OAAM,OAAM,QAAO,SAAQ,qBAAqB,QAAQ,IAAI,QAAQ,GAAG,GAAE,YAAW,OAAM,KAAI,QAAQ,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,QAAO,KAAI,QAAQ,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,SAAQ,KAAI,OAAM;AAAC;AAAhpH,IAAkpH,aAAW,WAAO;AAAC,MAAG,MAAM,QAAO,WAAW,KAAK,KAAK,IAAE,SAAS,KAAK,IAAE,WAAW,KAAK,KAAK,IAAE,SAAS,KAAK,IAAE,kBAAkB,KAAK;AAAC;AAA3xH,IAA6xH,eAAa,CAAC,OAAM,OAAM,eAAa;AAAC,MAAG,CAAC,SAAO,CAAC,OAAO,MAAM,QAAO,cAAc,UAAU;AAAE,MAAG,eAAa,MAAM,QAAO,QAAQ,UAAU,KAAG,cAAc,UAAU;AAAE,MAAG,CAAC,MAAM,IAAI,WAAW,GAAG,EAAE,KAAG;AAAC,WAAO,IAAI,qBAAqB,QAAQ,QAAQ,IAAI,MAAM,GAAG,CAAC;AAAA,EAAE,QAAM;AAAC,WAAO,cAAc;AAAA,EAAG;AAAC,MAAI,QAAM,MAAM,IAAI,MAAM,eAAe;AAAE,MAAG,CAAC,MAAM,QAAO,WAAW,KAAK,MAAM,GAAG,IAAE,MAAM,MAAI,cAAc;AAAI,MAAG,CAAC,IAAG,IAAG,EAAE,IAAE,MAAM,CAAC,EAAE,MAAM,EAAE;AAAE,SAAO,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAAE;AAAzwI,IAA2wI,gBAAc,CAAC,cAAa,aAAW;AAAC,MAAG,CAAC,OAAM,QAAQ,QAAE,uBAAS,gBAAc,EAAE,GAAE,CAAC,OAAM,QAAQ,QAAE,uBAAS,MAAI,WAAW,KAAK,CAAC,GAAE,CAAC,YAAW,aAAa,QAAE,uBAAS,OAAO,cAAY,KAAK;AAAE,8BAAU,MAAI;AAAC,QAAI,YAAU,gBAAc,IAAG,YAAU,WAAW,SAAS;AAAE,aAAS,SAAS,GAAE,SAAS,SAAS,GAAE,cAAc,WAAW,cAAY,KAAK;AAAA,EAAE,GAAE,CAAC,YAAY,CAAC;AAAE,MAAI,gBAAU,sBAAQ,MAAI,aAAa,OAAM,OAAM,UAAU,EAAE,YAAY,GAAE,CAAC,OAAM,OAAM,UAAU,CAAC,GAAE,kBAAY,0BAAY,YAAQ;AAAC,QAAI,SAAO,WAAW,MAAM,GAAE,KAAG,QAAQ,SAAO,UAAQ;AAAG,aAAS,EAAE,GAAE,OAAK,OAAK,SAAS,MAAM,GAAE,SAAS,MAAM,IAAG,WAAS,SAAS,MAAM,GAAE,cAAc,OAAO,UAAU,GAAE,SAAS,OAAO,KAAK;AAAA,EAAG,GAAE,CAAC,QAAQ,CAAC,GAAE,sBAAgB,0BAAY,MAAI;AAAC,QAAI,aAAW,aAAa,QAAQ,UAAU,IAAE,KAAG,aAAa,QAAO,YAAU,aAAa,SAAS;AAAE,kBAAc,SAAS;AAAE,QAAI,eAAa,QAAQ,SAAS,KAAG;AAAG,aAAS,YAAY,GAAE,SAAS,YAAY;AAAA,EAAE,GAAE,CAAC,OAAM,YAAW,QAAQ,CAAC;AAAE,SAAO,EAAC,OAAM,WAAU,aAAY,OAAM,YAAW,gBAAe;AAAC;AAAh0K,IAAk0K,KAAG,WAAO,MAAM,QAAQ,OAAM,EAAE,EAAE,YAAY;AAAh3K,IAAk3K,aAAW,CAAC,cAAa,cAAa,eAAa;AAAC,MAAG,CAAC,gBAAe,iBAAiB,QAAE,uBAAS,cAAc,QAAM,CAAC,YAAY,IAAE,CAAC,CAAC;AAAE,8BAAU,MAAI;AAAC,qBAAe,UAAQ,kBAAkB,CAAC,CAAC;AAAA,EAAE,GAAE,CAAC,YAAY,CAAC;AAAE,MAAI,cAAQ,sBAAQ,OAAK,gBAAc,CAAC,GAAG,IAAI,YAAQ,OAAO,UAAQ,WAAS,WAAW,MAAM,IAAE,OAAO,QAAM,EAAC,GAAG,WAAW,OAAO,KAAK,GAAE,SAAQ,OAAO,MAAK,IAAE,WAAW,OAAO,KAAK,CAAC,EAAE,OAAO,cAAc,EAAE,OAAO,OAAO,EAAE,MAAM,GAAG,GAAE,CAAC,cAAa,cAAc,CAAC,GAAE,gBAAU,0BAAY,WAAO;AAAC,WAAO,UAAQ,QAAQ,KAAK,YAAQ,UAAQ,OAAO,UAAU,KAAG,GAAG,OAAO,UAAU,KAAG,EAAE,MAAI,GAAG,MAAM,UAAU,KAAG,EAAE,CAAC,KAAG,kBAAkB,SAAK,IAAI,OAAO,KAAK,CAAC;AAAA,EAAG,GAAE,CAAC,YAAW,OAAO,CAAC;AAAE,SAAO,EAAC,SAAQ,UAAS;AAAC;AAA3kM,IAA6kM,eAAa,CAAC,EAAC,MAAK,OAAM,cAAa,UAAS,SAAQ,QAAO,cAAa,YAAU,OAAG,QAAO,MAAI;AAAC,MAAI,wBAAkB,0BAAY,UAAS,UAAS,GAAG,GAAE,CAAC,QAAQ,CAAC,GAAE,EAAC,OAAM,WAAU,aAAY,OAAM,YAAW,gBAAe,IAAE,cAAc,cAAa,iBAAiB,GAAE,EAAC,SAAQ,UAAS,IAAE,WAAW,gBAAc,CAAC,GAAE,OAAM,UAAU,GAAE,SAAO,YAAY,UAAU,GAAE,WAAS,CAAC,CAAC,SAAS,OAAO;AAAS,SAAO,aAAAA,QAAE,cAAc,SAAQ,EAAC,iBAAgB,SAAQ,GAAE,aAAAA,QAAE,cAAc,eAAc,EAAC,WAAU,SAAQ,WAAS,OAAK,QAAO,qBAAoB,MAAG,iBAAgB,MAAI,SAAO,UAAU,KAAK,GAAE,SAAQ,aAAAA,QAAE,cAAc,gBAAe,MAAK,aAAAA,QAAE,cAAc,QAAO,EAAC,OAAM,cAAY,gBAAc,YAAU,WAAU,UAAS,aAAY,SAAQ,OAAM,CAAC,GAAE,QAAQ,SAAO,KAAG,aAAAA,QAAE,cAAc,UAAS,MAAK,QAAQ,IAAI,CAAC,QAAO,UAAQ,aAAAA,QAAE,cAAc,IAAY,EAAC,KAAI,GAAG,QAAQ,SAAO,KAAK,IAAI,KAAK,IAAG,WAAU,OAAG,SAAQ,aAAAA,QAAE,cAAc,MAAK,EAAC,MAAK,QAAQ,WAAS,QAAQ,SAAO,GAAE,CAAC,EAAC,GAAE,aAAAA,QAAE,cAAc,QAAO,EAAC,OAAM,SAAS,UAAU,KAAG,IAAG,QAAO,CAAC,EAAE,SAAO,UAAQ,OAAO,UAAU,KAAG,GAAG,OAAO,UAAU,KAAG,EAAE,MAAI,GAAG,MAAM,UAAU,CAAC,IAAG,SAAQ,MAAI,UAAQ,YAAY,OAAO,SAAO,EAAE,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,GAAE,aAAAA,QAAE,cAAc,QAAO,EAAC,OAAM,WAAU,OAAM,EAAC,QAAO,EAAC,EAAC,CAAC,CAAC,GAAE,aAAAA,QAAE,cAAc,OAAM,EAAC,IAAG,aAAa,IAAI,GAAE,OAAM,UAAS,QAAI,YAAY,GAAG,OAAO,KAAK,GAAE,SAAQ,QAAI,GAAG,OAAO,OAAO,GAAE,UAAS,UAAS,aAAY,kBAAiB,CAAC,GAAE,QAAM,aAAAA,QAAE,cAAc,YAAW,EAAC,SAAQ,gBAAe,CAAC,IAAE,IAAI;AAAC;AAA5iP,IAA8iP,gBAAc;", "names": ["__toESM", "e"]}