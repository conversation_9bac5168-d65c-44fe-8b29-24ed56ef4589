import {
  require_overArg
} from "./chunk-DP5WW2PA.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// node_modules/lodash/_getPrototype.js
var require_getPrototype = __commonJS({
  "node_modules/lodash/_getPrototype.js"(exports, module) {
    var overArg = require_overArg();
    var getPrototype = overArg(Object.getPrototypeOf, Object);
    module.exports = getPrototype;
  }
});

export {
  require_getPrototype
};
//# sourceMappingURL=chunk-D6LLYL4A.js.map
