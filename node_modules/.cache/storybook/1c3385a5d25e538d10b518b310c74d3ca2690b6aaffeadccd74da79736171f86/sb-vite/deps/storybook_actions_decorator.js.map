{"version": 3, "sources": ["../../../../../storybook/dist/actions/decorator.js"], "sourcesContent": ["var D = Object.defineProperty;\nvar r = (e, n) => D(e, \"name\", { value: n, configurable: !0 });\n\n// src/actions/decorator.ts\nimport { makeDecorator as P, useEffect as x } from \"storybook/preview-api\";\n\n// src/actions/constants.ts\nvar h = \"actions\", y = \"storybook/actions\", $ = `${y}/panel`, g = `${y}/action-event`, B = `${y}/action-clear`;\n\n// src/actions/runtime/action.ts\nimport { ImplicitActionsDuringRendering as R } from \"storybook/internal/preview-errors\";\nimport { global as E } from \"@storybook/global\";\nimport { addons as S } from \"storybook/preview-api\";\n\n// src/actions/runtime/configureActions.ts\nvar a = {\n  depth: 10,\n  clearOnStoryChange: !0,\n  limit: 50\n};\n\n// src/actions/runtime/action.ts\nvar A = /* @__PURE__ */ r((e, n) => {\n  let t = Object.getPrototypeOf(e);\n  return !t || n(t) ? t : A(t, n);\n}, \"findProto\"), j = /* @__PURE__ */ r((e) => !!(typeof e == \"object\" && e && A(e, (n) => /^Synthetic(?:Base)?Event$/.test(n.constructor.name)) &&\ntypeof e.persist == \"function\"), \"isReactSyntheticEvent\"), I = /* @__PURE__ */ r((e) => {\n  if (j(e)) {\n    let n = Object.create(\n      e.constructor.prototype,\n      Object.getOwnPropertyDescriptors(e)\n    );\n    n.persist();\n    let t = Object.getOwnPropertyDescriptor(n, \"view\"), o = t?.value;\n    return typeof o == \"object\" && o?.constructor.name === \"Window\" && Object.defineProperty(n, \"view\", {\n      ...t,\n      value: Object.create(o.constructor.prototype)\n    }), n;\n  }\n  return e;\n}, \"serializeArg\");\nfunction O(e, n = {}) {\n  let t = {\n    ...a,\n    ...n\n  }, o = /* @__PURE__ */ r(function(...i) {\n    if (n.implicit) {\n      let d = (\"__STORYBOOK_PREVIEW__\" in E ? E.__STORYBOOK_PREVIEW__ : void 0)?.storyRenders.find(\n        (c) => c.phase === \"playing\" || c.phase === \"rendering\"\n      );\n      if (d) {\n        let c = !globalThis?.FEATURES?.disallowImplicitActionsInRenderV8, u = new R({\n          phase: d.phase,\n          name: e,\n          deprecated: c\n        });\n        if (c)\n          console.warn(u);\n        else\n          throw u;\n      }\n    }\n    let m = S.getChannel(), p = Date.now().toString(36) + Math.random().toString(36).substring(2), l = 5, f = i.map(I), w = i.length > 1 ? f :\n    f[0], _ = {\n      id: p,\n      count: 0,\n      data: { name: e, args: w },\n      options: {\n        ...t,\n        maxDepth: l + (t.depth || 3)\n      }\n    };\n    m.emit(g, _);\n  }, \"actionHandler\");\n  return o.isAction = !0, o.implicit = n.implicit, o;\n}\nr(O, \"action\");\n\n// src/actions/runtime/actions.ts\nvar b = /* @__PURE__ */ r((...e) => {\n  let n = a, t = e;\n  t.length === 1 && Array.isArray(t[0]) && ([t] = t), t.length !== 1 && typeof t[t.length - 1] != \"string\" && (n = {\n    ...a,\n    ...t.pop()\n  });\n  let o = t[0];\n  (t.length !== 1 || typeof o == \"string\") && (o = {}, t.forEach((i) => {\n    o[i] = i;\n  }));\n  let s = {};\n  return Object.keys(o).forEach((i) => {\n    s[i] = O(o[i], n);\n  }), s;\n}, \"actions\");\n\n// src/actions/decorator.ts\nvar T = /^(\\S+)\\s*(.*)$/, k = Element != null && !Element.prototype.matches, F = k ? \"msMatchesSelector\" : \"matches\", v = /* @__PURE__ */ r(\n(e, n) => {\n  if (e[F](n))\n    return !0;\n  let t = e.parentElement;\n  return t ? v(t, n) : !1;\n}, \"hasMatchInAncestry\"), M = /* @__PURE__ */ r((e, ...n) => {\n  let t = e(...n);\n  return Object.entries(t).map(([o, s]) => {\n    let [i, m, p] = o.match(T) || [];\n    return {\n      eventName: m,\n      handler: /* @__PURE__ */ r((l) => {\n        (!p || v(l.target, p)) && s(l);\n      }, \"handler\")\n    };\n  });\n}, \"createHandlers\"), C = /* @__PURE__ */ r((e, ...n) => {\n  let t = typeof globalThis.document < \"u\" && globalThis.document.getElementById(\"storybook-root\");\n  x(() => {\n    if (t) {\n      let o = M(e, ...n);\n      return o.forEach(({ eventName: s, handler: i }) => t.addEventListener(s, i)), () => o.forEach(({ eventName: s, handler: i }) => t.removeEventListener(\n      s, i));\n    }\n  }, [t, e, n]);\n}, \"applyEventHandlers\"), rt = P({\n  name: \"withActions\",\n  parameterName: h,\n  skipIfNoParametersOrOptions: !0,\n  wrapper: /* @__PURE__ */ r((e, n, { parameters: t }) => (t?.handles && C(b, ...t.handles), e(n)), \"wrapper\")\n});\nexport {\n  rt as withActions\n};\n"], "mappings": ";;;;;;;;;;;;;;AAIA,yBAAmD;AAMnD,4BAAoD;AACpD,oBAA4B;AAC5B,IAAAA,sBAA4B;AAZ5B,IAAI,IAAI,OAAO;AACf,IAAI,IAAI,CAAC,GAAG,MAAM,EAAE,GAAG,QAAQ,EAAE,OAAO,GAAG,cAAc,KAAG,CAAC;AAM7D,IAAI,IAAI;AAAR,IAAmB,IAAI;AAAvB,IAA4C,IAAI,GAAG,CAAC;AAApD,IAA8D,IAAI,GAAG,CAAC;AAAtE,IAAuF,IAAI,GAAG,CAAC;AAQ/F,IAAI,IAAI;AAAA,EACN,OAAO;AAAA,EACP,oBAAoB;AAAA,EACpB,OAAO;AACT;AAGA,IAAI,IAAoB,EAAE,CAAC,GAAG,MAAM;AAClC,MAAI,IAAI,OAAO,eAAe,CAAC;AAC/B,SAAO,CAAC,KAAK,EAAE,CAAC,IAAI,IAAI,EAAE,GAAG,CAAC;AAChC,GAAG,WAAW;AAHd,IAGiB,IAAoB,EAAE,CAAC,MAAM,CAAC,EAAE,OAAO,KAAK,YAAY,KAAK,EAAE,GAAG,CAAC,MAAM,4BAA4B,KAAK,EAAE,YAAY,IAAI,CAAC,KAC9I,OAAO,EAAE,WAAW,aAAa,uBAAuB;AAJxD,IAI2D,IAAoB,EAAE,CAAC,MAAM;AACtF,MAAI,EAAE,CAAC,GAAG;AACR,QAAI,IAAI,OAAO;AAAA,MACb,EAAE,YAAY;AAAA,MACd,OAAO,0BAA0B,CAAC;AAAA,IACpC;AACA,MAAE,QAAQ;AACV,QAAI,IAAI,OAAO,yBAAyB,GAAG,MAAM,GAAG,IAAI,GAAG;AAC3D,WAAO,OAAO,KAAK,YAAY,GAAG,YAAY,SAAS,YAAY,OAAO,eAAe,GAAG,QAAQ;AAAA,MAClG,GAAG;AAAA,MACH,OAAO,OAAO,OAAO,EAAE,YAAY,SAAS;AAAA,IAC9C,CAAC,GAAG;AAAA,EACN;AACA,SAAO;AACT,GAAG,cAAc;AACjB,SAAS,EAAE,GAAG,IAAI,CAAC,GAAG;AACpB,MAAI,IAAI;AAAA,IACN,GAAG;AAAA,IACH,GAAG;AAAA,EACL,GAAG,IAAoB,EAAE,YAAY,GAAG;AACtC,QAAI,EAAE,UAAU;AACd,UAAI,KAAK,2BAA2B,cAAAC,SAAI,cAAAA,OAAE,wBAAwB,SAAS,aAAa;AAAA,QACtF,CAAC,MAAM,EAAE,UAAU,aAAa,EAAE,UAAU;AAAA,MAC9C;AACA,UAAI,GAAG;AACL,YAAI,IAAI,CAAC,YAAY,UAAU,mCAAmC,IAAI,IAAI,sBAAAC,+BAAE;AAAA,UAC1E,OAAO,EAAE;AAAA,UACT,MAAM;AAAA,UACN,YAAY;AAAA,QACd,CAAC;AACD,YAAI;AACF,kBAAQ,KAAK,CAAC;AAAA;AAEd,gBAAM;AAAA,MACV;AAAA,IACF;AACA,QAAI,IAAI,oBAAAC,OAAE,WAAW,GAAG,IAAI,KAAK,IAAI,EAAE,SAAS,EAAE,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE,IAAI,CAAC,GAAG,IAAI,EAAE,SAAS,IAAI,IACvI,EAAE,CAAC,GAAG,IAAI;AAAA,MACR,IAAI;AAAA,MACJ,OAAO;AAAA,MACP,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE;AAAA,MACzB,SAAS;AAAA,QACP,GAAG;AAAA,QACH,UAAU,KAAK,EAAE,SAAS;AAAA,MAC5B;AAAA,IACF;AACA,MAAE,KAAK,GAAG,CAAC;AAAA,EACb,GAAG,eAAe;AAClB,SAAO,EAAE,WAAW,MAAI,EAAE,WAAW,EAAE,UAAU;AACnD;AACA,EAAE,GAAG,QAAQ;AAGb,IAAI,IAAoB,EAAE,IAAI,MAAM;AAClC,MAAI,IAAI,GAAG,IAAI;AACf,IAAE,WAAW,KAAK,MAAM,QAAQ,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,IAAI,EAAE,WAAW,KAAK,OAAO,EAAE,EAAE,SAAS,CAAC,KAAK,aAAa,IAAI;AAAA,IAC/G,GAAG;AAAA,IACH,GAAG,EAAE,IAAI;AAAA,EACX;AACA,MAAI,IAAI,EAAE,CAAC;AACX,GAAC,EAAE,WAAW,KAAK,OAAO,KAAK,cAAc,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,MAAM;AACpE,MAAE,CAAC,IAAI;AAAA,EACT,CAAC;AACD,MAAI,IAAI,CAAC;AACT,SAAO,OAAO,KAAK,CAAC,EAAE,QAAQ,CAAC,MAAM;AACnC,MAAE,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,CAAC;AAAA,EAClB,CAAC,GAAG;AACN,GAAG,SAAS;AAGZ,IAAI,IAAI;AAAR,IAA0B,IAAI,WAAW,QAAQ,CAAC,QAAQ,UAAU;AAApE,IAA6E,IAAI,IAAI,sBAAsB;AAA3G,IAAsH,IAAoB;AAAA,EAC1I,CAAC,GAAG,MAAM;AACR,QAAI,EAAE,CAAC,EAAE,CAAC;AACR,aAAO;AACT,QAAI,IAAI,EAAE;AACV,WAAO,IAAI,EAAE,GAAG,CAAC,IAAI;AAAA,EACvB;AAAA,EAAG;AAAoB;AANvB,IAM0B,IAAoB,EAAE,CAAC,MAAM,MAAM;AAC3D,MAAI,IAAI,EAAE,GAAG,CAAC;AACd,SAAO,OAAO,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM;AACvC,QAAI,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC;AAC/B,WAAO;AAAA,MACL,WAAW;AAAA,MACX,SAAyB,EAAE,CAAC,MAAM;AAChC,SAAC,CAAC,KAAK,EAAE,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC;AAAA,MAC/B,GAAG,SAAS;AAAA,IACd;AAAA,EACF,CAAC;AACH,GAAG,gBAAgB;AAjBnB,IAiBsB,IAAoB,EAAE,CAAC,MAAM,MAAM;AACvD,MAAI,IAAI,OAAO,WAAW,WAAW,OAAO,WAAW,SAAS,eAAe,gBAAgB;AAC/F,yBAAAC,WAAE,MAAM;AACN,QAAI,GAAG;AACL,UAAI,IAAI,EAAE,GAAG,GAAG,CAAC;AACjB,aAAO,EAAE,QAAQ,CAAC,EAAE,WAAW,GAAG,SAAS,EAAE,MAAM,EAAE,iBAAiB,GAAG,CAAC,CAAC,GAAG,MAAM,EAAE,QAAQ,CAAC,EAAE,WAAW,GAAG,SAAS,EAAE,MAAM,EAAE;AAAA,QAClI;AAAA,QAAG;AAAA,MAAC,CAAC;AAAA,IACP;AAAA,EACF,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AACd,GAAG,oBAAoB;AA1BvB,IA0B0B,SAAK,mBAAAC,eAAE;AAAA,EAC/B,MAAM;AAAA,EACN,eAAe;AAAA,EACf,6BAA6B;AAAA,EAC7B,SAAyB,EAAE,CAAC,GAAG,GAAG,EAAE,YAAY,EAAE,OAAO,GAAG,WAAW,EAAE,GAAG,GAAG,EAAE,OAAO,GAAG,EAAE,CAAC,IAAI,SAAS;AAC7G,CAAC;", "names": ["import_preview_api", "E", "R", "S", "x", "P"]}