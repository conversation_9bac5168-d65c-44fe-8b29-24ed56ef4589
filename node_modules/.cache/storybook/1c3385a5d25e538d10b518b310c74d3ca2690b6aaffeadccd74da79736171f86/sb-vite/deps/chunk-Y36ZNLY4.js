import {
  require_upperFirst
} from "./chunk-DUUAESMI.js";
import {
  require_createCompounder
} from "./chunk-ZFW4ABCM.js";
import {
  __commonJS
} from "./chunk-7D4SUZUM.js";

// node_modules/lodash/startCase.js
var require_startCase = __commonJS({
  "node_modules/lodash/startCase.js"(exports, module) {
    var createCompounder = require_createCompounder();
    var upperFirst = require_upperFirst();
    var startCase = createCompounder(function(result, word, index) {
      return result + (index ? " " : "") + upperFirst(word);
    });
    module.exports = startCase;
  }
});

export {
  require_startCase
};
//# sourceMappingURL=chunk-Y36ZNLY4.js.map
