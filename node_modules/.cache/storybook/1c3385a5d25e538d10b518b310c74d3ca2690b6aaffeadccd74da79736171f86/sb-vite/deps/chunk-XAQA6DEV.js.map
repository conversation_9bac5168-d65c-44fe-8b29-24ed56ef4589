{"version": 3, "sources": ["../../../../../lodash/kebabCase.js"], "sourcesContent": ["var createCompounder = require('./_createCompounder');\n\n/**\n * Converts `string` to\n * [kebab case](https://en.wikipedia.org/wiki/Letter_case#Special_case_styles).\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to convert.\n * @returns {string} Returns the kebab cased string.\n * @example\n *\n * _.kebabCase('Foo Bar');\n * // => 'foo-bar'\n *\n * _.kebabCase('fooBar');\n * // => 'foo-bar'\n *\n * _.kebabCase('__FOO_BAR__');\n * // => 'foo-bar'\n */\nvar kebabCase = createCompounder(function(result, word, index) {\n  return result + (index ? '-' : '') + word.toLowerCase();\n});\n\nmodule.exports = kebabCase;\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,QAAI,mBAAmB;AAuBvB,QAAI,YAAY,iBAAiB,SAAS,QAAQ,MAAM,OAAO;AAC7D,aAAO,UAAU,QAAQ,MAAM,MAAM,KAAK,YAAY;AAAA,IACxD,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;", "names": []}