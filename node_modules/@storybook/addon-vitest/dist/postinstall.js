'use strict';

var fs = require('fs');
var fs4 = require('fs/promises');
var babel = require('storybook/internal/babel');
var common = require('storybook/internal/common');
var csfTools = require('storybook/internal/csf-tools');
var nodeLogger = require('storybook/internal/node-logger');
var buffer = require('buffer');
var path = require('path');
var childProcess = require('child_process');
var process2 = require('process');
var url = require('url');
var os = require('os');
var promises = require('timers/promises');
var util = require('util');
var prompts = require('prompts');
var tsDedent = require('ts-dedent');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

function _interopNamespace(e) {
  if (e && e.__esModule) return e;
  var n = Object.create(null);
  if (e) {
    Object.keys(e).forEach(function (k) {
      if (k !== 'default') {
        var d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: function () { return e[k]; }
        });
      }
    });
  }
  n.default = e;
  return Object.freeze(n);
}

var fs4__namespace = /*#__PURE__*/_interopNamespace(fs4);
var path__default = /*#__PURE__*/_interopDefault(path);
var childProcess__default = /*#__PURE__*/_interopDefault(childProcess);
var process2__default = /*#__PURE__*/_interopDefault(process2);
var os__default = /*#__PURE__*/_interopDefault(os);
var prompts__default = /*#__PURE__*/_interopDefault(prompts);

var __create=Object.create;var __defProp=Object.defineProperty;var __getOwnPropDesc=Object.getOwnPropertyDescriptor;var __getOwnPropNames=Object.getOwnPropertyNames;var __getProtoOf=Object.getPrototypeOf,__hasOwnProp=Object.prototype.hasOwnProperty;var __require=(x=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(x,{get:(a,b)=>(typeof require<"u"?require:a)[b]}):x)(function(x){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+x+'" is not supported')});var __commonJS=(cb,mod)=>function(){return mod||(0, cb[__getOwnPropNames(cb)[0]])((mod={exports:{}}).exports,mod),mod.exports};var __copyProps=(to,from,except,desc)=>{if(from&&typeof from=="object"||typeof from=="function")for(let key of __getOwnPropNames(from))!__hasOwnProp.call(to,key)&&key!==except&&__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to};var __toESM=(mod,isNodeMode,target)=>(target=mod!=null?__create(__getProtoOf(mod)):{},__copyProps(isNodeMode||!mod||!mod.__esModule?__defProp(target,"default",{value:mod,enumerable:!0}):target,mod));var require_windows=__commonJS({"../../node_modules/isexe/windows.js"(exports,module){module.exports=isexe;isexe.sync=sync;var fs5=__require("fs");function checkPathExt(path6,options){var pathext=options.pathExt!==void 0?options.pathExt:process.env.PATHEXT;if(!pathext||(pathext=pathext.split(";"),pathext.indexOf("")!==-1))return !0;for(var i=0;i<pathext.length;i++){var p=pathext[i].toLowerCase();if(p&&path6.substr(-p.length).toLowerCase()===p)return !0}return !1}function checkStat(stat,path6,options){return !stat.isSymbolicLink()&&!stat.isFile()?!1:checkPathExt(path6,options)}function isexe(path6,options,cb){fs5.stat(path6,function(er,stat){cb(er,er?!1:checkStat(stat,path6,options));});}function sync(path6,options){return checkStat(fs5.statSync(path6),path6,options)}}});var require_mode=__commonJS({"../../node_modules/isexe/mode.js"(exports,module){module.exports=isexe;isexe.sync=sync;var fs5=__require("fs");function isexe(path6,options,cb){fs5.stat(path6,function(er,stat){cb(er,er?!1:checkStat(stat,options));});}function sync(path6,options){return checkStat(fs5.statSync(path6),options)}function checkStat(stat,options){return stat.isFile()&&checkMode(stat,options)}function checkMode(stat,options){var mod=stat.mode,uid=stat.uid,gid=stat.gid,myUid=options.uid!==void 0?options.uid:process.getuid&&process.getuid(),myGid=options.gid!==void 0?options.gid:process.getgid&&process.getgid(),u=parseInt("100",8),g=parseInt("010",8),o=parseInt("001",8),ug=u|g,ret=mod&o||mod&g&&gid===myGid||mod&u&&uid===myUid||mod&ug&&myUid===0;return ret}}});var require_isexe=__commonJS({"../../node_modules/isexe/index.js"(exports,module){__require("fs");var core;process.platform==="win32"||global.TESTING_WINDOWS?core=require_windows():core=require_mode();module.exports=isexe;isexe.sync=sync;function isexe(path6,options,cb){if(typeof options=="function"&&(cb=options,options={}),!cb){if(typeof Promise!="function")throw new TypeError("callback not provided");return new Promise(function(resolve2,reject){isexe(path6,options||{},function(er,is){er?reject(er):resolve2(is);});})}core(path6,options||{},function(er,is){er&&(er.code==="EACCES"||options&&options.ignoreErrors)&&(er=null,is=!1),cb(er,is);});}function sync(path6,options){try{return core.sync(path6,options||{})}catch(er){if(options&&options.ignoreErrors||er.code==="EACCES")return !1;throw er}}}});var require_which=__commonJS({"../../node_modules/which/which.js"(exports,module){var isWindows=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",path6=__require("path"),COLON=isWindows?";":":",isexe=require_isexe(),getNotFoundError=cmd=>Object.assign(new Error(`not found: ${cmd}`),{code:"ENOENT"}),getPathInfo=(cmd,opt)=>{let colon=opt.colon||COLON,pathEnv=cmd.match(/\//)||isWindows&&cmd.match(/\\/)?[""]:[...isWindows?[process.cwd()]:[],...(opt.path||process.env.PATH||"").split(colon)],pathExtExe=isWindows?opt.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"",pathExt=isWindows?pathExtExe.split(colon):[""];return isWindows&&cmd.indexOf(".")!==-1&&pathExt[0]!==""&&pathExt.unshift(""),{pathEnv,pathExt,pathExtExe}},which=(cmd,opt,cb)=>{typeof opt=="function"&&(cb=opt,opt={}),opt||(opt={});let{pathEnv,pathExt,pathExtExe}=getPathInfo(cmd,opt),found=[],step2=i=>new Promise((resolve2,reject)=>{if(i===pathEnv.length)return opt.all&&found.length?resolve2(found):reject(getNotFoundError(cmd));let ppRaw=pathEnv[i],pathPart=/^".*"$/.test(ppRaw)?ppRaw.slice(1,-1):ppRaw,pCmd=path6.join(pathPart,cmd),p=!pathPart&&/^\.[\\\/]/.test(cmd)?cmd.slice(0,2)+pCmd:pCmd;resolve2(subStep(p,i,0));}),subStep=(p,i,ii)=>new Promise((resolve2,reject)=>{if(ii===pathExt.length)return resolve2(step2(i+1));let ext=pathExt[ii];isexe(p+ext,{pathExt:pathExtExe},(er,is)=>{if(!er&&is)if(opt.all)found.push(p+ext);else return resolve2(p+ext);return resolve2(subStep(p,i,ii+1))});});return cb?step2(0).then(res=>cb(null,res),cb):step2(0)},whichSync=(cmd,opt)=>{opt=opt||{};let{pathEnv,pathExt,pathExtExe}=getPathInfo(cmd,opt),found=[];for(let i=0;i<pathEnv.length;i++){let ppRaw=pathEnv[i],pathPart=/^".*"$/.test(ppRaw)?ppRaw.slice(1,-1):ppRaw,pCmd=path6.join(pathPart,cmd),p=!pathPart&&/^\.[\\\/]/.test(cmd)?cmd.slice(0,2)+pCmd:pCmd;for(let j=0;j<pathExt.length;j++){let cur=p+pathExt[j];try{if(isexe.sync(cur,{pathExt:pathExtExe}))if(opt.all)found.push(cur);else return cur}catch{}}}if(opt.all&&found.length)return found;if(opt.nothrow)return null;throw getNotFoundError(cmd)};module.exports=which;which.sync=whichSync;}});var require_path_key=__commonJS({"../../node_modules/path-key/index.js"(exports,module){var pathKey2=(options={})=>{let environment=options.env||process.env;return (options.platform||process.platform)!=="win32"?"PATH":Object.keys(environment).reverse().find(key=>key.toUpperCase()==="PATH")||"Path"};module.exports=pathKey2;module.exports.default=pathKey2;}});var require_resolveCommand=__commonJS({"../../node_modules/cross-spawn/lib/util/resolveCommand.js"(exports,module){var path6=__require("path"),which=require_which(),getPathKey=require_path_key();function resolveCommandAttempt(parsed,withoutPathExt){let env=parsed.options.env||process.env,cwd2=process.cwd(),hasCustomCwd=parsed.options.cwd!=null,shouldSwitchCwd=hasCustomCwd&&process.chdir!==void 0&&!process.chdir.disabled;if(shouldSwitchCwd)try{process.chdir(parsed.options.cwd);}catch{}let resolved;try{resolved=which.sync(parsed.command,{path:env[getPathKey({env})],pathExt:withoutPathExt?path6.delimiter:void 0});}catch{}finally{shouldSwitchCwd&&process.chdir(cwd2);}return resolved&&(resolved=path6.resolve(hasCustomCwd?parsed.options.cwd:"",resolved)),resolved}function resolveCommand(parsed){return resolveCommandAttempt(parsed)||resolveCommandAttempt(parsed,!0)}module.exports=resolveCommand;}});var require_escape=__commonJS({"../../node_modules/cross-spawn/lib/util/escape.js"(exports,module){var metaCharsRegExp=/([()\][%!^"`<>&|;, *?])/g;function escapeCommand(arg){return arg=arg.replace(metaCharsRegExp,"^$1"),arg}function escapeArgument(arg,doubleEscapeMetaChars){return arg=`${arg}`,arg=arg.replace(/(?=(\\+?)?)\1"/g,'$1$1\\"'),arg=arg.replace(/(?=(\\+?)?)\1$/,"$1$1"),arg=`"${arg}"`,arg=arg.replace(metaCharsRegExp,"^$1"),doubleEscapeMetaChars&&(arg=arg.replace(metaCharsRegExp,"^$1")),arg}module.exports.command=escapeCommand;module.exports.argument=escapeArgument;}});var require_shebang_regex=__commonJS({"../../node_modules/shebang-regex/index.js"(exports,module){module.exports=/^#!(.*)/;}});var require_shebang_command=__commonJS({"../../node_modules/shebang-command/index.js"(exports,module){var shebangRegex=require_shebang_regex();module.exports=(string="")=>{let match=string.match(shebangRegex);if(!match)return null;let[path6,argument]=match[0].replace(/#! ?/,"").split(" "),binary=path6.split("/").pop();return binary==="env"?argument:argument?`${binary} ${argument}`:binary};}});var require_readShebang=__commonJS({"../../node_modules/cross-spawn/lib/util/readShebang.js"(exports,module){var fs5=__require("fs"),shebangCommand=require_shebang_command();function readShebang(command){let buffer=Buffer.alloc(150),fd;try{fd=fs5.openSync(command,"r"),fs5.readSync(fd,buffer,0,150,0),fs5.closeSync(fd);}catch{}return shebangCommand(buffer.toString())}module.exports=readShebang;}});var require_parse=__commonJS({"../../node_modules/cross-spawn/lib/parse.js"(exports,module){var path6=__require("path"),resolveCommand=require_resolveCommand(),escape=require_escape(),readShebang=require_readShebang(),isWin=process.platform==="win32",isExecutableRegExp=/\.(?:com|exe)$/i,isCmdShimRegExp=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function detectShebang(parsed){parsed.file=resolveCommand(parsed);let shebang=parsed.file&&readShebang(parsed.file);return shebang?(parsed.args.unshift(parsed.file),parsed.command=shebang,resolveCommand(parsed)):parsed.file}function parseNonShell(parsed){if(!isWin)return parsed;let commandFile=detectShebang(parsed),needsShell=!isExecutableRegExp.test(commandFile);if(parsed.options.forceShell||needsShell){let needsDoubleEscapeMetaChars=isCmdShimRegExp.test(commandFile);parsed.command=path6.normalize(parsed.command),parsed.command=escape.command(parsed.command),parsed.args=parsed.args.map(arg=>escape.argument(arg,needsDoubleEscapeMetaChars));let shellCommand=[parsed.command].concat(parsed.args).join(" ");parsed.args=["/d","/s","/c",`"${shellCommand}"`],parsed.command=process.env.comspec||"cmd.exe",parsed.options.windowsVerbatimArguments=!0;}return parsed}function parse2(command,args,options){args&&!Array.isArray(args)&&(options=args,args=null),args=args?args.slice(0):[],options=Object.assign({},options);let parsed={command,args,options,file:void 0,original:{command,args}};return options.shell?parsed:parseNonShell(parsed)}module.exports=parse2;}});var require_enoent=__commonJS({"../../node_modules/cross-spawn/lib/enoent.js"(exports,module){var isWin=process.platform==="win32";function notFoundError(original,syscall){return Object.assign(new Error(`${syscall} ${original.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${syscall} ${original.command}`,path:original.command,spawnargs:original.args})}function hookChildProcess(cp,parsed){if(!isWin)return;let originalEmit=cp.emit;cp.emit=function(name,arg1){if(name==="exit"){let err=verifyENOENT(arg1,parsed);if(err)return originalEmit.call(cp,"error",err)}return originalEmit.apply(cp,arguments)};}function verifyENOENT(status,parsed){return isWin&&status===1&&!parsed.file?notFoundError(parsed.original,"spawn"):null}function verifyENOENTSync(status,parsed){return isWin&&status===1&&!parsed.file?notFoundError(parsed.original,"spawnSync"):null}module.exports={hookChildProcess,verifyENOENT,verifyENOENTSync,notFoundError};}});var require_cross_spawn=__commonJS({"../../node_modules/cross-spawn/index.js"(exports,module){var cp=__require("child_process"),parse2=require_parse(),enoent=require_enoent();function spawn(command,args,options){let parsed=parse2(command,args,options),spawned=cp.spawn(parsed.command,parsed.args,parsed.options);return enoent.hookChildProcess(spawned,parsed),spawned}function spawnSync(command,args,options){let parsed=parse2(command,args,options),result=cp.spawnSync(parsed.command,parsed.args,parsed.options);return result.error=result.error||enoent.verifyENOENTSync(result.status,parsed),result}module.exports=spawn;module.exports.spawn=spawn;module.exports.sync=spawnSync;module.exports._parse=parse2;module.exports._enoent=enoent;}});var require_merge_stream=__commonJS({"../../node_modules/merge-stream/index.js"(exports,module){var{PassThrough}=__require("stream");module.exports=function(){var sources=[],output=new PassThrough({objectMode:!0});return output.setMaxListeners(0),output.add=add,output.isEmpty=isEmpty,output.on("unpipe",remove),Array.prototype.slice.call(arguments).forEach(add),output;function add(source){return Array.isArray(source)?(source.forEach(add),this):(sources.push(source),source.once("end",remove.bind(null,source)),source.once("error",output.emit.bind(output,"error")),source.pipe(output,{end:!1}),this)}function isEmpty(){return sources.length==0}function remove(source){sources=sources.filter(function(it){return it!==source}),!sources.length&&output.readable&&output.end();}};}});var require_constants=__commonJS({"../../node_modules/semver/internal/constants.js"(exports,module){var SEMVER_SPEC_VERSION="2.0.0",MAX_SAFE_INTEGER=Number.MAX_SAFE_INTEGER||9007199254740991,MAX_SAFE_COMPONENT_LENGTH=16,MAX_SAFE_BUILD_LENGTH=250,RELEASE_TYPES=["major","premajor","minor","preminor","patch","prepatch","prerelease"];module.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH,MAX_SAFE_BUILD_LENGTH,MAX_SAFE_INTEGER,RELEASE_TYPES,SEMVER_SPEC_VERSION,FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2};}});var require_debug=__commonJS({"../../node_modules/semver/internal/debug.js"(exports,module){var debug=typeof process=="object"&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...args)=>console.error("SEMVER",...args):()=>{};module.exports=debug;}});var require_re=__commonJS({"../../node_modules/semver/internal/re.js"(exports,module){var{MAX_SAFE_COMPONENT_LENGTH,MAX_SAFE_BUILD_LENGTH,MAX_LENGTH}=require_constants(),debug=require_debug();exports=module.exports={};var re=exports.re=[],safeRe=exports.safeRe=[],src=exports.src=[],safeSrc=exports.safeSrc=[],t=exports.t={},R=0,LETTERDASHNUMBER="[a-zA-Z0-9-]",safeRegexReplacements=[["\\s",1],["\\d",MAX_LENGTH],[LETTERDASHNUMBER,MAX_SAFE_BUILD_LENGTH]],makeSafeRegex=value=>{for(let[token,max]of safeRegexReplacements)value=value.split(`${token}*`).join(`${token}{0,${max}}`).split(`${token}+`).join(`${token}{1,${max}}`);return value},createToken=(name,value,isGlobal)=>{let safe=makeSafeRegex(value),index=R++;debug(name,index,value),t[name]=index,src[index]=value,safeSrc[index]=safe,re[index]=new RegExp(value,isGlobal?"g":void 0),safeRe[index]=new RegExp(safe,isGlobal?"g":void 0);};createToken("NUMERICIDENTIFIER","0|[1-9]\\d*");createToken("NUMERICIDENTIFIERLOOSE","\\d+");createToken("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${LETTERDASHNUMBER}*`);createToken("MAINVERSION",`(${src[t.NUMERICIDENTIFIER]})\\.(${src[t.NUMERICIDENTIFIER]})\\.(${src[t.NUMERICIDENTIFIER]})`);createToken("MAINVERSIONLOOSE",`(${src[t.NUMERICIDENTIFIERLOOSE]})\\.(${src[t.NUMERICIDENTIFIERLOOSE]})\\.(${src[t.NUMERICIDENTIFIERLOOSE]})`);createToken("PRERELEASEIDENTIFIER",`(?:${src[t.NONNUMERICIDENTIFIER]}|${src[t.NUMERICIDENTIFIER]})`);createToken("PRERELEASEIDENTIFIERLOOSE",`(?:${src[t.NONNUMERICIDENTIFIER]}|${src[t.NUMERICIDENTIFIERLOOSE]})`);createToken("PRERELEASE",`(?:-(${src[t.PRERELEASEIDENTIFIER]}(?:\\.${src[t.PRERELEASEIDENTIFIER]})*))`);createToken("PRERELEASELOOSE",`(?:-?(${src[t.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${src[t.PRERELEASEIDENTIFIERLOOSE]})*))`);createToken("BUILDIDENTIFIER",`${LETTERDASHNUMBER}+`);createToken("BUILD",`(?:\\+(${src[t.BUILDIDENTIFIER]}(?:\\.${src[t.BUILDIDENTIFIER]})*))`);createToken("FULLPLAIN",`v?${src[t.MAINVERSION]}${src[t.PRERELEASE]}?${src[t.BUILD]}?`);createToken("FULL",`^${src[t.FULLPLAIN]}$`);createToken("LOOSEPLAIN",`[v=\\s]*${src[t.MAINVERSIONLOOSE]}${src[t.PRERELEASELOOSE]}?${src[t.BUILD]}?`);createToken("LOOSE",`^${src[t.LOOSEPLAIN]}$`);createToken("GTLT","((?:<|>)?=?)");createToken("XRANGEIDENTIFIERLOOSE",`${src[t.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`);createToken("XRANGEIDENTIFIER",`${src[t.NUMERICIDENTIFIER]}|x|X|\\*`);createToken("XRANGEPLAIN",`[v=\\s]*(${src[t.XRANGEIDENTIFIER]})(?:\\.(${src[t.XRANGEIDENTIFIER]})(?:\\.(${src[t.XRANGEIDENTIFIER]})(?:${src[t.PRERELEASE]})?${src[t.BUILD]}?)?)?`);createToken("XRANGEPLAINLOOSE",`[v=\\s]*(${src[t.XRANGEIDENTIFIERLOOSE]})(?:\\.(${src[t.XRANGEIDENTIFIERLOOSE]})(?:\\.(${src[t.XRANGEIDENTIFIERLOOSE]})(?:${src[t.PRERELEASELOOSE]})?${src[t.BUILD]}?)?)?`);createToken("XRANGE",`^${src[t.GTLT]}\\s*${src[t.XRANGEPLAIN]}$`);createToken("XRANGELOOSE",`^${src[t.GTLT]}\\s*${src[t.XRANGEPLAINLOOSE]}$`);createToken("COERCEPLAIN",`(^|[^\\d])(\\d{1,${MAX_SAFE_COMPONENT_LENGTH}})(?:\\.(\\d{1,${MAX_SAFE_COMPONENT_LENGTH}}))?(?:\\.(\\d{1,${MAX_SAFE_COMPONENT_LENGTH}}))?`);createToken("COERCE",`${src[t.COERCEPLAIN]}(?:$|[^\\d])`);createToken("COERCEFULL",src[t.COERCEPLAIN]+`(?:${src[t.PRERELEASE]})?(?:${src[t.BUILD]})?(?:$|[^\\d])`);createToken("COERCERTL",src[t.COERCE],!0);createToken("COERCERTLFULL",src[t.COERCEFULL],!0);createToken("LONETILDE","(?:~>?)");createToken("TILDETRIM",`(\\s*)${src[t.LONETILDE]}\\s+`,!0);exports.tildeTrimReplace="$1~";createToken("TILDE",`^${src[t.LONETILDE]}${src[t.XRANGEPLAIN]}$`);createToken("TILDELOOSE",`^${src[t.LONETILDE]}${src[t.XRANGEPLAINLOOSE]}$`);createToken("LONECARET","(?:\\^)");createToken("CARETTRIM",`(\\s*)${src[t.LONECARET]}\\s+`,!0);exports.caretTrimReplace="$1^";createToken("CARET",`^${src[t.LONECARET]}${src[t.XRANGEPLAIN]}$`);createToken("CARETLOOSE",`^${src[t.LONECARET]}${src[t.XRANGEPLAINLOOSE]}$`);createToken("COMPARATORLOOSE",`^${src[t.GTLT]}\\s*(${src[t.LOOSEPLAIN]})$|^$`);createToken("COMPARATOR",`^${src[t.GTLT]}\\s*(${src[t.FULLPLAIN]})$|^$`);createToken("COMPARATORTRIM",`(\\s*)${src[t.GTLT]}\\s*(${src[t.LOOSEPLAIN]}|${src[t.XRANGEPLAIN]})`,!0);exports.comparatorTrimReplace="$1$2$3";createToken("HYPHENRANGE",`^\\s*(${src[t.XRANGEPLAIN]})\\s+-\\s+(${src[t.XRANGEPLAIN]})\\s*$`);createToken("HYPHENRANGELOOSE",`^\\s*(${src[t.XRANGEPLAINLOOSE]})\\s+-\\s+(${src[t.XRANGEPLAINLOOSE]})\\s*$`);createToken("STAR","(<|>)?=?\\s*\\*");createToken("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$");createToken("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$");}});var require_parse_options=__commonJS({"../../node_modules/semver/internal/parse-options.js"(exports,module){var looseOption=Object.freeze({loose:!0}),emptyOpts=Object.freeze({}),parseOptions=options=>options?typeof options!="object"?looseOption:options:emptyOpts;module.exports=parseOptions;}});var require_identifiers=__commonJS({"../../node_modules/semver/internal/identifiers.js"(exports,module){var numeric=/^[0-9]+$/,compareIdentifiers=(a,b)=>{let anum=numeric.test(a),bnum=numeric.test(b);return anum&&bnum&&(a=+a,b=+b),a===b?0:anum&&!bnum?-1:bnum&&!anum?1:a<b?-1:1},rcompareIdentifiers=(a,b)=>compareIdentifiers(b,a);module.exports={compareIdentifiers,rcompareIdentifiers};}});var require_semver=__commonJS({"../../node_modules/semver/classes/semver.js"(exports,module){var debug=require_debug(),{MAX_LENGTH,MAX_SAFE_INTEGER}=require_constants(),{safeRe:re,t}=require_re(),parseOptions=require_parse_options(),{compareIdentifiers}=require_identifiers(),SemVer=class _SemVer{constructor(version,options){if(options=parseOptions(options),version instanceof _SemVer){if(version.loose===!!options.loose&&version.includePrerelease===!!options.includePrerelease)return version;version=version.version;}else if(typeof version!="string")throw new TypeError(`Invalid version. Must be a string. Got type "${typeof version}".`);if(version.length>MAX_LENGTH)throw new TypeError(`version is longer than ${MAX_LENGTH} characters`);debug("SemVer",version,options),this.options=options,this.loose=!!options.loose,this.includePrerelease=!!options.includePrerelease;let m=version.trim().match(options.loose?re[t.LOOSE]:re[t.FULL]);if(!m)throw new TypeError(`Invalid Version: ${version}`);if(this.raw=version,this.major=+m[1],this.minor=+m[2],this.patch=+m[3],this.major>MAX_SAFE_INTEGER||this.major<0)throw new TypeError("Invalid major version");if(this.minor>MAX_SAFE_INTEGER||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>MAX_SAFE_INTEGER||this.patch<0)throw new TypeError("Invalid patch version");m[4]?this.prerelease=m[4].split(".").map(id=>{if(/^[0-9]+$/.test(id)){let num=+id;if(num>=0&&num<MAX_SAFE_INTEGER)return num}return id}):this.prerelease=[],this.build=m[5]?m[5].split("."):[],this.format();}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(other){if(debug("SemVer.compare",this.version,this.options,other),!(other instanceof _SemVer)){if(typeof other=="string"&&other===this.version)return 0;other=new _SemVer(other,this.options);}return other.version===this.version?0:this.compareMain(other)||this.comparePre(other)}compareMain(other){return other instanceof _SemVer||(other=new _SemVer(other,this.options)),compareIdentifiers(this.major,other.major)||compareIdentifiers(this.minor,other.minor)||compareIdentifiers(this.patch,other.patch)}comparePre(other){if(other instanceof _SemVer||(other=new _SemVer(other,this.options)),this.prerelease.length&&!other.prerelease.length)return -1;if(!this.prerelease.length&&other.prerelease.length)return 1;if(!this.prerelease.length&&!other.prerelease.length)return 0;let i=0;do{let a=this.prerelease[i],b=other.prerelease[i];if(debug("prerelease compare",i,a,b),a===void 0&&b===void 0)return 0;if(b===void 0)return 1;if(a===void 0)return -1;if(a===b)continue;return compareIdentifiers(a,b)}while(++i)}compareBuild(other){other instanceof _SemVer||(other=new _SemVer(other,this.options));let i=0;do{let a=this.build[i],b=other.build[i];if(debug("build compare",i,a,b),a===void 0&&b===void 0)return 0;if(b===void 0)return 1;if(a===void 0)return -1;if(a===b)continue;return compareIdentifiers(a,b)}while(++i)}inc(release,identifier,identifierBase){if(release.startsWith("pre")){if(!identifier&&identifierBase===!1)throw new Error("invalid increment argument: identifier is empty");if(identifier){let match=`-${identifier}`.match(this.options.loose?re[t.PRERELEASELOOSE]:re[t.PRERELEASE]);if(!match||match[1]!==identifier)throw new Error(`invalid identifier: ${identifier}`)}}switch(release){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",identifier,identifierBase);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",identifier,identifierBase);break;case"prepatch":this.prerelease.length=0,this.inc("patch",identifier,identifierBase),this.inc("pre",identifier,identifierBase);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",identifier,identifierBase),this.inc("pre",identifier,identifierBase);break;case"release":if(this.prerelease.length===0)throw new Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":{let base=Number(identifierBase)?1:0;if(this.prerelease.length===0)this.prerelease=[base];else {let i=this.prerelease.length;for(;--i>=0;)typeof this.prerelease[i]=="number"&&(this.prerelease[i]++,i=-2);if(i===-1){if(identifier===this.prerelease.join(".")&&identifierBase===!1)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(base);}}if(identifier){let prerelease=[identifier,base];identifierBase===!1&&(prerelease=[identifier]),compareIdentifiers(this.prerelease[0],identifier)===0?isNaN(this.prerelease[1])&&(this.prerelease=prerelease):this.prerelease=prerelease;}break}default:throw new Error(`invalid increment argument: ${release}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}};module.exports=SemVer;}});var require_parse2=__commonJS({"../../node_modules/semver/functions/parse.js"(exports,module){var SemVer=require_semver(),parse2=(version,options,throwErrors=!1)=>{if(version instanceof SemVer)return version;try{return new SemVer(version,options)}catch(er){if(!throwErrors)return null;throw er}};module.exports=parse2;}});var require_valid=__commonJS({"../../node_modules/semver/functions/valid.js"(exports,module){var parse2=require_parse2(),valid=(version,options)=>{let v=parse2(version,options);return v?v.version:null};module.exports=valid;}});var require_clean=__commonJS({"../../node_modules/semver/functions/clean.js"(exports,module){var parse2=require_parse2(),clean=(version,options)=>{let s=parse2(version.trim().replace(/^[=v]+/,""),options);return s?s.version:null};module.exports=clean;}});var require_inc=__commonJS({"../../node_modules/semver/functions/inc.js"(exports,module){var SemVer=require_semver(),inc=(version,release,options,identifier,identifierBase)=>{typeof options=="string"&&(identifierBase=identifier,identifier=options,options=void 0);try{return new SemVer(version instanceof SemVer?version.version:version,options).inc(release,identifier,identifierBase).version}catch{return null}};module.exports=inc;}});var require_diff=__commonJS({"../../node_modules/semver/functions/diff.js"(exports,module){var parse2=require_parse2(),diff=(version1,version2)=>{let v1=parse2(version1,null,!0),v2=parse2(version2,null,!0),comparison=v1.compare(v2);if(comparison===0)return null;let v1Higher=comparison>0,highVersion=v1Higher?v1:v2,lowVersion=v1Higher?v2:v1,highHasPre=!!highVersion.prerelease.length;if(!!lowVersion.prerelease.length&&!highHasPre){if(!lowVersion.patch&&!lowVersion.minor)return "major";if(lowVersion.compareMain(highVersion)===0)return lowVersion.minor&&!lowVersion.patch?"minor":"patch"}let prefix=highHasPre?"pre":"";return v1.major!==v2.major?prefix+"major":v1.minor!==v2.minor?prefix+"minor":v1.patch!==v2.patch?prefix+"patch":"prerelease"};module.exports=diff;}});var require_major=__commonJS({"../../node_modules/semver/functions/major.js"(exports,module){var SemVer=require_semver(),major=(a,loose)=>new SemVer(a,loose).major;module.exports=major;}});var require_minor=__commonJS({"../../node_modules/semver/functions/minor.js"(exports,module){var SemVer=require_semver(),minor=(a,loose)=>new SemVer(a,loose).minor;module.exports=minor;}});var require_patch=__commonJS({"../../node_modules/semver/functions/patch.js"(exports,module){var SemVer=require_semver(),patch=(a,loose)=>new SemVer(a,loose).patch;module.exports=patch;}});var require_prerelease=__commonJS({"../../node_modules/semver/functions/prerelease.js"(exports,module){var parse2=require_parse2(),prerelease=(version,options)=>{let parsed=parse2(version,options);return parsed&&parsed.prerelease.length?parsed.prerelease:null};module.exports=prerelease;}});var require_compare=__commonJS({"../../node_modules/semver/functions/compare.js"(exports,module){var SemVer=require_semver(),compare=(a,b,loose)=>new SemVer(a,loose).compare(new SemVer(b,loose));module.exports=compare;}});var require_rcompare=__commonJS({"../../node_modules/semver/functions/rcompare.js"(exports,module){var compare=require_compare(),rcompare=(a,b,loose)=>compare(b,a,loose);module.exports=rcompare;}});var require_compare_loose=__commonJS({"../../node_modules/semver/functions/compare-loose.js"(exports,module){var compare=require_compare(),compareLoose=(a,b)=>compare(a,b,!0);module.exports=compareLoose;}});var require_compare_build=__commonJS({"../../node_modules/semver/functions/compare-build.js"(exports,module){var SemVer=require_semver(),compareBuild=(a,b,loose)=>{let versionA=new SemVer(a,loose),versionB=new SemVer(b,loose);return versionA.compare(versionB)||versionA.compareBuild(versionB)};module.exports=compareBuild;}});var require_sort=__commonJS({"../../node_modules/semver/functions/sort.js"(exports,module){var compareBuild=require_compare_build(),sort=(list,loose)=>list.sort((a,b)=>compareBuild(a,b,loose));module.exports=sort;}});var require_rsort=__commonJS({"../../node_modules/semver/functions/rsort.js"(exports,module){var compareBuild=require_compare_build(),rsort=(list,loose)=>list.sort((a,b)=>compareBuild(b,a,loose));module.exports=rsort;}});var require_gt=__commonJS({"../../node_modules/semver/functions/gt.js"(exports,module){var compare=require_compare(),gt=(a,b,loose)=>compare(a,b,loose)>0;module.exports=gt;}});var require_lt=__commonJS({"../../node_modules/semver/functions/lt.js"(exports,module){var compare=require_compare(),lt=(a,b,loose)=>compare(a,b,loose)<0;module.exports=lt;}});var require_eq=__commonJS({"../../node_modules/semver/functions/eq.js"(exports,module){var compare=require_compare(),eq=(a,b,loose)=>compare(a,b,loose)===0;module.exports=eq;}});var require_neq=__commonJS({"../../node_modules/semver/functions/neq.js"(exports,module){var compare=require_compare(),neq=(a,b,loose)=>compare(a,b,loose)!==0;module.exports=neq;}});var require_gte=__commonJS({"../../node_modules/semver/functions/gte.js"(exports,module){var compare=require_compare(),gte=(a,b,loose)=>compare(a,b,loose)>=0;module.exports=gte;}});var require_lte=__commonJS({"../../node_modules/semver/functions/lte.js"(exports,module){var compare=require_compare(),lte=(a,b,loose)=>compare(a,b,loose)<=0;module.exports=lte;}});var require_cmp=__commonJS({"../../node_modules/semver/functions/cmp.js"(exports,module){var eq=require_eq(),neq=require_neq(),gt=require_gt(),gte=require_gte(),lt=require_lt(),lte=require_lte(),cmp=(a,op,b,loose)=>{switch(op){case"===":return typeof a=="object"&&(a=a.version),typeof b=="object"&&(b=b.version),a===b;case"!==":return typeof a=="object"&&(a=a.version),typeof b=="object"&&(b=b.version),a!==b;case"":case"=":case"==":return eq(a,b,loose);case"!=":return neq(a,b,loose);case">":return gt(a,b,loose);case">=":return gte(a,b,loose);case"<":return lt(a,b,loose);case"<=":return lte(a,b,loose);default:throw new TypeError(`Invalid operator: ${op}`)}};module.exports=cmp;}});var require_coerce=__commonJS({"../../node_modules/semver/functions/coerce.js"(exports,module){var SemVer=require_semver(),parse2=require_parse2(),{safeRe:re,t}=require_re(),coerce2=(version,options)=>{if(version instanceof SemVer)return version;if(typeof version=="number"&&(version=String(version)),typeof version!="string")return null;options=options||{};let match=null;if(!options.rtl)match=version.match(options.includePrerelease?re[t.COERCEFULL]:re[t.COERCE]);else {let coerceRtlRegex=options.includePrerelease?re[t.COERCERTLFULL]:re[t.COERCERTL],next;for(;(next=coerceRtlRegex.exec(version))&&(!match||match.index+match[0].length!==version.length);)(!match||next.index+next[0].length!==match.index+match[0].length)&&(match=next),coerceRtlRegex.lastIndex=next.index+next[1].length+next[2].length;coerceRtlRegex.lastIndex=-1;}if(match===null)return null;let major=match[2],minor=match[3]||"0",patch=match[4]||"0",prerelease=options.includePrerelease&&match[5]?`-${match[5]}`:"",build=options.includePrerelease&&match[6]?`+${match[6]}`:"";return parse2(`${major}.${minor}.${patch}${prerelease}${build}`,options)};module.exports=coerce2;}});var require_lrucache=__commonJS({"../../node_modules/semver/internal/lrucache.js"(exports,module){var LRUCache=class{constructor(){this.max=1e3,this.map=new Map;}get(key){let value=this.map.get(key);if(value!==void 0)return this.map.delete(key),this.map.set(key,value),value}delete(key){return this.map.delete(key)}set(key,value){if(!this.delete(key)&&value!==void 0){if(this.map.size>=this.max){let firstKey=this.map.keys().next().value;this.delete(firstKey);}this.map.set(key,value);}return this}};module.exports=LRUCache;}});var require_range=__commonJS({"../../node_modules/semver/classes/range.js"(exports,module){var SPACE_CHARACTERS=/\s+/g,Range=class _Range{constructor(range,options){if(options=parseOptions(options),range instanceof _Range)return range.loose===!!options.loose&&range.includePrerelease===!!options.includePrerelease?range:new _Range(range.raw,options);if(range instanceof Comparator)return this.raw=range.value,this.set=[[range]],this.formatted=void 0,this;if(this.options=options,this.loose=!!options.loose,this.includePrerelease=!!options.includePrerelease,this.raw=range.trim().replace(SPACE_CHARACTERS," "),this.set=this.raw.split("||").map(r=>this.parseRange(r.trim())).filter(c=>c.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){let first=this.set[0];if(this.set=this.set.filter(c=>!isNullSet(c[0])),this.set.length===0)this.set=[first];else if(this.set.length>1){for(let c of this.set)if(c.length===1&&isAny(c[0])){this.set=[c];break}}}this.formatted=void 0;}get range(){if(this.formatted===void 0){this.formatted="";for(let i=0;i<this.set.length;i++){i>0&&(this.formatted+="||");let comps=this.set[i];for(let k=0;k<comps.length;k++)k>0&&(this.formatted+=" "),this.formatted+=comps[k].toString().trim();}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(range){let memoKey=((this.options.includePrerelease&&FLAG_INCLUDE_PRERELEASE)|(this.options.loose&&FLAG_LOOSE))+":"+range,cached=cache.get(memoKey);if(cached)return cached;let loose=this.options.loose,hr=loose?re[t.HYPHENRANGELOOSE]:re[t.HYPHENRANGE];range=range.replace(hr,hyphenReplace(this.options.includePrerelease)),debug("hyphen replace",range),range=range.replace(re[t.COMPARATORTRIM],comparatorTrimReplace),debug("comparator trim",range),range=range.replace(re[t.TILDETRIM],tildeTrimReplace),debug("tilde trim",range),range=range.replace(re[t.CARETTRIM],caretTrimReplace),debug("caret trim",range);let rangeList=range.split(" ").map(comp=>parseComparator(comp,this.options)).join(" ").split(/\s+/).map(comp=>replaceGTE0(comp,this.options));loose&&(rangeList=rangeList.filter(comp=>(debug("loose invalid filter",comp,this.options),!!comp.match(re[t.COMPARATORLOOSE])))),debug("range list",rangeList);let rangeMap=new Map,comparators=rangeList.map(comp=>new Comparator(comp,this.options));for(let comp of comparators){if(isNullSet(comp))return [comp];rangeMap.set(comp.value,comp);}rangeMap.size>1&&rangeMap.has("")&&rangeMap.delete("");let result=[...rangeMap.values()];return cache.set(memoKey,result),result}intersects(range,options){if(!(range instanceof _Range))throw new TypeError("a Range is required");return this.set.some(thisComparators=>isSatisfiable(thisComparators,options)&&range.set.some(rangeComparators=>isSatisfiable(rangeComparators,options)&&thisComparators.every(thisComparator=>rangeComparators.every(rangeComparator=>thisComparator.intersects(rangeComparator,options)))))}test(version){if(!version)return !1;if(typeof version=="string")try{version=new SemVer(version,this.options);}catch{return !1}for(let i=0;i<this.set.length;i++)if(testSet(this.set[i],version,this.options))return !0;return !1}};module.exports=Range;var LRU=require_lrucache(),cache=new LRU,parseOptions=require_parse_options(),Comparator=require_comparator(),debug=require_debug(),SemVer=require_semver(),{safeRe:re,t,comparatorTrimReplace,tildeTrimReplace,caretTrimReplace}=require_re(),{FLAG_INCLUDE_PRERELEASE,FLAG_LOOSE}=require_constants(),isNullSet=c=>c.value==="<0.0.0-0",isAny=c=>c.value==="",isSatisfiable=(comparators,options)=>{let result=!0,remainingComparators=comparators.slice(),testComparator=remainingComparators.pop();for(;result&&remainingComparators.length;)result=remainingComparators.every(otherComparator=>testComparator.intersects(otherComparator,options)),testComparator=remainingComparators.pop();return result},parseComparator=(comp,options)=>(debug("comp",comp,options),comp=replaceCarets(comp,options),debug("caret",comp),comp=replaceTildes(comp,options),debug("tildes",comp),comp=replaceXRanges(comp,options),debug("xrange",comp),comp=replaceStars(comp,options),debug("stars",comp),comp),isX=id=>!id||id.toLowerCase()==="x"||id==="*",replaceTildes=(comp,options)=>comp.trim().split(/\s+/).map(c=>replaceTilde(c,options)).join(" "),replaceTilde=(comp,options)=>{let r=options.loose?re[t.TILDELOOSE]:re[t.TILDE];return comp.replace(r,(_,M,m,p,pr)=>{debug("tilde",comp,_,M,m,p,pr);let ret;return isX(M)?ret="":isX(m)?ret=`>=${M}.0.0 <${+M+1}.0.0-0`:isX(p)?ret=`>=${M}.${m}.0 <${M}.${+m+1}.0-0`:pr?(debug("replaceTilde pr",pr),ret=`>=${M}.${m}.${p}-${pr} <${M}.${+m+1}.0-0`):ret=`>=${M}.${m}.${p} <${M}.${+m+1}.0-0`,debug("tilde return",ret),ret})},replaceCarets=(comp,options)=>comp.trim().split(/\s+/).map(c=>replaceCaret(c,options)).join(" "),replaceCaret=(comp,options)=>{debug("caret",comp,options);let r=options.loose?re[t.CARETLOOSE]:re[t.CARET],z=options.includePrerelease?"-0":"";return comp.replace(r,(_,M,m,p,pr)=>{debug("caret",comp,_,M,m,p,pr);let ret;return isX(M)?ret="":isX(m)?ret=`>=${M}.0.0${z} <${+M+1}.0.0-0`:isX(p)?M==="0"?ret=`>=${M}.${m}.0${z} <${M}.${+m+1}.0-0`:ret=`>=${M}.${m}.0${z} <${+M+1}.0.0-0`:pr?(debug("replaceCaret pr",pr),M==="0"?m==="0"?ret=`>=${M}.${m}.${p}-${pr} <${M}.${m}.${+p+1}-0`:ret=`>=${M}.${m}.${p}-${pr} <${M}.${+m+1}.0-0`:ret=`>=${M}.${m}.${p}-${pr} <${+M+1}.0.0-0`):(debug("no pr"),M==="0"?m==="0"?ret=`>=${M}.${m}.${p}${z} <${M}.${m}.${+p+1}-0`:ret=`>=${M}.${m}.${p}${z} <${M}.${+m+1}.0-0`:ret=`>=${M}.${m}.${p} <${+M+1}.0.0-0`),debug("caret return",ret),ret})},replaceXRanges=(comp,options)=>(debug("replaceXRanges",comp,options),comp.split(/\s+/).map(c=>replaceXRange(c,options)).join(" ")),replaceXRange=(comp,options)=>{comp=comp.trim();let r=options.loose?re[t.XRANGELOOSE]:re[t.XRANGE];return comp.replace(r,(ret,gtlt,M,m,p,pr)=>{debug("xRange",comp,ret,gtlt,M,m,p,pr);let xM=isX(M),xm=xM||isX(m),xp=xm||isX(p),anyX=xp;return gtlt==="="&&anyX&&(gtlt=""),pr=options.includePrerelease?"-0":"",xM?gtlt===">"||gtlt==="<"?ret="<0.0.0-0":ret="*":gtlt&&anyX?(xm&&(m=0),p=0,gtlt===">"?(gtlt=">=",xm?(M=+M+1,m=0,p=0):(m=+m+1,p=0)):gtlt==="<="&&(gtlt="<",xm?M=+M+1:m=+m+1),gtlt==="<"&&(pr="-0"),ret=`${gtlt+M}.${m}.${p}${pr}`):xm?ret=`>=${M}.0.0${pr} <${+M+1}.0.0-0`:xp&&(ret=`>=${M}.${m}.0${pr} <${M}.${+m+1}.0-0`),debug("xRange return",ret),ret})},replaceStars=(comp,options)=>(debug("replaceStars",comp,options),comp.trim().replace(re[t.STAR],"")),replaceGTE0=(comp,options)=>(debug("replaceGTE0",comp,options),comp.trim().replace(re[options.includePrerelease?t.GTE0PRE:t.GTE0],"")),hyphenReplace=incPr=>($0,from,fM,fm,fp,fpr,fb,to,tM,tm,tp,tpr)=>(isX(fM)?from="":isX(fm)?from=`>=${fM}.0.0${incPr?"-0":""}`:isX(fp)?from=`>=${fM}.${fm}.0${incPr?"-0":""}`:fpr?from=`>=${from}`:from=`>=${from}${incPr?"-0":""}`,isX(tM)?to="":isX(tm)?to=`<${+tM+1}.0.0-0`:isX(tp)?to=`<${tM}.${+tm+1}.0-0`:tpr?to=`<=${tM}.${tm}.${tp}-${tpr}`:incPr?to=`<${tM}.${tm}.${+tp+1}-0`:to=`<=${to}`,`${from} ${to}`.trim()),testSet=(set,version,options)=>{for(let i=0;i<set.length;i++)if(!set[i].test(version))return !1;if(version.prerelease.length&&!options.includePrerelease){for(let i=0;i<set.length;i++)if(debug(set[i].semver),set[i].semver!==Comparator.ANY&&set[i].semver.prerelease.length>0){let allowed=set[i].semver;if(allowed.major===version.major&&allowed.minor===version.minor&&allowed.patch===version.patch)return !0}return !1}return !0};}});var require_comparator=__commonJS({"../../node_modules/semver/classes/comparator.js"(exports,module){var ANY=Symbol("SemVer ANY"),Comparator=class _Comparator{static get ANY(){return ANY}constructor(comp,options){if(options=parseOptions(options),comp instanceof _Comparator){if(comp.loose===!!options.loose)return comp;comp=comp.value;}comp=comp.trim().split(/\s+/).join(" "),debug("comparator",comp,options),this.options=options,this.loose=!!options.loose,this.parse(comp),this.semver===ANY?this.value="":this.value=this.operator+this.semver.version,debug("comp",this);}parse(comp){let r=this.options.loose?re[t.COMPARATORLOOSE]:re[t.COMPARATOR],m=comp.match(r);if(!m)throw new TypeError(`Invalid comparator: ${comp}`);this.operator=m[1]!==void 0?m[1]:"",this.operator==="="&&(this.operator=""),m[2]?this.semver=new SemVer(m[2],this.options.loose):this.semver=ANY;}toString(){return this.value}test(version){if(debug("Comparator.test",version,this.options.loose),this.semver===ANY||version===ANY)return !0;if(typeof version=="string")try{version=new SemVer(version,this.options);}catch{return !1}return cmp(version,this.operator,this.semver,this.options)}intersects(comp,options){if(!(comp instanceof _Comparator))throw new TypeError("a Comparator is required");return this.operator===""?this.value===""?!0:new Range(comp.value,options).test(this.value):comp.operator===""?comp.value===""?!0:new Range(this.value,options).test(comp.semver):(options=parseOptions(options),options.includePrerelease&&(this.value==="<0.0.0-0"||comp.value==="<0.0.0-0")||!options.includePrerelease&&(this.value.startsWith("<0.0.0")||comp.value.startsWith("<0.0.0"))?!1:!!(this.operator.startsWith(">")&&comp.operator.startsWith(">")||this.operator.startsWith("<")&&comp.operator.startsWith("<")||this.semver.version===comp.semver.version&&this.operator.includes("=")&&comp.operator.includes("=")||cmp(this.semver,"<",comp.semver,options)&&this.operator.startsWith(">")&&comp.operator.startsWith("<")||cmp(this.semver,">",comp.semver,options)&&this.operator.startsWith("<")&&comp.operator.startsWith(">")))}};module.exports=Comparator;var parseOptions=require_parse_options(),{safeRe:re,t}=require_re(),cmp=require_cmp(),debug=require_debug(),SemVer=require_semver(),Range=require_range();}});var require_satisfies=__commonJS({"../../node_modules/semver/functions/satisfies.js"(exports,module){var Range=require_range(),satisfies2=(version,range,options)=>{try{range=new Range(range,options);}catch{return !1}return range.test(version)};module.exports=satisfies2;}});var require_to_comparators=__commonJS({"../../node_modules/semver/ranges/to-comparators.js"(exports,module){var Range=require_range(),toComparators=(range,options)=>new Range(range,options).set.map(comp=>comp.map(c=>c.value).join(" ").trim().split(" "));module.exports=toComparators;}});var require_max_satisfying=__commonJS({"../../node_modules/semver/ranges/max-satisfying.js"(exports,module){var SemVer=require_semver(),Range=require_range(),maxSatisfying=(versions,range,options)=>{let max=null,maxSV=null,rangeObj=null;try{rangeObj=new Range(range,options);}catch{return null}return versions.forEach(v=>{rangeObj.test(v)&&(!max||maxSV.compare(v)===-1)&&(max=v,maxSV=new SemVer(max,options));}),max};module.exports=maxSatisfying;}});var require_min_satisfying=__commonJS({"../../node_modules/semver/ranges/min-satisfying.js"(exports,module){var SemVer=require_semver(),Range=require_range(),minSatisfying=(versions,range,options)=>{let min=null,minSV=null,rangeObj=null;try{rangeObj=new Range(range,options);}catch{return null}return versions.forEach(v=>{rangeObj.test(v)&&(!min||minSV.compare(v)===1)&&(min=v,minSV=new SemVer(min,options));}),min};module.exports=minSatisfying;}});var require_min_version=__commonJS({"../../node_modules/semver/ranges/min-version.js"(exports,module){var SemVer=require_semver(),Range=require_range(),gt=require_gt(),minVersion=(range,loose)=>{range=new Range(range,loose);let minver=new SemVer("0.0.0");if(range.test(minver)||(minver=new SemVer("0.0.0-0"),range.test(minver)))return minver;minver=null;for(let i=0;i<range.set.length;++i){let comparators=range.set[i],setMin=null;comparators.forEach(comparator=>{let compver=new SemVer(comparator.semver.version);switch(comparator.operator){case">":compver.prerelease.length===0?compver.patch++:compver.prerelease.push(0),compver.raw=compver.format();case"":case">=":(!setMin||gt(compver,setMin))&&(setMin=compver);break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${comparator.operator}`)}}),setMin&&(!minver||gt(minver,setMin))&&(minver=setMin);}return minver&&range.test(minver)?minver:null};module.exports=minVersion;}});var require_valid2=__commonJS({"../../node_modules/semver/ranges/valid.js"(exports,module){var Range=require_range(),validRange=(range,options)=>{try{return new Range(range,options).range||"*"}catch{return null}};module.exports=validRange;}});var require_outside=__commonJS({"../../node_modules/semver/ranges/outside.js"(exports,module){var SemVer=require_semver(),Comparator=require_comparator(),{ANY}=Comparator,Range=require_range(),satisfies2=require_satisfies(),gt=require_gt(),lt=require_lt(),lte=require_lte(),gte=require_gte(),outside=(version,range,hilo,options)=>{version=new SemVer(version,options),range=new Range(range,options);let gtfn,ltefn,ltfn,comp,ecomp;switch(hilo){case">":gtfn=gt,ltefn=lte,ltfn=lt,comp=">",ecomp=">=";break;case"<":gtfn=lt,ltefn=gte,ltfn=gt,comp="<",ecomp="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(satisfies2(version,range,options))return !1;for(let i=0;i<range.set.length;++i){let comparators=range.set[i],high=null,low=null;if(comparators.forEach(comparator=>{comparator.semver===ANY&&(comparator=new Comparator(">=0.0.0")),high=high||comparator,low=low||comparator,gtfn(comparator.semver,high.semver,options)?high=comparator:ltfn(comparator.semver,low.semver,options)&&(low=comparator);}),high.operator===comp||high.operator===ecomp||(!low.operator||low.operator===comp)&&ltefn(version,low.semver))return !1;if(low.operator===ecomp&&ltfn(version,low.semver))return !1}return !0};module.exports=outside;}});var require_gtr=__commonJS({"../../node_modules/semver/ranges/gtr.js"(exports,module){var outside=require_outside(),gtr=(version,range,options)=>outside(version,range,">",options);module.exports=gtr;}});var require_ltr=__commonJS({"../../node_modules/semver/ranges/ltr.js"(exports,module){var outside=require_outside(),ltr=(version,range,options)=>outside(version,range,"<",options);module.exports=ltr;}});var require_intersects=__commonJS({"../../node_modules/semver/ranges/intersects.js"(exports,module){var Range=require_range(),intersects=(r1,r2,options)=>(r1=new Range(r1,options),r2=new Range(r2,options),r1.intersects(r2,options));module.exports=intersects;}});var require_simplify=__commonJS({"../../node_modules/semver/ranges/simplify.js"(exports,module){var satisfies2=require_satisfies(),compare=require_compare();module.exports=(versions,range,options)=>{let set=[],first=null,prev=null,v=versions.sort((a,b)=>compare(a,b,options));for(let version of v)satisfies2(version,range,options)?(prev=version,first||(first=version)):(prev&&set.push([first,prev]),prev=null,first=null);first&&set.push([first,null]);let ranges=[];for(let[min,max]of set)min===max?ranges.push(min):!max&&min===v[0]?ranges.push("*"):max?min===v[0]?ranges.push(`<=${max}`):ranges.push(`${min} - ${max}`):ranges.push(`>=${min}`);let simplified=ranges.join(" || "),original=typeof range.raw=="string"?range.raw:String(range);return simplified.length<original.length?simplified:range};}});var require_subset=__commonJS({"../../node_modules/semver/ranges/subset.js"(exports,module){var Range=require_range(),Comparator=require_comparator(),{ANY}=Comparator,satisfies2=require_satisfies(),compare=require_compare(),subset=(sub,dom,options={})=>{if(sub===dom)return !0;sub=new Range(sub,options),dom=new Range(dom,options);let sawNonNull=!1;OUTER:for(let simpleSub of sub.set){for(let simpleDom of dom.set){let isSub=simpleSubset(simpleSub,simpleDom,options);if(sawNonNull=sawNonNull||isSub!==null,isSub)continue OUTER}if(sawNonNull)return !1}return !0},minimumVersionWithPreRelease=[new Comparator(">=0.0.0-0")],minimumVersion=[new Comparator(">=0.0.0")],simpleSubset=(sub,dom,options)=>{if(sub===dom)return !0;if(sub.length===1&&sub[0].semver===ANY){if(dom.length===1&&dom[0].semver===ANY)return !0;options.includePrerelease?sub=minimumVersionWithPreRelease:sub=minimumVersion;}if(dom.length===1&&dom[0].semver===ANY){if(options.includePrerelease)return !0;dom=minimumVersion;}let eqSet=new Set,gt,lt;for(let c of sub)c.operator===">"||c.operator===">="?gt=higherGT(gt,c,options):c.operator==="<"||c.operator==="<="?lt=lowerLT(lt,c,options):eqSet.add(c.semver);if(eqSet.size>1)return null;let gtltComp;if(gt&&lt){if(gtltComp=compare(gt.semver,lt.semver,options),gtltComp>0)return null;if(gtltComp===0&&(gt.operator!==">="||lt.operator!=="<="))return null}for(let eq of eqSet){if(gt&&!satisfies2(eq,String(gt),options)||lt&&!satisfies2(eq,String(lt),options))return null;for(let c of dom)if(!satisfies2(eq,String(c),options))return !1;return !0}let higher,lower,hasDomLT,hasDomGT,needDomLTPre=lt&&!options.includePrerelease&&lt.semver.prerelease.length?lt.semver:!1,needDomGTPre=gt&&!options.includePrerelease&&gt.semver.prerelease.length?gt.semver:!1;needDomLTPre&&needDomLTPre.prerelease.length===1&&lt.operator==="<"&&needDomLTPre.prerelease[0]===0&&(needDomLTPre=!1);for(let c of dom){if(hasDomGT=hasDomGT||c.operator===">"||c.operator===">=",hasDomLT=hasDomLT||c.operator==="<"||c.operator==="<=",gt){if(needDomGTPre&&c.semver.prerelease&&c.semver.prerelease.length&&c.semver.major===needDomGTPre.major&&c.semver.minor===needDomGTPre.minor&&c.semver.patch===needDomGTPre.patch&&(needDomGTPre=!1),c.operator===">"||c.operator===">="){if(higher=higherGT(gt,c,options),higher===c&&higher!==gt)return !1}else if(gt.operator===">="&&!satisfies2(gt.semver,String(c),options))return !1}if(lt){if(needDomLTPre&&c.semver.prerelease&&c.semver.prerelease.length&&c.semver.major===needDomLTPre.major&&c.semver.minor===needDomLTPre.minor&&c.semver.patch===needDomLTPre.patch&&(needDomLTPre=!1),c.operator==="<"||c.operator==="<="){if(lower=lowerLT(lt,c,options),lower===c&&lower!==lt)return !1}else if(lt.operator==="<="&&!satisfies2(lt.semver,String(c),options))return !1}if(!c.operator&&(lt||gt)&&gtltComp!==0)return !1}return !(gt&&hasDomLT&&!lt&&gtltComp!==0||lt&&hasDomGT&&!gt&&gtltComp!==0||needDomGTPre||needDomLTPre)},higherGT=(a,b,options)=>{if(!a)return b;let comp=compare(a.semver,b.semver,options);return comp>0?a:comp<0||b.operator===">"&&a.operator===">="?b:a},lowerLT=(a,b,options)=>{if(!a)return b;let comp=compare(a.semver,b.semver,options);return comp<0?a:comp>0||b.operator==="<"&&a.operator==="<="?b:a};module.exports=subset;}});var require_semver2=__commonJS({"../../node_modules/semver/index.js"(exports,module){var internalRe=require_re(),constants3=require_constants(),SemVer=require_semver(),identifiers=require_identifiers(),parse2=require_parse2(),valid=require_valid(),clean=require_clean(),inc=require_inc(),diff=require_diff(),major=require_major(),minor=require_minor(),patch=require_patch(),prerelease=require_prerelease(),compare=require_compare(),rcompare=require_rcompare(),compareLoose=require_compare_loose(),compareBuild=require_compare_build(),sort=require_sort(),rsort=require_rsort(),gt=require_gt(),lt=require_lt(),eq=require_eq(),neq=require_neq(),gte=require_gte(),lte=require_lte(),cmp=require_cmp(),coerce2=require_coerce(),Comparator=require_comparator(),Range=require_range(),satisfies2=require_satisfies(),toComparators=require_to_comparators(),maxSatisfying=require_max_satisfying(),minSatisfying=require_min_satisfying(),minVersion=require_min_version(),validRange=require_valid2(),outside=require_outside(),gtr=require_gtr(),ltr=require_ltr(),intersects=require_intersects(),simplifyRange=require_simplify(),subset=require_subset();module.exports={parse:parse2,valid,clean,inc,diff,major,minor,patch,prerelease,compare,rcompare,compareLoose,compareBuild,sort,rsort,gt,lt,eq,neq,gte,lte,cmp,coerce:coerce2,Comparator,Range,satisfies:satisfies2,toComparators,maxSatisfying,minSatisfying,minVersion,validRange,outside,gtr,ltr,intersects,simplifyRange,subset,SemVer,re:internalRe.re,src:internalRe.src,tokens:internalRe.t,SEMVER_SPEC_VERSION:constants3.SEMVER_SPEC_VERSION,RELEASE_TYPES:constants3.RELEASE_TYPES,compareIdentifiers:identifiers.compareIdentifiers,rcompareIdentifiers:identifiers.rcompareIdentifiers};}});var import_cross_spawn=__toESM(require_cross_spawn(),1);function stripFinalNewline(input){let LF=typeof input=="string"?`
`:10,CR=typeof input=="string"?"\r":13;return input[input.length-1]===LF&&(input=input.slice(0,-1)),input[input.length-1]===CR&&(input=input.slice(0,-1)),input}function pathKey(options={}){let{env=process.env,platform=process.platform}=options;return platform!=="win32"?"PATH":Object.keys(env).reverse().find(key=>key.toUpperCase()==="PATH")||"Path"}var npmRunPath=({cwd:cwd2=process2__default.default.cwd(),path:pathOption=process2__default.default.env[pathKey()],preferLocal=!0,execPath=process2__default.default.execPath,addExecPath=!0}={})=>{let cwdString=cwd2 instanceof URL?url.fileURLToPath(cwd2):cwd2,cwdPath=path__default.default.resolve(cwdString),result=[];return preferLocal&&applyPreferLocal(result,cwdPath),addExecPath&&applyExecPath(result,execPath,cwdPath),[...result,pathOption].join(path__default.default.delimiter)},applyPreferLocal=(result,cwdPath)=>{let previous;for(;previous!==cwdPath;)result.push(path__default.default.join(cwdPath,"node_modules/.bin")),previous=cwdPath,cwdPath=path__default.default.resolve(cwdPath,"..");},applyExecPath=(result,execPath,cwdPath)=>{let execPathString=execPath instanceof URL?url.fileURLToPath(execPath):execPath;result.push(path__default.default.resolve(cwdPath,execPathString,".."));},npmRunPathEnv=({env=process2__default.default.env,...options}={})=>{env={...env};let pathName=pathKey({env});return options.path=env[pathName],env[pathName]=npmRunPath(options),env};var copyProperty=(to,from,property,ignoreNonConfigurable)=>{if(property==="length"||property==="prototype"||property==="arguments"||property==="caller")return;let toDescriptor=Object.getOwnPropertyDescriptor(to,property),fromDescriptor=Object.getOwnPropertyDescriptor(from,property);!canCopyProperty(toDescriptor,fromDescriptor)&&ignoreNonConfigurable||Object.defineProperty(to,property,fromDescriptor);},canCopyProperty=function(toDescriptor,fromDescriptor){return toDescriptor===void 0||toDescriptor.configurable||toDescriptor.writable===fromDescriptor.writable&&toDescriptor.enumerable===fromDescriptor.enumerable&&toDescriptor.configurable===fromDescriptor.configurable&&(toDescriptor.writable||toDescriptor.value===fromDescriptor.value)},changePrototype=(to,from)=>{let fromPrototype=Object.getPrototypeOf(from);fromPrototype!==Object.getPrototypeOf(to)&&Object.setPrototypeOf(to,fromPrototype);},wrappedToString=(withName,fromBody)=>`/* Wrapped ${withName}*/
${fromBody}`,toStringDescriptor=Object.getOwnPropertyDescriptor(Function.prototype,"toString"),toStringName=Object.getOwnPropertyDescriptor(Function.prototype.toString,"name"),changeToString=(to,from,name)=>{let withName=name===""?"":`with ${name.trim()}() `,newToString=wrappedToString.bind(null,withName,from.toString());Object.defineProperty(newToString,"name",toStringName),Object.defineProperty(to,"toString",{...toStringDescriptor,value:newToString});};function mimicFunction(to,from,{ignoreNonConfigurable=!1}={}){let{name}=to;for(let property of Reflect.ownKeys(from))copyProperty(to,from,property,ignoreNonConfigurable);return changePrototype(to,from),changeToString(to,from,name),to}var calledFunctions=new WeakMap,onetime=(function_,options={})=>{if(typeof function_!="function")throw new TypeError("Expected a function");let returnValue,callCount=0,functionName=function_.displayName||function_.name||"<anonymous>",onetime2=function(...arguments_){if(calledFunctions.set(onetime2,++callCount),callCount===1)returnValue=function_.apply(this,arguments_),function_=null;else if(options.throw===!0)throw new Error(`Function \`${functionName}\` can only be called once`);return returnValue};return mimicFunction(onetime2,function_),calledFunctions.set(onetime2,callCount),onetime2};onetime.callCount=function_=>{if(!calledFunctions.has(function_))throw new Error(`The given function \`${function_.name}\` is not wrapped by the \`onetime\` package`);return calledFunctions.get(function_)};var onetime_default=onetime;var getRealtimeSignals=()=>{let length=SIGRTMAX-SIGRTMIN+1;return Array.from({length},getRealtimeSignal)},getRealtimeSignal=(value,index)=>({name:`SIGRT${index+1}`,number:SIGRTMIN+index,action:"terminate",description:"Application-specific signal (realtime)",standard:"posix"}),SIGRTMIN=34,SIGRTMAX=64;var SIGNALS=[{name:"SIGHUP",number:1,action:"terminate",description:"Terminal closed",standard:"posix"},{name:"SIGINT",number:2,action:"terminate",description:"User interruption with CTRL-C",standard:"ansi"},{name:"SIGQUIT",number:3,action:"core",description:"User interruption with CTRL-\\",standard:"posix"},{name:"SIGILL",number:4,action:"core",description:"Invalid machine instruction",standard:"ansi"},{name:"SIGTRAP",number:5,action:"core",description:"Debugger breakpoint",standard:"posix"},{name:"SIGABRT",number:6,action:"core",description:"Aborted",standard:"ansi"},{name:"SIGIOT",number:6,action:"core",description:"Aborted",standard:"bsd"},{name:"SIGBUS",number:7,action:"core",description:"Bus error due to misaligned, non-existing address or paging error",standard:"bsd"},{name:"SIGEMT",number:7,action:"terminate",description:"Command should be emulated but is not implemented",standard:"other"},{name:"SIGFPE",number:8,action:"core",description:"Floating point arithmetic error",standard:"ansi"},{name:"SIGKILL",number:9,action:"terminate",description:"Forced termination",standard:"posix",forced:!0},{name:"SIGUSR1",number:10,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGSEGV",number:11,action:"core",description:"Segmentation fault",standard:"ansi"},{name:"SIGUSR2",number:12,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGPIPE",number:13,action:"terminate",description:"Broken pipe or socket",standard:"posix"},{name:"SIGALRM",number:14,action:"terminate",description:"Timeout or timer",standard:"posix"},{name:"SIGTERM",number:15,action:"terminate",description:"Termination",standard:"ansi"},{name:"SIGSTKFLT",number:16,action:"terminate",description:"Stack is empty or overflowed",standard:"other"},{name:"SIGCHLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"posix"},{name:"SIGCLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"other"},{name:"SIGCONT",number:18,action:"unpause",description:"Unpaused",standard:"posix",forced:!0},{name:"SIGSTOP",number:19,action:"pause",description:"Paused",standard:"posix",forced:!0},{name:"SIGTSTP",number:20,action:"pause",description:'Paused using CTRL-Z or "suspend"',standard:"posix"},{name:"SIGTTIN",number:21,action:"pause",description:"Background process cannot read terminal input",standard:"posix"},{name:"SIGBREAK",number:21,action:"terminate",description:"User interruption with CTRL-BREAK",standard:"other"},{name:"SIGTTOU",number:22,action:"pause",description:"Background process cannot write to terminal output",standard:"posix"},{name:"SIGURG",number:23,action:"ignore",description:"Socket received out-of-band data",standard:"bsd"},{name:"SIGXCPU",number:24,action:"core",description:"Process timed out",standard:"bsd"},{name:"SIGXFSZ",number:25,action:"core",description:"File too big",standard:"bsd"},{name:"SIGVTALRM",number:26,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGPROF",number:27,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGWINCH",number:28,action:"ignore",description:"Terminal window size changed",standard:"bsd"},{name:"SIGIO",number:29,action:"terminate",description:"I/O is available",standard:"other"},{name:"SIGPOLL",number:29,action:"terminate",description:"Watched event",standard:"other"},{name:"SIGINFO",number:29,action:"ignore",description:"Request for process information",standard:"other"},{name:"SIGPWR",number:30,action:"terminate",description:"Device running out of power",standard:"systemv"},{name:"SIGSYS",number:31,action:"core",description:"Invalid system call",standard:"other"},{name:"SIGUNUSED",number:31,action:"terminate",description:"Invalid system call",standard:"other"}];var getSignals=()=>{let realtimeSignals=getRealtimeSignals();return [...SIGNALS,...realtimeSignals].map(normalizeSignal)},normalizeSignal=({name,number:defaultNumber,description,action,forced=!1,standard})=>{let{signals:{[name]:constantSignal}}=os.constants,supported=constantSignal!==void 0;return {name,number:supported?constantSignal:defaultNumber,description,supported,action,forced,standard}};var getSignalsByName=()=>{let signals2=getSignals();return Object.fromEntries(signals2.map(getSignalByName))},getSignalByName=({name,number,description,supported,action,forced,standard})=>[name,{name,number,description,supported,action,forced,standard}],signalsByName=getSignalsByName(),getSignalsByNumber=()=>{let signals2=getSignals(),length=65,signalsA=Array.from({length},(value,number)=>getSignalByNumber(number,signals2));return Object.assign({},...signalsA)},getSignalByNumber=(number,signals2)=>{let signal=findSignalByNumber(number,signals2);if(signal===void 0)return {};let{name,description,supported,action,forced,standard}=signal;return {[number]:{name,number,description,supported,action,forced,standard}}},findSignalByNumber=(number,signals2)=>{let signal=signals2.find(({name})=>os.constants.signals[name]===number);return signal!==void 0?signal:signals2.find(signalA=>signalA.number===number)};getSignalsByNumber();var getErrorPrefix=({timedOut,timeout,errorCode,signal,signalDescription,exitCode,isCanceled})=>timedOut?`timed out after ${timeout} milliseconds`:isCanceled?"was canceled":errorCode!==void 0?`failed with ${errorCode}`:signal!==void 0?`was killed with ${signal} (${signalDescription})`:exitCode!==void 0?`failed with exit code ${exitCode}`:"failed",makeError=({stdout,stderr,all,error:error2,signal,exitCode,command,escapedCommand,timedOut,isCanceled,killed,parsed:{options:{timeout,cwd:cwd2=process2__default.default.cwd()}}})=>{exitCode=exitCode===null?void 0:exitCode,signal=signal===null?void 0:signal;let signalDescription=signal===void 0?void 0:signalsByName[signal].description,errorCode=error2&&error2.code,execaMessage=`Command ${getErrorPrefix({timedOut,timeout,errorCode,signal,signalDescription,exitCode,isCanceled})}: ${command}`,isError=Object.prototype.toString.call(error2)==="[object Error]",shortMessage=isError?`${execaMessage}
${error2.message}`:execaMessage,message=[shortMessage,stderr,stdout].filter(Boolean).join(`
`);return isError?(error2.originalMessage=error2.message,error2.message=message):error2=new Error(message),error2.shortMessage=shortMessage,error2.command=command,error2.escapedCommand=escapedCommand,error2.exitCode=exitCode,error2.signal=signal,error2.signalDescription=signalDescription,error2.stdout=stdout,error2.stderr=stderr,error2.cwd=cwd2,all!==void 0&&(error2.all=all),"bufferedData"in error2&&delete error2.bufferedData,error2.failed=!0,error2.timedOut=!!timedOut,error2.isCanceled=isCanceled,error2.killed=killed&&!timedOut,error2};var aliases=["stdin","stdout","stderr"],hasAlias=options=>aliases.some(alias=>options[alias]!==void 0),normalizeStdio=options=>{if(!options)return;let{stdio}=options;if(stdio===void 0)return aliases.map(alias=>options[alias]);if(hasAlias(options))throw new Error(`It's not possible to provide \`stdio\` in combination with one of ${aliases.map(alias=>`\`${alias}\``).join(", ")}`);if(typeof stdio=="string")return stdio;if(!Array.isArray(stdio))throw new TypeError(`Expected \`stdio\` to be of type \`string\` or \`Array\`, got \`${typeof stdio}\``);let length=Math.max(stdio.length,aliases.length);return Array.from({length},(value,index)=>stdio[index])};var signals=[];signals.push("SIGHUP","SIGINT","SIGTERM");process.platform!=="win32"&&signals.push("SIGALRM","SIGABRT","SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");process.platform==="linux"&&signals.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT");var processOk=process8=>!!process8&&typeof process8=="object"&&typeof process8.removeListener=="function"&&typeof process8.emit=="function"&&typeof process8.reallyExit=="function"&&typeof process8.listeners=="function"&&typeof process8.kill=="function"&&typeof process8.pid=="number"&&typeof process8.on=="function",kExitEmitter=Symbol.for("signal-exit emitter"),global2=globalThis,ObjectDefineProperty=Object.defineProperty.bind(Object),Emitter=class{emitted={afterExit:!1,exit:!1};listeners={afterExit:[],exit:[]};count=0;id=Math.random();constructor(){if(global2[kExitEmitter])return global2[kExitEmitter];ObjectDefineProperty(global2,kExitEmitter,{value:this,writable:!1,enumerable:!1,configurable:!1});}on(ev,fn){this.listeners[ev].push(fn);}removeListener(ev,fn){let list=this.listeners[ev],i=list.indexOf(fn);i!==-1&&(i===0&&list.length===1?list.length=0:list.splice(i,1));}emit(ev,code,signal){if(this.emitted[ev])return !1;this.emitted[ev]=!0;let ret=!1;for(let fn of this.listeners[ev])ret=fn(code,signal)===!0||ret;return ev==="exit"&&(ret=this.emit("afterExit",code,signal)||ret),ret}},SignalExitBase=class{},signalExitWrap=handler=>({onExit(cb,opts){return handler.onExit(cb,opts)},load(){return handler.load()},unload(){return handler.unload()}}),SignalExitFallback=class extends SignalExitBase{onExit(){return ()=>{}}load(){}unload(){}},SignalExit=class extends SignalExitBase{#hupSig=process4.platform==="win32"?"SIGINT":"SIGHUP";#emitter=new Emitter;#process;#originalProcessEmit;#originalProcessReallyExit;#sigListeners={};#loaded=!1;constructor(process8){super(),this.#process=process8,this.#sigListeners={};for(let sig of signals)this.#sigListeners[sig]=()=>{let listeners=this.#process.listeners(sig),{count}=this.#emitter,p=process8;if(typeof p.__signal_exit_emitter__=="object"&&typeof p.__signal_exit_emitter__.count=="number"&&(count+=p.__signal_exit_emitter__.count),listeners.length===count){this.unload();let ret=this.#emitter.emit("exit",null,sig),s=sig==="SIGHUP"?this.#hupSig:sig;ret||process8.kill(process8.pid,s);}};this.#originalProcessReallyExit=process8.reallyExit,this.#originalProcessEmit=process8.emit;}onExit(cb,opts){if(!processOk(this.#process))return ()=>{};this.#loaded===!1&&this.load();let ev=opts?.alwaysLast?"afterExit":"exit";return this.#emitter.on(ev,cb),()=>{this.#emitter.removeListener(ev,cb),this.#emitter.listeners.exit.length===0&&this.#emitter.listeners.afterExit.length===0&&this.unload();}}load(){if(!this.#loaded){this.#loaded=!0,this.#emitter.count+=1;for(let sig of signals)try{let fn=this.#sigListeners[sig];fn&&this.#process.on(sig,fn);}catch{}this.#process.emit=(ev,...a)=>this.#processEmit(ev,...a),this.#process.reallyExit=code=>this.#processReallyExit(code);}}unload(){this.#loaded&&(this.#loaded=!1,signals.forEach(sig=>{let listener=this.#sigListeners[sig];if(!listener)throw new Error("Listener not defined for signal: "+sig);try{this.#process.removeListener(sig,listener);}catch{}}),this.#process.emit=this.#originalProcessEmit,this.#process.reallyExit=this.#originalProcessReallyExit,this.#emitter.count-=1);}#processReallyExit(code){return processOk(this.#process)?(this.#process.exitCode=code||0,this.#emitter.emit("exit",this.#process.exitCode,null),this.#originalProcessReallyExit.call(this.#process,this.#process.exitCode)):0}#processEmit(ev,...args){let og=this.#originalProcessEmit;if(ev==="exit"&&processOk(this.#process)){typeof args[0]=="number"&&(this.#process.exitCode=args[0]);let ret=og.call(this.#process,ev,...args);return this.#emitter.emit("exit",this.#process.exitCode,null),ret}else return og.call(this.#process,ev,...args)}},process4=globalThis.process,{onExit,load,unload}=signalExitWrap(processOk(process4)?new SignalExit(process4):new SignalExitFallback);var DEFAULT_FORCE_KILL_TIMEOUT=1e3*5,spawnedKill=(kill,signal="SIGTERM",options={})=>{let killResult=kill(signal);return setKillTimeout(kill,signal,options,killResult),killResult},setKillTimeout=(kill,signal,options,killResult)=>{if(!shouldForceKill(signal,options,killResult))return;let timeout=getForceKillAfterTimeout(options),t=setTimeout(()=>{kill("SIGKILL");},timeout);t.unref&&t.unref();},shouldForceKill=(signal,{forceKillAfterTimeout},killResult)=>isSigterm(signal)&&forceKillAfterTimeout!==!1&&killResult,isSigterm=signal=>signal===os__default.default.constants.signals.SIGTERM||typeof signal=="string"&&signal.toUpperCase()==="SIGTERM",getForceKillAfterTimeout=({forceKillAfterTimeout=!0})=>{if(forceKillAfterTimeout===!0)return DEFAULT_FORCE_KILL_TIMEOUT;if(!Number.isFinite(forceKillAfterTimeout)||forceKillAfterTimeout<0)throw new TypeError(`Expected the \`forceKillAfterTimeout\` option to be a non-negative integer, got \`${forceKillAfterTimeout}\` (${typeof forceKillAfterTimeout})`);return forceKillAfterTimeout},spawnedCancel=(spawned,context)=>{spawned.kill()&&(context.isCanceled=!0);},timeoutKill=(spawned,signal,reject)=>{spawned.kill(signal),reject(Object.assign(new Error("Timed out"),{timedOut:!0,signal}));},setupTimeout=(spawned,{timeout,killSignal="SIGTERM"},spawnedPromise)=>{if(timeout===0||timeout===void 0)return spawnedPromise;let timeoutId,timeoutPromise=new Promise((resolve2,reject)=>{timeoutId=setTimeout(()=>{timeoutKill(spawned,killSignal,reject);},timeout);}),safeSpawnedPromise=spawnedPromise.finally(()=>{clearTimeout(timeoutId);});return Promise.race([timeoutPromise,safeSpawnedPromise])},validateTimeout=({timeout})=>{if(timeout!==void 0&&(!Number.isFinite(timeout)||timeout<0))throw new TypeError(`Expected the \`timeout\` option to be a non-negative integer, got \`${timeout}\` (${typeof timeout})`)},setExitHandler=async(spawned,{cleanup,detached},timedPromise)=>{if(!cleanup||detached)return timedPromise;let removeExitHandler=onExit(()=>{spawned.kill();});return timedPromise.finally(()=>{removeExitHandler();})};function isStream(stream){return stream!==null&&typeof stream=="object"&&typeof stream.pipe=="function"}function isWritableStream(stream){return isStream(stream)&&stream.writable!==!1&&typeof stream._write=="function"&&typeof stream._writableState=="object"}var isExecaChildProcess=target=>target instanceof childProcess.ChildProcess&&typeof target.then=="function",pipeToTarget=(spawned,streamName,target)=>{if(typeof target=="string")return spawned[streamName].pipe(fs.createWriteStream(target)),spawned;if(isWritableStream(target))return spawned[streamName].pipe(target),spawned;if(!isExecaChildProcess(target))throw new TypeError("The second argument must be a string, a stream or an Execa child process.");if(!isWritableStream(target.stdin))throw new TypeError("The target child process's stdin must be available.");return spawned[streamName].pipe(target.stdin),target},addPipeMethods=spawned=>{spawned.stdout!==null&&(spawned.pipeStdout=pipeToTarget.bind(void 0,spawned,"stdout")),spawned.stderr!==null&&(spawned.pipeStderr=pipeToTarget.bind(void 0,spawned,"stderr")),spawned.all!==void 0&&(spawned.pipeAll=pipeToTarget.bind(void 0,spawned,"all"));};var getStreamContents=async(stream,{init,convertChunk,getSize,truncateChunk,addChunk,getFinalChunk,finalize},{maxBuffer=Number.POSITIVE_INFINITY}={})=>{if(!isAsyncIterable(stream))throw new Error("The first argument must be a Readable, a ReadableStream, or an async iterable.");let state=init();state.length=0;try{for await(let chunk of stream){let chunkType=getChunkType(chunk),convertedChunk=convertChunk[chunkType](chunk,state);appendChunk({convertedChunk,state,getSize,truncateChunk,addChunk,maxBuffer});}return appendFinalChunk({state,convertChunk,getSize,truncateChunk,addChunk,getFinalChunk,maxBuffer}),finalize(state)}catch(error2){throw error2.bufferedData=finalize(state),error2}},appendFinalChunk=({state,getSize,truncateChunk,addChunk,getFinalChunk,maxBuffer})=>{let convertedChunk=getFinalChunk(state);convertedChunk!==void 0&&appendChunk({convertedChunk,state,getSize,truncateChunk,addChunk,maxBuffer});},appendChunk=({convertedChunk,state,getSize,truncateChunk,addChunk,maxBuffer})=>{let chunkSize=getSize(convertedChunk),newLength=state.length+chunkSize;if(newLength<=maxBuffer){addNewChunk(convertedChunk,state,addChunk,newLength);return}let truncatedChunk=truncateChunk(convertedChunk,maxBuffer-state.length);throw truncatedChunk!==void 0&&addNewChunk(truncatedChunk,state,addChunk,maxBuffer),new MaxBufferError},addNewChunk=(convertedChunk,state,addChunk,newLength)=>{state.contents=addChunk(convertedChunk,state,newLength),state.length=newLength;},isAsyncIterable=stream=>typeof stream=="object"&&stream!==null&&typeof stream[Symbol.asyncIterator]=="function",getChunkType=chunk=>{let typeOfChunk=typeof chunk;if(typeOfChunk==="string")return "string";if(typeOfChunk!=="object"||chunk===null)return "others";if(globalThis.Buffer?.isBuffer(chunk))return "buffer";let prototypeName=objectToString.call(chunk);return prototypeName==="[object ArrayBuffer]"?"arrayBuffer":prototypeName==="[object DataView]"?"dataView":Number.isInteger(chunk.byteLength)&&Number.isInteger(chunk.byteOffset)&&objectToString.call(chunk.buffer)==="[object ArrayBuffer]"?"typedArray":"others"},{toString:objectToString}=Object.prototype,MaxBufferError=class extends Error{name="MaxBufferError";constructor(){super("maxBuffer exceeded");}};var identity=value=>value,noop=()=>{},getContentsProp=({contents})=>contents,throwObjectStream=chunk=>{throw new Error(`Streams in object mode are not supported: ${String(chunk)}`)},getLengthProp=convertedChunk=>convertedChunk.length;async function getStreamAsArrayBuffer(stream,options){return getStreamContents(stream,arrayBufferMethods,options)}var initArrayBuffer=()=>({contents:new ArrayBuffer(0)}),useTextEncoder=chunk=>textEncoder.encode(chunk),textEncoder=new TextEncoder,useUint8Array=chunk=>new Uint8Array(chunk),useUint8ArrayWithOffset=chunk=>new Uint8Array(chunk.buffer,chunk.byteOffset,chunk.byteLength),truncateArrayBufferChunk=(convertedChunk,chunkSize)=>convertedChunk.slice(0,chunkSize),addArrayBufferChunk=(convertedChunk,{contents,length:previousLength},length)=>{let newContents=hasArrayBufferResize()?resizeArrayBuffer(contents,length):resizeArrayBufferSlow(contents,length);return new Uint8Array(newContents).set(convertedChunk,previousLength),newContents},resizeArrayBufferSlow=(contents,length)=>{if(length<=contents.byteLength)return contents;let arrayBuffer=new ArrayBuffer(getNewContentsLength(length));return new Uint8Array(arrayBuffer).set(new Uint8Array(contents),0),arrayBuffer},resizeArrayBuffer=(contents,length)=>{if(length<=contents.maxByteLength)return contents.resize(length),contents;let arrayBuffer=new ArrayBuffer(length,{maxByteLength:getNewContentsLength(length)});return new Uint8Array(arrayBuffer).set(new Uint8Array(contents),0),arrayBuffer},getNewContentsLength=length=>SCALE_FACTOR**Math.ceil(Math.log(length)/Math.log(SCALE_FACTOR)),SCALE_FACTOR=2,finalizeArrayBuffer=({contents,length})=>hasArrayBufferResize()?contents:contents.slice(0,length),hasArrayBufferResize=()=>"resize"in ArrayBuffer.prototype,arrayBufferMethods={init:initArrayBuffer,convertChunk:{string:useTextEncoder,buffer:useUint8Array,arrayBuffer:useUint8Array,dataView:useUint8ArrayWithOffset,typedArray:useUint8ArrayWithOffset,others:throwObjectStream},getSize:getLengthProp,truncateChunk:truncateArrayBufferChunk,addChunk:addArrayBufferChunk,getFinalChunk:noop,finalize:finalizeArrayBuffer};async function getStreamAsBuffer(stream,options){if(!("Buffer"in globalThis))throw new Error("getStreamAsBuffer() is only supported in Node.js");try{return arrayBufferToNodeBuffer(await getStreamAsArrayBuffer(stream,options))}catch(error2){throw error2.bufferedData!==void 0&&(error2.bufferedData=arrayBufferToNodeBuffer(error2.bufferedData)),error2}}var arrayBufferToNodeBuffer=arrayBuffer=>globalThis.Buffer.from(arrayBuffer);async function getStreamAsString(stream,options){return getStreamContents(stream,stringMethods,options)}var initString=()=>({contents:"",textDecoder:new TextDecoder}),useTextDecoder=(chunk,{textDecoder})=>textDecoder.decode(chunk,{stream:!0}),addStringChunk=(convertedChunk,{contents})=>contents+convertedChunk,truncateStringChunk=(convertedChunk,chunkSize)=>convertedChunk.slice(0,chunkSize),getFinalStringChunk=({textDecoder})=>{let finalChunk=textDecoder.decode();return finalChunk===""?void 0:finalChunk},stringMethods={init:initString,convertChunk:{string:identity,buffer:useTextDecoder,arrayBuffer:useTextDecoder,dataView:useTextDecoder,typedArray:useTextDecoder,others:throwObjectStream},getSize:getLengthProp,truncateChunk:truncateStringChunk,addChunk:addStringChunk,getFinalChunk:getFinalStringChunk,finalize:getContentsProp};var import_merge_stream=__toESM(require_merge_stream(),1),validateInputOptions=input=>{if(input!==void 0)throw new TypeError("The `input` and `inputFile` options cannot be both set.")},getInput=({input,inputFile})=>typeof inputFile!="string"?input:(validateInputOptions(input),fs.createReadStream(inputFile)),handleInput=(spawned,options)=>{let input=getInput(options);input!==void 0&&(isStream(input)?input.pipe(spawned.stdin):spawned.stdin.end(input));},makeAllStream=(spawned,{all})=>{if(!all||!spawned.stdout&&!spawned.stderr)return;let mixed=(0, import_merge_stream.default)();return spawned.stdout&&mixed.add(spawned.stdout),spawned.stderr&&mixed.add(spawned.stderr),mixed},getBufferedData=async(stream,streamPromise)=>{if(!(!stream||streamPromise===void 0)){await promises.setTimeout(0),stream.destroy();try{return await streamPromise}catch(error2){return error2.bufferedData}}},getStreamPromise=(stream,{encoding,buffer,maxBuffer})=>{if(!(!stream||!buffer))return encoding==="utf8"||encoding==="utf-8"?getStreamAsString(stream,{maxBuffer}):encoding===null||encoding==="buffer"?getStreamAsBuffer(stream,{maxBuffer}):applyEncoding(stream,maxBuffer,encoding)},applyEncoding=async(stream,maxBuffer,encoding)=>(await getStreamAsBuffer(stream,{maxBuffer})).toString(encoding),getSpawnedResult=async({stdout,stderr,all},{encoding,buffer,maxBuffer},processDone)=>{let stdoutPromise=getStreamPromise(stdout,{encoding,buffer,maxBuffer}),stderrPromise=getStreamPromise(stderr,{encoding,buffer,maxBuffer}),allPromise=getStreamPromise(all,{encoding,buffer,maxBuffer:maxBuffer*2});try{return await Promise.all([processDone,stdoutPromise,stderrPromise,allPromise])}catch(error2){return Promise.all([{error:error2,signal:error2.signal,timedOut:error2.timedOut},getBufferedData(stdout,stdoutPromise),getBufferedData(stderr,stderrPromise),getBufferedData(all,allPromise)])}};var nativePromisePrototype=(async()=>{})().constructor.prototype,descriptors=["then","catch","finally"].map(property=>[property,Reflect.getOwnPropertyDescriptor(nativePromisePrototype,property)]),mergePromise=(spawned,promise)=>{for(let[property,descriptor]of descriptors){let value=typeof promise=="function"?(...args)=>Reflect.apply(descriptor.value,promise(),args):descriptor.value.bind(promise);Reflect.defineProperty(spawned,property,{...descriptor,value});}},getSpawnedPromise=spawned=>new Promise((resolve2,reject)=>{spawned.on("exit",(exitCode,signal)=>{resolve2({exitCode,signal});}),spawned.on("error",error2=>{reject(error2);}),spawned.stdin&&spawned.stdin.on("error",error2=>{reject(error2);});});var normalizeArgs=(file,args=[])=>Array.isArray(args)?[file,...args]:[file],NO_ESCAPE_REGEXP=/^[\w.-]+$/,escapeArg=arg=>typeof arg!="string"||NO_ESCAPE_REGEXP.test(arg)?arg:`"${arg.replaceAll('"','\\"')}"`,joinCommand=(file,args)=>normalizeArgs(file,args).join(" "),getEscapedCommand=(file,args)=>normalizeArgs(file,args).map(arg=>escapeArg(arg)).join(" ");var verboseDefault=util.debuglog("execa").enabled,padField=(field,padding)=>String(field).padStart(padding,"0"),getTimestamp=()=>{let date=new Date;return `${padField(date.getHours(),2)}:${padField(date.getMinutes(),2)}:${padField(date.getSeconds(),2)}.${padField(date.getMilliseconds(),3)}`},logCommand=(escapedCommand,{verbose})=>{verbose&&process2__default.default.stderr.write(`[${getTimestamp()}] ${escapedCommand}
`);};var DEFAULT_MAX_BUFFER=1e3*1e3*100,getEnv=({env:envOption,extendEnv,preferLocal,localDir,execPath})=>{let env=extendEnv?{...process2__default.default.env,...envOption}:envOption;return preferLocal?npmRunPathEnv({env,cwd:localDir,execPath}):env},handleArguments=(file,args,options={})=>{let parsed=import_cross_spawn.default._parse(file,args,options);return file=parsed.command,args=parsed.args,options=parsed.options,options={maxBuffer:DEFAULT_MAX_BUFFER,buffer:!0,stripFinalNewline:!0,extendEnv:!0,preferLocal:!1,localDir:options.cwd||process2__default.default.cwd(),execPath:process2__default.default.execPath,encoding:"utf8",reject:!0,cleanup:!0,all:!1,windowsHide:!0,verbose:verboseDefault,...options},options.env=getEnv(options),options.stdio=normalizeStdio(options),process2__default.default.platform==="win32"&&path__default.default.basename(file,".exe")==="cmd"&&args.unshift("/q"),{file,args,options,parsed}},handleOutput=(options,value,error2)=>typeof value!="string"&&!buffer.Buffer.isBuffer(value)?error2===void 0?void 0:"":options.stripFinalNewline?stripFinalNewline(value):value;function execa(file,args,options){let parsed=handleArguments(file,args,options),command=joinCommand(file,args),escapedCommand=getEscapedCommand(file,args);logCommand(escapedCommand,parsed.options),validateTimeout(parsed.options);let spawned;try{spawned=childProcess__default.default.spawn(parsed.file,parsed.args,parsed.options);}catch(error2){let dummySpawned=new childProcess__default.default.ChildProcess,errorPromise=Promise.reject(makeError({error:error2,stdout:"",stderr:"",all:"",command,escapedCommand,parsed,timedOut:!1,isCanceled:!1,killed:!1}));return mergePromise(dummySpawned,errorPromise),dummySpawned}let spawnedPromise=getSpawnedPromise(spawned),timedPromise=setupTimeout(spawned,parsed.options,spawnedPromise),processDone=setExitHandler(spawned,parsed.options,timedPromise),context={isCanceled:!1};spawned.kill=spawnedKill.bind(null,spawned.kill.bind(spawned)),spawned.cancel=spawnedCancel.bind(null,spawned,context);let handlePromiseOnce=onetime_default(async()=>{let[{error:error2,exitCode,signal,timedOut},stdoutResult,stderrResult,allResult]=await getSpawnedResult(spawned,parsed.options,processDone),stdout=handleOutput(parsed.options,stdoutResult),stderr=handleOutput(parsed.options,stderrResult),all=handleOutput(parsed.options,allResult);if(error2||exitCode!==0||signal!==null){let returnedError=makeError({error:error2,exitCode,signal,stdout,stderr,all,command,escapedCommand,parsed,timedOut,isCanceled:(parsed.options.signal?parsed.options.signal.aborted:!1),killed:spawned.killed});if(!parsed.options.reject)return returnedError;throw returnedError}return {command,escapedCommand,exitCode:0,stdout,stderr,all,failed:!1,timedOut:!1,isCanceled:!1,killed:!1}});return handleInput(spawned,parsed.options),spawned.all=makeAllStream(spawned,parsed.options),addPipeMethods(spawned),mergePromise(spawned,handlePromiseOnce),spawned}var Node=class{value;next;constructor(value){this.value=value;}},Queue=class{#head;#tail;#size;constructor(){this.clear();}enqueue(value){let node=new Node(value);this.#head?(this.#tail.next=node,this.#tail=node):(this.#head=node,this.#tail=node),this.#size++;}dequeue(){let current=this.#head;if(current)return this.#head=this.#head.next,this.#size--,current.value}peek(){if(this.#head)return this.#head.value}clear(){this.#head=void 0,this.#tail=void 0,this.#size=0;}get size(){return this.#size}*[Symbol.iterator](){let current=this.#head;for(;current;)yield current.value,current=current.next;}*drain(){for(;this.#head;)yield this.dequeue();}};function pLimit(concurrency){if(!((Number.isInteger(concurrency)||concurrency===Number.POSITIVE_INFINITY)&&concurrency>0))throw new TypeError("Expected `concurrency` to be a number from 1 and up");let queue=new Queue,activeCount=0,next=()=>{activeCount--,queue.size>0&&queue.dequeue()();},run=async(fn,resolve2,args)=>{activeCount++;let result=(async()=>fn(...args))();resolve2(result);try{await result;}catch{}next();},enqueue=(fn,resolve2,args)=>{queue.enqueue(run.bind(void 0,fn,resolve2,args)),(async()=>(await Promise.resolve(),activeCount<concurrency&&queue.size>0&&queue.dequeue()()))();},generator=(fn,...args)=>new Promise(resolve2=>{enqueue(fn,resolve2,args);});return Object.defineProperties(generator,{activeCount:{get:()=>activeCount},pendingCount:{get:()=>queue.size},clearQueue:{value:()=>{queue.clear();}}}),generator}var EndError=class extends Error{constructor(value){super(),this.value=value;}},testElement=async(element,tester)=>tester(await element),finder=async element=>{let values=await Promise.all(element);if(values[1]===!0)throw new EndError(values[0]);return !1};async function pLocate(iterable,tester,{concurrency=Number.POSITIVE_INFINITY,preserveOrder=!0}={}){let limit=pLimit(concurrency),items=[...iterable].map(element=>[element,limit(testElement,element,tester)]),checkLimit=pLimit(preserveOrder?1:Number.POSITIVE_INFINITY);try{await Promise.all(items.map(element=>checkLimit(finder,element)));}catch(error2){if(error2 instanceof EndError)return error2.value;throw error2}}var typeMappings={directory:"isDirectory",file:"isFile"};function checkType(type){if(!Object.hasOwnProperty.call(typeMappings,type))throw new Error(`Invalid type specified: ${type}`)}var matchType=(type,stat)=>stat[typeMappings[type]](),toPath=urlOrPath=>urlOrPath instanceof URL?url.fileURLToPath(urlOrPath):urlOrPath;async function locatePath(paths,{cwd:cwd2=process2__default.default.cwd(),type="file",allowSymlinks=!0,concurrency,preserveOrder}={}){checkType(type),cwd2=toPath(cwd2);let statFunction=allowSymlinks?fs.promises.stat:fs.promises.lstat;return pLocate(paths,async path_=>{try{let stat=await statFunction(path__default.default.resolve(cwd2,path_));return matchType(type,stat)}catch{return !1}},{concurrency,preserveOrder})}function toPath2(urlOrPath){return urlOrPath instanceof URL?url.fileURLToPath(urlOrPath):urlOrPath}var findUpStop=Symbol("findUpStop");async function findUpMultiple(name,options={}){let directory=path__default.default.resolve(toPath2(options.cwd)??""),{root}=path__default.default.parse(directory),stopAt=path__default.default.resolve(directory,toPath2(options.stopAt??root)),limit=options.limit??Number.POSITIVE_INFINITY,paths=[name].flat(),runMatcher=async locateOptions=>{if(typeof name!="function")return locatePath(paths,locateOptions);let foundPath=await name(locateOptions.cwd);return typeof foundPath=="string"?locatePath([foundPath],locateOptions):foundPath},matches=[];for(;;){let foundPath=await runMatcher({...options,cwd:directory});if(foundPath===findUpStop||(foundPath&&matches.push(path__default.default.resolve(directory,foundPath)),directory===stopAt||matches.length>=limit))break;directory=path__default.default.dirname(directory);}return matches}async function findUp(name,options={}){return (await findUpMultiple(name,{...options,limit:1}))[0]}var _DRIVE_LETTER_START_RE=/^[A-Za-z]:\//;function normalizeWindowsPath(input=""){return input&&input.replace(/\\/g,"/").replace(_DRIVE_LETTER_START_RE,r=>r.toUpperCase())}var _UNC_REGEX=/^[/\\]{2}/,_IS_ABSOLUTE_RE=/^[/\\](?![/\\])|^[/\\]{2}(?!\.)|^[A-Za-z]:[/\\]/,_DRIVE_LETTER_RE=/^[A-Za-z]:$/,_ROOT_FOLDER_RE=/^\/([A-Za-z]:)?$/;var normalize=function(path6){if(path6.length===0)return ".";path6=normalizeWindowsPath(path6);let isUNCPath=path6.match(_UNC_REGEX),isPathAbsolute=isAbsolute(path6),trailingSeparator=path6[path6.length-1]==="/";return path6=normalizeString(path6,!isPathAbsolute),path6.length===0?isPathAbsolute?"/":trailingSeparator?"./":".":(trailingSeparator&&(path6+="/"),_DRIVE_LETTER_RE.test(path6)&&(path6+="/"),isUNCPath?isPathAbsolute?`//${path6}`:`//./${path6}`:isPathAbsolute&&!isAbsolute(path6)?`/${path6}`:path6)},join=function(...arguments_){if(arguments_.length===0)return ".";let joined;for(let argument of arguments_)argument&&argument.length>0&&(joined===void 0?joined=argument:joined+=`/${argument}`);return joined===void 0?".":normalize(joined.replace(/\/\/+/g,"/"))};function cwd(){return typeof process<"u"&&typeof process.cwd=="function"?process.cwd().replace(/\\/g,"/"):"/"}var resolve=function(...arguments_){arguments_=arguments_.map(argument=>normalizeWindowsPath(argument));let resolvedPath="",resolvedAbsolute=!1;for(let index=arguments_.length-1;index>=-1&&!resolvedAbsolute;index--){let path6=index>=0?arguments_[index]:cwd();!path6||path6.length===0||(resolvedPath=`${path6}/${resolvedPath}`,resolvedAbsolute=isAbsolute(path6));}return resolvedPath=normalizeString(resolvedPath,!resolvedAbsolute),resolvedAbsolute&&!isAbsolute(resolvedPath)?`/${resolvedPath}`:resolvedPath.length>0?resolvedPath:"."};function normalizeString(path6,allowAboveRoot){let res="",lastSegmentLength=0,lastSlash=-1,dots=0,char=null;for(let index=0;index<=path6.length;++index){if(index<path6.length)char=path6[index];else {if(char==="/")break;char="/";}if(char==="/"){if(!(lastSlash===index-1||dots===1))if(dots===2){if(res.length<2||lastSegmentLength!==2||res[res.length-1]!=="."||res[res.length-2]!=="."){if(res.length>2){let lastSlashIndex=res.lastIndexOf("/");lastSlashIndex===-1?(res="",lastSegmentLength=0):(res=res.slice(0,lastSlashIndex),lastSegmentLength=res.length-1-res.lastIndexOf("/")),lastSlash=index,dots=0;continue}else if(res.length>0){res="",lastSegmentLength=0,lastSlash=index,dots=0;continue}}allowAboveRoot&&(res+=res.length>0?"/..":"..",lastSegmentLength=2);}else res.length>0?res+=`/${path6.slice(lastSlash+1,index)}`:res=path6.slice(lastSlash+1,index),lastSegmentLength=index-lastSlash-1;lastSlash=index,dots=0;}else char==="."&&dots!==-1?++dots:dots=-1;}return res}var isAbsolute=function(p){return _IS_ABSOLUTE_RE.test(p)};var relative=function(from,to){let _from=resolve(from).replace(_ROOT_FOLDER_RE,"$1").split("/"),_to=resolve(to).replace(_ROOT_FOLDER_RE,"$1").split("/");if(_to[0][1]===":"&&_from[0][1]===":"&&_from[0]!==_to[0])return _to.join("/");let _fromCopy=[..._from];for(let segment of _fromCopy){if(_to[0]!==segment)break;_from.shift(),_to.shift();}return [..._from.map(()=>".."),..._to].join("/")},dirname=function(p){let segments=normalizeWindowsPath(p).replace(/\/$/,"").split("/").slice(0,-1);return segments.length===1&&_DRIVE_LETTER_RE.test(segments[0])&&(segments[0]+="/"),segments.join("/")||(isAbsolute(p)?"/":".")};var import_semver=__toESM(require_semver2());var DOCUMENTATION_LINK2="writing-tests/integrations/vitest-addon";var SUPPORTED_FRAMEWORKS=["@storybook/nextjs","@storybook/nextjs-vite","@storybook/react-vite","@storybook/svelte-vite","@storybook/vue3-vite","@storybook/html-vite","@storybook/web-components-vite","@storybook/sveltekit","@storybook/react-native-web-vite"];var fancy=process.platform!=="win32"||process.env.CI||process.env.TERM==="xterm-256color",step=nodeLogger.colors.gray("\u203A");nodeLogger.colors.blue(fancy?"\u2139":"i");nodeLogger.colors.green(fancy?"\u2714":"\u221A");nodeLogger.colors.orange(fancy?"\u26A0":"\u203C");nodeLogger.colors.red(fancy?"\u2716":"\xD7");var baseOptions={borderStyle:"round",padding:1},print=(message,options)=>{nodeLogger.logger.line(1),nodeLogger.logger.logBox(message,{...baseOptions,...options});},printInfo=(title,message,options)=>print(message,{borderColor:"blue",title,...options}),printWarning=(title,message,options)=>print(message,{borderColor:"yellow",title,...options}),printError=(title,message,options)=>print(message,{borderColor:"red",title,...options}),printSuccess=(title,message,options)=>print(message,{borderColor:"green",title,...options});var loadTemplate=async(name,replacements)=>{let template=await fs4__namespace.readFile(join(dirname(__require.resolve("@storybook/addon-vitest/package.json")),"templates",name),"utf8");return Object.entries(replacements).forEach(([key,value])=>template=template.replace(key,value)),template},mergeProperties=(source,target)=>{for(let sourceProp of source)if(sourceProp.type==="ObjectProperty"){let targetProp=target.find(p=>sourceProp.key.type==="Identifier"&&p.type==="ObjectProperty"&&p.key.type==="Identifier"&&p.key.name===sourceProp.key.name);targetProp&&targetProp.type==="ObjectProperty"?sourceProp.value.type==="ObjectExpression"&&targetProp.value.type==="ObjectExpression"?mergeProperties(sourceProp.value.properties,targetProp.value.properties):sourceProp.value.type==="ArrayExpression"&&targetProp.value.type==="ArrayExpression"?targetProp.value.elements.push(...sourceProp.value.elements):targetProp.value=sourceProp.value:target.push(sourceProp);}},updateConfigFile=(source,target)=>{let updated=!1;for(let sourceNode of source.program.body)if(sourceNode.type==="ImportDeclaration"){if(!target.program.body.some(targetNode=>targetNode.type===sourceNode.type&&targetNode.specifiers.some(s=>s.local.name===sourceNode.specifiers[0].local.name))){let lastImport=target.program.body.findLastIndex(n=>n.type==="ImportDeclaration");target.program.body.splice(lastImport+1,0,sourceNode);}}else if(sourceNode.type==="VariableDeclaration"){if(!target.program.body.some(targetNode=>targetNode.type===sourceNode.type&&targetNode.declarations.some(d=>"name"in d.id&&"name"in sourceNode.declarations[0].id&&d.id.name===sourceNode.declarations[0].id.name))){let lastImport=target.program.body.findLastIndex(n=>n.type==="ImportDeclaration");target.program.body.splice(lastImport+1,0,sourceNode);}}else if(sourceNode.type==="ExportDefaultDeclaration"){let exportDefault=target.program.body.find(n=>n.type==="ExportDefaultDeclaration");if(exportDefault&&sourceNode.declaration.type==="CallExpression"&&sourceNode.declaration.arguments.length>0&&sourceNode.declaration.arguments[0].type==="ObjectExpression"){let{properties}=sourceNode.declaration.arguments[0];exportDefault.declaration.type==="ObjectExpression"?(mergeProperties(properties,exportDefault.declaration.properties),updated=!0):exportDefault.declaration.type==="CallExpression"&&exportDefault.declaration.callee.type==="Identifier"&&exportDefault.declaration.callee.name==="defineConfig"&&exportDefault.declaration.arguments[0]?.type==="ObjectExpression"&&(mergeProperties(properties,exportDefault.declaration.arguments[0].properties),updated=!0);}}return updated},updateWorkspaceFile=(source,target)=>{let updated=!1;for(let sourceNode of source.program.body)if(sourceNode.type==="ImportDeclaration"){if(!target.program.body.some(targetNode=>targetNode.type===sourceNode.type&&targetNode.source.value===sourceNode.source.value&&targetNode.specifiers.some(s=>s.local.name===sourceNode.specifiers[0].local.name))){let lastImport=target.program.body.findLastIndex(n=>n.type==="ImportDeclaration");target.program.body.splice(lastImport+1,0,sourceNode);}}else if(sourceNode.type==="VariableDeclaration"){if(!target.program.body.some(targetNode=>targetNode.type===sourceNode.type&&targetNode.declarations.some(d=>"name"in d.id&&"name"in sourceNode.declarations[0].id&&d.id.name===sourceNode.declarations[0].id.name))){let lastImport=target.program.body.findLastIndex(n=>n.type==="ImportDeclaration");target.program.body.splice(lastImport+1,0,sourceNode);}}else if(sourceNode.type==="ExportDefaultDeclaration"){let exportDefault=target.program.body.find(n=>n.type==="ExportDefaultDeclaration");if(exportDefault&&sourceNode.declaration.type==="CallExpression"&&sourceNode.declaration.arguments.length>0&&sourceNode.declaration.arguments[0].type==="ArrayExpression"&&sourceNode.declaration.arguments[0].elements.length>0){let{elements}=sourceNode.declaration.arguments[0];exportDefault.declaration.type==="ArrayExpression"?(exportDefault.declaration.elements.push(...elements),updated=!0):exportDefault.declaration.type==="CallExpression"&&exportDefault.declaration.callee.type==="Identifier"&&exportDefault.declaration.callee.name==="defineWorkspace"&&exportDefault.declaration.arguments[0]?.type==="ArrayExpression"&&(exportDefault.declaration.arguments[0].elements.push(...elements),updated=!0);}}return updated};function getAddonNames(mainConfig){return (mainConfig.addons||[]).map(addon=>{let name="";return typeof addon=="string"?name=addon:typeof addon=="object"&&(name=addon.name),name}).filter(item=>item!=null)}var ADDON_NAME="@storybook/addon-vitest",EXTENSIONS=[".ts",".tsx",".js",".jsx",".cts",".mts",".cjs",".mjs"],addonA11yName="@storybook/addon-a11y",hasErrors=!1,logErrors=(...args)=>{hasErrors=!0,printError(...args);},findFile=async(basename2,extensions=EXTENSIONS)=>findUp(extensions.map(ext=>basename2+ext),{stopAt:common.getProjectRoot()});async function postInstall(options){printSuccess("\u{1F44B} Howdy!",tsDedent.dedent`
      I'm the installation helper for ${ADDON_NAME}

      Hold on for a moment while I look at your project and get it set up...
    `);let packageManager=common.JsPackageManagerFactory.getPackageManager({force:options.packageManager}),info2=await getStorybookInfo(options),allDeps=packageManager.getAllDependencies(),dependencies=["vitest","@vitest/browser","playwright"].filter(p=>!allDeps[p]),vitestVersionSpecifier=await packageManager.getInstalledVersion("vitest"),coercedVitestVersion=vitestVersionSpecifier?(0, import_semver.coerce)(vitestVersionSpecifier):null,isVitest3_2OrNewer=vitestVersionSpecifier?(0, import_semver.satisfies)(vitestVersionSpecifier,">=3.2.0"):!0,mainJsPath=common.serverResolve(resolve(options.configDir,"main")),config=await csfTools.readConfig(mainJsPath),hasCustomWebpackConfig=!!config.getFieldNode(["webpackFinal"]),isInteractive=process.stdout.isTTY&&!process.env.CI;info2.frameworkPackageName==="@storybook/nextjs"&&!hasCustomWebpackConfig&&(options.yes||!isInteractive?{migrateToNextjsVite:!!options.yes}:await prompts__default.default({type:"confirm",name:"migrateToNextjsVite",message:tsDedent.dedent`
            The addon requires the use of @storybook/nextjs-vite to work with Next.js.
            https://storybook.js.org/docs/next/${DOCUMENTATION_LINK2}#install-and-set-up

            Do you want to migrate?
          `,initial:!0})).migrateToNextjsVite&&(await packageManager.addDependencies({type:"devDependencies",skipInstall:!0},["@storybook/nextjs-vite"]),await packageManager.removeDependencies(["@storybook/nextjs"]),babel.traverse(config._ast,{StringLiteral(path6){path6.node.value==="@storybook/nextjs"&&(path6.node.value="@storybook/nextjs-vite");}}),await csfTools.writeConfig(config,mainJsPath),info2.frameworkPackageName="@storybook/nextjs-vite",info2.builderPackageName="@storybook/builder-vite",await common.scanAndTransformFiles({promptMessage:"Enter a glob to scan for all @storybook/nextjs imports to substitute with @storybook/nextjs-vite:",force:options.yes,dryRun:!1,transformFn:(files,options2,dryRun)=>common.transformImportFiles(files,options2,dryRun),transformOptions:{"@storybook/nextjs":"@storybook/nextjs-vite"}}));let annotationsImport=SUPPORTED_FRAMEWORKS.includes(info2.frameworkPackageName)?info2.frameworkPackageName==="@storybook/nextjs"?"@storybook/nextjs-vite":info2.frameworkPackageName:null,isRendererSupported=!!annotationsImport,result=await(async()=>{let reasons=[];hasCustomWebpackConfig&&reasons.push("\u2022 The addon can not be used with a custom Webpack configuration."),info2.frameworkPackageName!=="@storybook/nextjs"&&info2.builderPackageName!=="@storybook/builder-vite"&&reasons.push("\u2022 The addon can only be used with a Vite-based Storybook framework or Next.js."),isRendererSupported||reasons.push(tsDedent.dedent`
        • The addon cannot yet be used with ${info2.frameworkPackageName}
      `),coercedVitestVersion&&!(0, import_semver.satisfies)(coercedVitestVersion,">=3.0.0")&&reasons.push(tsDedent.dedent`
        • The addon requires Vitest 3.0.0 or higher. You are currently using ${vitestVersionSpecifier}.
          Please update all of your Vitest dependencies and try again.
      `);let mswVersionSpecifier=await packageManager.getInstalledVersion("msw"),coercedMswVersion=mswVersionSpecifier?(0, import_semver.coerce)(mswVersionSpecifier):null;return coercedMswVersion&&!(0, import_semver.satisfies)(coercedMswVersion,">=2.0.0")&&reasons.push(tsDedent.dedent`
        • The addon uses Vitest behind the scenes, which supports only version 2 and above of MSW. However, we have detected version ${coercedMswVersion.version} in this project.
        Please update the 'msw' package and try again.
      `),info2.frameworkPackageName==="@storybook/nextjs"&&(await packageManager.getInstalledVersion("next")||reasons.push(tsDedent.dedent`
          • You are using @storybook/nextjs without having "next" installed.
            Please install "next" or use a different Storybook framework integration and try again.
        `)),reasons.length>0?(reasons.unshift("@storybook/addon-vitest's automated setup failed due to the following package incompatibilities:"),reasons.push("--------------------------------"),reasons.push(tsDedent.dedent`
          You can fix these issues and rerun the command to reinstall. If you wish to roll back the installation, remove ${ADDON_NAME} from the "addons" array
          in your main Storybook config file and remove the dependency from your package.json file.
        `),isRendererSupported?reasons.push(tsDedent.dedent`
            Fear not, however, you can follow the manual installation process instead at:
            https://storybook.js.org/docs/next/${DOCUMENTATION_LINK2}#manual-setup
          `):reasons.push(tsDedent.dedent`
            Please check the documentation for more information about its requirements and installation:
            https://storybook.js.org/docs/next/${DOCUMENTATION_LINK2}
          `),reasons.map(r=>r.trim()).join(`

`)):null})();if(result){logErrors("\u26D4\uFE0F Sorry!",result),nodeLogger.logger.line(1);return}if(info2.frameworkPackageName==="@storybook/nextjs"){printInfo("\u{1F37F} Just so you know...",tsDedent.dedent`
        It looks like you're using Next.js.

        Adding "@storybook/nextjs-vite/vite-plugin" so you can use it with Vitest.

        More info about the plugin at https://github.com/storybookjs/vite-plugin-storybook-nextjs
      `);try{let storybookVersion=await packageManager.getInstalledVersion("storybook");dependencies.push(`@storybook/nextjs-vite@^${storybookVersion}`);}catch{console.error("Failed to install @storybook/nextjs-vite. Please install it manually");}}let v8Version=await packageManager.getInstalledVersion("@vitest/coverage-v8"),istanbulVersion=await packageManager.getInstalledVersion("@vitest/coverage-istanbul");!v8Version&&!istanbulVersion&&(printInfo("\u{1F648} Let me cover this for you",tsDedent.dedent`
        You don't seem to have a coverage reporter installed. Vitest needs either V8 or Istanbul to generate coverage reports.

        Adding "@vitest/coverage-v8" to enable coverage reporting.
        Read more about Vitest coverage providers at https://vitest.dev/guide/coverage.html#coverage-providers
      `),dependencies.push("@vitest/coverage-v8"));let versionedDependencies=dependencies.map(p=>p.includes("vitest")&&vitestVersionSpecifier?`${p}@${vitestVersionSpecifier}`:p);versionedDependencies.length>0&&(await packageManager.addDependencies({type:"devDependencies",skipInstall:!0},versionedDependencies),nodeLogger.logger.line(1),nodeLogger.logger.plain(`${step} Installing dependencies:`),nodeLogger.logger.plain("  "+versionedDependencies.join(", "))),await packageManager.installDependencies(),nodeLogger.logger.line(1),options.skipInstall?(nodeLogger.logger.plain("Skipping Playwright installation, please run this command manually:"),nodeLogger.logger.plain("  npx playwright install chromium --with-deps")):(nodeLogger.logger.plain(`${step} Configuring Playwright with Chromium (this might take some time):`),nodeLogger.logger.plain("  npx playwright install chromium --with-deps"),await packageManager.executeCommand({command:"npx",args:["playwright","install","chromium","--with-deps"]}));let fileExtension=allDeps.typescript||await findFile("tsconfig",[...EXTENSIONS,".json"])?"ts":"js",vitestSetupFile=resolve(options.configDir,`vitest.setup.${fileExtension}`);if(fs.existsSync(vitestSetupFile)){logErrors("\u{1F6A8} Oh no!",tsDedent.dedent`
        Found an existing Vitest setup file:
        ${vitestSetupFile}

        Please refer to the documentation to complete the setup manually:
        https://storybook.js.org/docs/next/${DOCUMENTATION_LINK2}#manual-setup
      `),nodeLogger.logger.line(1);return}nodeLogger.logger.line(1),nodeLogger.logger.plain(`${step} Creating a Vitest setup file for Storybook:`),nodeLogger.logger.plain(`  ${vitestSetupFile}`);let previewExists=EXTENSIONS.map(ext=>resolve(options.configDir,`preview${ext}`)).some(fs.existsSync),imports=[`import { setProjectAnnotations } from '${annotationsImport}';`],projectAnnotations=[];previewExists&&(imports.push("import * as projectAnnotations from './preview';"),projectAnnotations.push("projectAnnotations")),await fs4.writeFile(vitestSetupFile,tsDedent.dedent`
      ${imports.join(`
`)}

      // This is an important step to apply the right configuration when testing your stories.
      // More info at: https://storybook.js.org/docs/api/portable-stories/portable-stories-vitest#setprojectannotations
      setProjectAnnotations([${projectAnnotations.join(", ")}]);
    `);let vitestWorkspaceFile=await findFile("vitest.workspace",[".ts",".js",".json"])||await findFile("vitest.projects",[".ts",".js",".json"]),viteConfigFile=await findFile("vite.config"),vitestConfigFile=await findFile("vitest.config"),vitestShimFile=await findFile("vitest.shims.d"),rootConfig=vitestConfigFile||viteConfigFile,browserConfig=`{
        enabled: true,
        headless: true,
        provider: 'playwright',
        instances: [{ browser: 'chromium' }]
      }`;if(fileExtension==="ts"&&!vitestShimFile&&await fs4.writeFile("vitest.shims.d.ts",'/// <reference types="@vitest/browser/providers/playwright" />'),vitestWorkspaceFile){let workspaceTemplate=await loadTemplate("vitest.workspace.template.ts",{EXTENDS_WORKSPACE:viteConfigFile?relative(dirname(vitestWorkspaceFile),viteConfigFile):"",CONFIG_DIR:options.configDir,BROWSER_CONFIG:browserConfig,SETUP_FILE:relative(dirname(vitestWorkspaceFile),vitestSetupFile)}).then(t=>t.replace(`
  'ROOT_CONFIG',`,"").replace(/\s+extends: '',/,"")),workspaceFile=await fs4__namespace.readFile(vitestWorkspaceFile,"utf8"),source=babel.babelParse(workspaceTemplate),target=babel.babelParse(workspaceFile);if(updateWorkspaceFile(source,target)){nodeLogger.logger.line(1),nodeLogger.logger.plain(`${step} Updating your Vitest workspace file:`),nodeLogger.logger.plain(`  ${vitestWorkspaceFile}`);let formattedContent=await common.formatFileContent(vitestWorkspaceFile,babel.generate(target).code);await fs4.writeFile(vitestWorkspaceFile,formattedContent);}else {logErrors("\u{1F6A8} Oh no!",tsDedent.dedent`
          Could not update existing Vitest workspace file:
          ${vitestWorkspaceFile}

          I was able to configure most of the addon but could not safely extend
          your existing workspace file automatically, you must do it yourself.

          Please refer to the documentation to complete the setup manually:
          https://storybook.js.org/docs/next/${DOCUMENTATION_LINK2}#manual-setup
        `),nodeLogger.logger.line(1);return}}else if(rootConfig){let target,updated,configFile=await fs4__namespace.readFile(rootConfig,"utf8"),hasProjectsConfig=configFile.includes("projects:"),configFileHasTypeReference=configFile.match(/\/\/\/\s*<reference\s+types=["']vitest\/config["']\s*\/>/),templateName=hasProjectsConfig||isVitest3_2OrNewer?"vitest.config.3.2.template.ts":"vitest.config.template.ts";if(templateName){let configTemplate=await loadTemplate(templateName,{CONFIG_DIR:options.configDir,BROWSER_CONFIG:browserConfig,SETUP_FILE:relative(dirname(rootConfig),vitestSetupFile)}),source=babel.babelParse(configTemplate);target=babel.babelParse(configFile),updated=updateConfigFile(source,target);}if(target&&updated){nodeLogger.logger.line(1),nodeLogger.logger.plain(`${step} Updating your ${vitestConfigFile?"Vitest":"Vite"} config file:`),nodeLogger.logger.plain(`  ${rootConfig}`);let formattedContent=await common.formatFileContent(rootConfig,babel.generate(target).code);await fs4.writeFile(rootConfig,configFileHasTypeReference?formattedContent:`/// <reference types="vitest/config" />
`+formattedContent);}else logErrors("\u{1F6A8} Oh no!",tsDedent.dedent`
        We were unable to update your existing ${vitestConfigFile?"Vitest":"Vite"} config file.

        Please refer to the documentation to complete the setup manually:
        https://storybook.js.org/docs/writing-tests/integrations/vitest-addon#manual-setup
      `);}else {let newConfigFile=resolve(`vitest.config.${fileExtension}`),configTemplate=await loadTemplate(isVitest3_2OrNewer?"vitest.config.3.2.template.ts":"vitest.config.template.ts",{CONFIG_DIR:options.configDir,BROWSER_CONFIG:browserConfig,SETUP_FILE:relative(dirname(newConfigFile),vitestSetupFile)});nodeLogger.logger.line(1),nodeLogger.logger.plain(`${step} Creating a Vitest config file:`),nodeLogger.logger.plain(`  ${newConfigFile}`);let formattedContent=await common.formatFileContent(newConfigFile,configTemplate);await fs4.writeFile(newConfigFile,formattedContent);}if(info2.addons.find(addon=>addon.includes(addonA11yName)))try{nodeLogger.logger.plain(`${step} Setting up ${addonA11yName} for @storybook/addon-vitest:`);let command=["automigrate","addon-a11y-addon-test"];command.push("--loglevel","silent"),command.push("--yes","--skip-doctor"),options.packageManager&&command.push("--package-manager",options.packageManager),options.skipInstall&&command.push("--skip-install"),options.configDir!==".storybook"&&command.push("--config-dir",options.configDir),await execa("storybook",command,{stdio:"inherit"});}catch(e){logErrors("\u{1F6A8} Oh no!",tsDedent.dedent`
        We have detected that you have ${addonA11yName} installed but could not automatically set it up for @storybook/addon-vitest:

        ${e instanceof Error?e.message:String(e)}

        Please refer to the documentation to complete the setup manually:
        https://storybook.js.org/docs/writing-tests/accessibility-testing#test-addon-integration
      `);}hasErrors?printWarning("\u26A0\uFE0F Done, but with errors!",tsDedent.dedent`
        @storybook/addon-vitest was installed successfully, but there were some errors during the setup process.

        Please refer to the documentation to complete the setup manually and check the errors above:
        https://storybook.js.org/docs/next/${DOCUMENTATION_LINK2}#manual-setup
      `):printSuccess("\u{1F389} All done!",tsDedent.dedent`
        @storybook/addon-vitest is now configured and you're ready to run your tests!
  
        Here are a couple of tips to get you started:
        • You can run tests with "${rootConfig?"npx vitest --project=storybook":"npx vitest"}"
        • When using the Vitest extension in your editor, all of your stories will be shown as tests!
  
        Check the documentation for more information about its features and options at:
        https://storybook.js.org/docs/next/${DOCUMENTATION_LINK2}
      `),nodeLogger.logger.line(1);}async function getStorybookInfo({configDir,packageManager:pkgMgr}){let packageManager=common.JsPackageManagerFactory.getPackageManager({force:pkgMgr,configDir}),{packageJson}=packageManager.primaryPackageJson,config=await common.loadMainConfig({configDir,noCache:!0}),{framework}=config,frameworkName=typeof framework=="string"?framework:framework?.name;common.validateFrameworkName(frameworkName);let frameworkPackageName=common.extractProperFrameworkName(frameworkName),core=await(await common.loadAllPresets({corePresets:[join(frameworkName,"preset")],overridePresets:[__require.resolve("storybook/internal/core-server/presets/common-override-preset")],packageJson,configDir,isCritical:!0})).apply("core",{}),{builder,renderer}=core;if(!builder)throw new Error("Could not detect your Storybook builder.");let builderPackageJson=await fs4__namespace.readFile(__require.resolve(join(typeof builder=="string"?builder:builder.name,"package.json")),"utf8"),builderPackageName=JSON.parse(builderPackageJson).name,rendererPackageName;if(renderer){let rendererPackageJson=await fs4__namespace.readFile(__require.resolve(join(renderer,"package.json")),"utf8");rendererPackageName=JSON.parse(rendererPackageJson).name;}return {frameworkPackageName,builderPackageName,rendererPackageName,addons:getAddonNames(config)}}

module.exports = postInstall;
