
# YoYo AI version control directory
.yoyo/

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production builds
dist/
build/
.next/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Storybook build outputs
storybook-static/

# TypeScript cache
*.tsbuildinfo

# Storybook logs
*storybook.log

# Package manager lock files (keep only one)
package-lock.json
yarn.lock
pnpm-lock.yaml

# Cache directories
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# Testing
test-results/
playwright-report/
coverage/
.nyc_output/

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Build artifacts
*.map
*.min.js
*.min.css

# Development tools
.vite/
.turbo/

# Documentation build
docs/.vitepress/cache
docs/.vitepress/dist

# Local development
.local/
local/

# Backup files
*.bak
*.backup
*.orig

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# Database files
*.db
*.sqlite
*.sqlite3

# Configuration files with sensitive data
config.local.*
.env.*.local
