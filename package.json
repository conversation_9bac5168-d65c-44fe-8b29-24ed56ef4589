{"name": "lyricwritingproject", "version": "1.0.0", "description": "", "main": "index.js", "directories": {"doc": "docs"}, "scripts": {"test": "vitest", "test:components": "vitest --project=components", "test:storybook": "vitest --project=storybook", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "next": "^15.4.4", "react": "^19.1.1", "react-dom": "^19.1.1", "typescript": "^5.8.3", "zustand": "^5.0.6"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.1", "@storybook/addon-a11y": "^9.0.18", "@storybook/addon-docs": "^9.0.18", "@storybook/addon-onboarding": "^9.0.18", "@storybook/addon-vitest": "^9.0.18", "@storybook/react-vite": "^9.0.18", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "jsdom": "^26.1.0", "playwright": "^1.54.1", "storybook": "^9.0.18", "vitest": "^3.2.4"}}