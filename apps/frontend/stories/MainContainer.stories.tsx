// Storybook 故事 - 主容器组件
import type { Meta, StoryObj } from '@storybook/react';
import MainContainer from '../../../frontend/app/main_container';

const meta: Meta<typeof MainContainer> = {
  title: '容器组件/主容器',
  component: MainContainer,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: '主容器组件，包含一级和二级容器布局，实现按键切换功能。',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  name: '默认状态',
  parameters: {
    docs: {
      description: {
        story: '默认状态下，模式按键激活，显示 ComponentB1 容器。',
      },
    },
  },
};

export const BusinessMode: Story = {
  name: '业务模式',
  parameters: {
    docs: {
      description: {
        story: '点击业务按键后，显示 ComponentB2 容器。',
      },
    },
  },
  play: async ({ canvasElement }) => {
    // 这里可以添加交互测试逻辑
    // 例如模拟点击业务按键
  },
};
