// 简单的容器组件验证脚本
const fs = require('fs');
const path = require('path');

console.log('🔍 验证容器组件文件...\n');

// 需要检查的文件列表
const filesToCheck = [
  'frontend/app/main_container.tsx',
  'frontend/componets/componet/componentA1.tsx',
  'frontend/componets/componet/componentB1.tsx',
  'frontend/componets/componet/componentB2.tsx',
  'frontend/componets/componet/componentButton.tsx',
  'frontend/componets/interaction/component_interactionB.tsx',
  'frontend/Store/store.ts'
];

let allFilesExist = true;

filesToCheck.forEach(file => {
  const fullPath = path.join(process.cwd(), file);
  if (fs.existsSync(fullPath)) {
    console.log(`✅ ${file} - 文件存在`);
    
    // 检查文件内容
    const content = fs.readFileSync(fullPath, 'utf8');
    if (content.length > 0) {
      console.log(`   📄 文件大小: ${content.length} 字符`);
    } else {
      console.log(`   ⚠️  文件为空`);
    }
  } else {
    console.log(`❌ ${file} - 文件不存在`);
    allFilesExist = false;
  }
});

console.log('\n🔍 验证组件导入关系...\n');

// 检查主容器是否正确导入子组件
const mainContainerPath = path.join(process.cwd(), 'frontend/app/main_container.tsx');
if (fs.existsSync(mainContainerPath)) {
  const content = fs.readFileSync(mainContainerPath, 'utf8');
  
  if (content.includes('ComponentA1')) {
    console.log('✅ 主容器正确导入 ComponentA1');
  } else {
    console.log('❌ 主容器未导入 ComponentA1');
  }
  
  if (content.includes('ComponentInteractionB')) {
    console.log('✅ 主容器正确导入 ComponentInteractionB');
  } else {
    console.log('❌ 主容器未导入 ComponentInteractionB');
  }
}

// 检查状态管理
const storePath = path.join(process.cwd(), 'frontend/Store/store.ts');
if (fs.existsSync(storePath)) {
  const content = fs.readFileSync(storePath, 'utf8');
  
  if (content.includes('zustand')) {
    console.log('✅ 状态管理正确使用 Zustand');
  } else {
    console.log('❌ 状态管理未使用 Zustand');
  }
  
  if (content.includes('activeButton')) {
    console.log('✅ 状态管理包含 activeButton 状态');
  } else {
    console.log('❌ 状态管理缺少 activeButton 状态');
  }
}

console.log('\n📋 验证结果总结:');
if (allFilesExist) {
  console.log('✅ 所有必需的组件文件都已创建');
  console.log('🎉 容器组件构建完成！');
} else {
  console.log('❌ 部分文件缺失，请检查上述错误');
}

console.log('\n📝 下一步建议:');
console.log('1. 在 Next.js 项目中集成这些组件');
console.log('2. 创建页面文件来渲染主容器');
console.log('3. 添加样式和交互测试');
console.log('4. 验证按键切换功能是否正常工作');
